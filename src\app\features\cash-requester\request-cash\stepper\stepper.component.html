<div class="custom-container">

  <!-- Header + Progress Bar -->
  <div class="form-header">
    <h1 class="new-cash-request">{{ formTitles[selectedIndex] }}</h1>
    <p class="step-1-of-3">Step {{ selectedIndex + 1 }} of {{ steps.length }}</p>

    <div class="progress-indicator-base">
      <div 
        class="progress-bar" 
        [style.width.%]="getProgressPercent()">
      </div>
    </div>
  </div>

  <!-- Active Step Content -->
  <div *ngIf="selected">
    <ng-container class="step-content" [ngTemplateOutlet]="selected.content"></ng-container>
  </div>

  <!-- Your custom navigation buttons -->
  <div class="stepper-buttons">
    <button
      matButton="elevated"
      matStepperPrevious
      [disabled]="selectedIndex === 0"
      class="block-shape secondary"
      (click)="onClick(selectedIndex - 1)"
    >
      <span class="text-label">Back</span>
    </button>

    <button
      matButton="elevated"
      matStepperNext
      class="block-shape primary"
      (click)="onClick(selectedIndex + 1)"
      [disabled]="selectedIndex === steps.length - 1"
    >
      <span class="text-label">
        {{ selectedIndex === steps.length - 1 ? 'Submit Request' : 'Next' }}
      </span>
    </button>
  </div>

</div>

