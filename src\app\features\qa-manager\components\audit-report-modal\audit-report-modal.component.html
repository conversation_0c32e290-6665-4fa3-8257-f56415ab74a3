<div class="audit-report-modal">
  <!-- <PERSON><PERSON>er -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>assessment</mat-icon>
      </div>
      <div class="header-text">
        <h2 mat-dialog-title>Complete Inventory Audit Report</h2>
        <p class="header-subtitle" *ngIf="auditReport">
          Generated on {{ auditReport.generatedDate | date:'medium' }}
        </p>
      </div>
    </div>
    <button mat-icon-button class="close-btn" (click)="onClose()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-icon class="loading-icon">hourglass_empty</mat-icon>
    <p>Generating comprehensive audit report...</p>
  </div>

  <!-- Report Content -->
  <div class="modal-content" *ngIf="!isLoading && auditReport">

    <!-- Summary Cards -->
    <div class="summary-section">
      <h3 class="section-title">
        <mat-icon>dashboard</mat-icon>
        Executive Summary
      </h3>

      <div class="summary-grid">
        <mat-card class="summary-card total-value">
          <mat-card-header>
            <mat-icon mat-card-avatar>account_balance_wallet</mat-icon>
            <mat-card-title>Total Inventory Value</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ formatCurrency(auditReport.totalInventoryValue + auditReport.dyeStainedSummary.totalValue + auditReport.totalCoinValue) }}</div>
            <div class="metric-breakdown">
              <span>Cash: {{ formatCurrency(auditReport.totalInventoryValue) }}</span>
              <span>Dye-Stained: {{ formatCurrency(auditReport.dyeStainedSummary.totalValue) }}</span>
              <span>Coins: {{ formatCurrency(auditReport.totalCoinValue) }}</span>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="summary-card total-notes">
          <mat-card-header>
            <mat-icon mat-card-avatar>receipt</mat-icon>
            <mat-card-title>Total Notes</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ formatNumber(auditReport.totalNotes) }}</div>
            <div class="metric-subtitle">Individual banknotes</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="summary-card total-coins">
          <mat-card-header>
            <mat-icon mat-card-avatar>monetization_on</mat-icon>
            <mat-card-title>Total Coins</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ formatNumber(auditReport.totalCoins) }}</div>
            <div class="metric-subtitle">Individual coins</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="summary-card low-stock" [class.warning]="auditReport.lowStockItems.length > 0">
          <mat-card-header>
            <mat-icon mat-card-avatar>warning</mat-icon>
            <mat-card-title>Low Stock Alerts</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="metric-value">{{ auditReport.lowStockItems.length }}</div>
            <div class="metric-subtitle">Items requiring attention</div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <mat-divider></mat-divider>

    <!-- Detailed Breakdown Tabs -->
    <div class="details-section">
      <mat-tab-group class="report-tabs" animationDuration="300ms">

        <!-- Note Series Tab -->
        <mat-tab label="Note Series Breakdown">
          <ng-template mat-tab-label>
            <mat-icon>receipt_long</mat-icon>
            Note Series
          </ng-template>

          <div class="tab-content">
            <div class="series-breakdown" *ngFor="let series of auditReport.noteSeriesSummary">
              <mat-card class="series-card">
                <mat-card-header>
                  <mat-card-title>{{ series.seriesName }}</mat-card-title>
                  <mat-card-subtitle>
                    {{ formatNumber(series.totalNotes) }} notes • {{ formatCurrency(series.totalValue) }}
                  </mat-card-subtitle>
                </mat-card-header>

                <mat-card-content>
                  <div class="series-summary">
                    <div class="summary-item">
                      <span class="label">Total Batches:</span>
                      <span class="value">{{ formatNumber(series.totalBatches) }}</span>
                    </div>
                    <div class="summary-item">
                      <span class="label">Total Singles:</span>
                      <span class="value">{{ formatNumber(series.totalSingles) }}</span>
                    </div>
                  </div>

                  <table mat-table [dataSource]="series.denominations" class="denomination-table">
                    <ng-container matColumnDef="denomination">
                      <th mat-header-cell *matHeaderCellDef>Denomination</th>
                      <td mat-cell *matCellDef="let element">{{ element.denominationLabel }}</td>
                    </ng-container>

                    <ng-container matColumnDef="quantity">
                      <th mat-header-cell *matHeaderCellDef>Quantity</th>
                      <td mat-cell *matCellDef="let element">{{ formatNumber(element.quantity) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="batches">
                      <th mat-header-cell *matHeaderCellDef>Batches</th>
                      <td mat-cell *matCellDef="let element">{{ formatNumber(element.batches) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="singles">
                      <th mat-header-cell *matHeaderCellDef>Singles</th>
                      <td mat-cell *matCellDef="let element">{{ formatNumber(element.singles) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="value">
                      <th mat-header-cell *matHeaderCellDef>Value</th>
                      <td mat-cell *matCellDef="let element">{{ formatCurrency(element.value) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="stockLevel">
                      <th mat-header-cell *matHeaderCellDef>Stock Level</th>
                      <td mat-cell *matCellDef="let element">
                        <span class="stock-badge" [class]="element.stockLevel">
                          {{ element.stockLevel | titlecase }}
                        </span>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="denominationColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: denominationColumns;"></tr>
                  </table>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Coin Summary Tab -->
        <mat-tab label="Coin Summary">
          <ng-template mat-tab-label>
            <mat-icon>monetization_on</mat-icon>
            Coins
          </ng-template>

          <div class="tab-content">
            <table mat-table [dataSource]="auditReport.coinSummary" class="coins-table">
              <ng-container matColumnDef="denomination">
                <th mat-header-cell *matHeaderCellDef>Denomination</th>
                <td mat-cell *matCellDef="let element">{{ element.denominationLabel }}</td>
              </ng-container>

              <ng-container matColumnDef="totalQuantity">
                <th mat-header-cell *matHeaderCellDef>Total Quantity</th>
                <td mat-cell *matCellDef="let element">{{ formatNumber(element.totalQuantity) }}</td>
              </ng-container>

              <ng-container matColumnDef="totalBatches">
                <th mat-header-cell *matHeaderCellDef>Total Batches</th>
                <td mat-cell *matCellDef="let element">{{ formatNumber(element.totalBatches) }}</td>
              </ng-container>

              <ng-container matColumnDef="totalValue">
                <th mat-header-cell *matHeaderCellDef>Total Value</th>
                <td mat-cell *matCellDef="let element">{{ formatCurrency(element.totalValue) }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="coinColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: coinColumns;"></tr>
            </table>
          </div>
        </mat-tab>

        <!-- Low Stock Alerts Tab -->
        <mat-tab label="Low Stock Alerts" [disabled]="auditReport.lowStockItems.length === 0">
          <ng-template mat-tab-label>
            <mat-icon>warning</mat-icon>
            Low Stock ({{ auditReport.lowStockItems.length }})
          </ng-template>

          <div class="tab-content">
            <div class="alert-message" *ngIf="auditReport.lowStockItems.length === 0">
              <mat-icon>check_circle</mat-icon>
              <h4>No Low Stock Items</h4>
              <p>All inventory items are currently at acceptable stock levels.</p>
            </div>

            <table mat-table [dataSource]="auditReport.lowStockItems" class="low-stock-table" *ngIf="auditReport.lowStockItems.length > 0">
              <ng-container matColumnDef="type">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let element">
                  <span class="type-badge" [class]="element.type">
                    <mat-icon>{{ element.type === 'note' ? 'receipt' : 'monetization_on' }}</mat-icon>
                    {{ element.type | titlecase }}
                  </span>
                </td>
              </ng-container>

              <ng-container matColumnDef="series">
                <th mat-header-cell *matHeaderCellDef>Series</th>
                <td mat-cell *matCellDef="let element">{{ element.series }}</td>
              </ng-container>

              <ng-container matColumnDef="denomination">
                <th mat-header-cell *matHeaderCellDef>Denomination</th>
                <td mat-cell *matCellDef="let element">{{ element.denomination }}</td>
              </ng-container>

              <ng-container matColumnDef="currentQuantity">
                <th mat-header-cell *matHeaderCellDef>Current Quantity</th>
                <td mat-cell *matCellDef="let element">{{ formatNumber(element.currentQuantity) }}</td>
              </ng-container>

              <ng-container matColumnDef="stockLevel">
                <th mat-header-cell *matHeaderCellDef>Stock Level</th>
                <td mat-cell *matCellDef="let element">
                  <span class="stock-badge" [class]="element.stockLevel">
                    {{ element.stockLevel | titlecase }}
                  </span>
                </td>
              </ng-container>

              <ng-container matColumnDef="value">
                <th mat-header-cell *matHeaderCellDef>Value</th>
                <td mat-cell *matCellDef="let element">{{ formatCurrency(element.value) }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="lowStockColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: lowStockColumns;"></tr>
            </table>
          </div>
        </mat-tab>

        <!-- Overall Breakdown Tab -->
        <mat-tab label="Overall Breakdown">
          <ng-template mat-tab-label>
            <mat-icon>pie_chart</mat-icon>
            Overall
          </ng-template>

          <div class="tab-content">
            <table mat-table [dataSource]="auditReport.denominationBreakdown" class="breakdown-table">
              <ng-container matColumnDef="denomination">
                <th mat-header-cell *matHeaderCellDef>Denomination</th>
                <td mat-cell *matCellDef="let element">{{ element.denominationLabel }}</td>
              </ng-container>

              <ng-container matColumnDef="type">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let element">
                  <span class="type-badge" [class]="element.type">
                    <mat-icon>{{ element.type === 'note' ? 'receipt' : 'monetization_on' }}</mat-icon>
                    {{ element.type | titlecase }}
                  </span>
                </td>
              </ng-container>

              <ng-container matColumnDef="totalQuantity">
                <th mat-header-cell *matHeaderCellDef>Total Quantity</th>
                <td mat-cell *matCellDef="let element">{{ formatNumber(element.totalQuantity) }}</td>
              </ng-container>

              <ng-container matColumnDef="totalValue">
                <th mat-header-cell *matHeaderCellDef>Total Value</th>
                <td mat-cell *matCellDef="let element">{{ formatCurrency(element.totalValue) }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="['denomination', 'type', 'totalQuantity', 'totalValue']"></tr>
              <tr mat-row *matRowDef="let row; columns: ['denomination', 'type', 'totalQuantity', 'totalValue'];"></tr>
            </table>
          </div>
        </mat-tab>

      </mat-tab-group>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="modal-actions" *ngIf="!isLoading">
    <button mat-stroked-button class="cancel-btn" (click)="onClose()">
      <mat-icon>close</mat-icon>
      Close
    </button>
    <button mat-raised-button class="download-btn" (click)="onDownloadExcel()" [disabled]="!auditReport || isExporting">
      <mat-icon *ngIf="!isExporting">file_download</mat-icon>
      <mat-icon *ngIf="isExporting" class="spinning">hourglass_empty</mat-icon>
      {{ isExporting ? 'Generating Excel...' : 'Download Excel Report' }}
    </button>
  </div>
</div>
