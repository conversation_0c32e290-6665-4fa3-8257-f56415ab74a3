.unauthorized-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.unauthorized-card {
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 48px;
  color: #f44336;
  margin-right: 10px;
  vertical-align: middle;
}

mat-card-title {
  font-size: 24px;
  font-weight: 600;
  color: #4f5455;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message {
  font-size: 16px;
  color: #666;
  margin: 20px 0;
  line-height: 1.5;
}

mat-card-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 20px;

  button {
    min-width: 120px;

    mat-icon {
      margin-right: 8px;
    }
  }
}

@media (max-width: 600px) {
  mat-card-actions {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}
