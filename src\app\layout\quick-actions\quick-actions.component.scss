.container {
    // border: 5px solid #000;
    padding-top: 20px;
    margin:1%;
    background-color: var(--quick-actions-bg);
}

.header {
    padding-left: 20px;
    font-size: larger;
}

//I have to add a white background to it
hr {
    width: 97%;
    // color: var(--backgound-white);
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 20px;
  text-align: center;
  padding-bottom: 20px;
}

mat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 350px;
    min-height: 100px;
    cursor: pointer;
    background-color: var(--backgound-white);
    
}
mat-card-actions {
    margin-bottom: 0;
    padding-bottom: 0;
    align-items: end;
}

mat-card-header {
    justify-content: center;
    width: 100%;
    margin-top: 0 ;
    padding-top: 0;
}

mat-icon {
    font-size: 26px;
    color: var(--absa-red);
}

a {
    text-decoration: none;
}

// go shorta mobile responvisiveness
