// ===== RESPONSIVE BREAKPOINTS =====
$mobile-small: 320px;
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$desktop-large: 1200px;
$desktop-xl: 1440px;

// ===== RESPONSIVE MIXINS =====
@mixin mobile-small {
  @media (max-width: #{$mobile-small - 1px}) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: #{$tablet - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $desktop) {
    @content;
  }
}

@mixin desktop-large {
  @media (min-width: $desktop-large) {
    @content;
  }
}

// ===== DETAILED INVENTORY SECTION =====
.detailed-inventory-section {
  margin-top: 3rem;
  position: relative;
  z-index: 1;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 2rem 2.5rem;
    background: var(--absa-gradient-primary);
    border-radius: 16px 16px 0 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
      pointer-events: none;
    }

    .header-left {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      z-index: 1;
    }

    .header-center {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
    }

    .header-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      position: relative;
      z-index: 1;
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .section-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--absa-white);
      margin: 0;
    }

    // Base mode indicator styling
    .mode-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.375rem 0.75rem;
      border-radius: 8px;
      backdrop-filter: blur(10px);
      width: fit-content;
      border: 1px solid;
      transition: all 0.3s ease;

      .indicator-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }

      .indicator-text {
        font-size: 0.75rem;
        font-weight: 600;
        letter-spacing: 0.05em;
        text-transform: uppercase;
      }
    }

    // Normal Mode (Gray)
    .normal-mode-indicator {
      background: rgba(107, 114, 128, 0.9);
      border-color: #6b7280;
      animation: normalModePulse 2s ease-in-out infinite;

      .indicator-icon,
      .indicator-text {
        color: white;
      }
    }

    // Dye-Stained Mode (Purple)
    .dye-stained-indicator {
      background: rgba(139, 92, 246, 0.9);
      border-color: #8b5cf6;
      animation: dyeStainedPulse 2s ease-in-out infinite;

      .indicator-icon,
      .indicator-text {
        color: white;
      }
    }

    // Coins Mode (Gold)
    .coins-mode-indicator {
      background: rgba(212, 175, 55, 0.9);
      border-color: var(--absa-gold);
      animation: coinsPulse 2s ease-in-out infinite;

      .indicator-icon,
      .indicator-text {
        color: white;
      }
    }

    // Clean header styling
    .header-center {
      // Empty center space for layout balance
    }

    .export-audit-btn {
      background: rgba(255, 255, 255, 0.15);
      color: var(--absa-white);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      mat-icon {
        margin-right: 0.5rem;
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }

  .series-tabs-container {
    background: var(--absa-white);
    border-radius: 0 0 16px 16px;
    box-shadow: var(--absa-shadow-lg);
    overflow: visible;
    border: 1px solid var(--absa-gray-200);
    border-top: none;
    width: 100%;

    ::ng-deep {
      .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          border-bottom: 1px solid var(--absa-gray-200);
          background: var(--absa-gray-50);
          overflow: visible !important;

          // Hide pagination arrows completely
          .mat-mdc-tab-header-pagination,
          .mat-mdc-tab-header-pagination-before,
          .mat-mdc-tab-header-pagination-after {
            display: none !important;
          }

          // Disable pagination behavior
          .mat-mdc-tab-header-pagination-controls-enabled & {
            .mat-mdc-tab-header-pagination {
              display: none !important;
            }
          }

          // Ensure tabs container takes full width
          .mat-mdc-tab-label-container {
            flex: 1;
            overflow: visible !important;

            .mat-mdc-tab-labels {
              display: flex;
              width: 100%;
              justify-content: space-evenly;
            }

            .mat-mdc-tab {
              flex: 1;
              min-width: 140px;
              max-width: none;
              padding: 0 1.5rem;
              height: 60px;
              font-weight: 600;
              color: var(--absa-gray-600);
              transition: all 0.3s ease;
              position: relative;

              &:hover {
                background: rgba(196, 30, 58, 0.05);
                color: var(--absa-red);
              }

              &.mdc-tab--active {
                color: var(--absa-red);
                background: var(--absa-white);

                &::before {
                  content: '';
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  right: 0;
                  height: 3px;
                  background: var(--absa-gradient-primary);
                }
              }

              .mdc-tab__content {
                .mdc-tab__text-label {
                  font-size: 0.875rem;
                  font-weight: inherit;
                }
              }
            }
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              overflow: visible;
            }
          }
        }
      }
    }

    .tab-content {
      padding: 2.5rem;
      min-height: 600px;

      .series-summary {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        margin-bottom: 2.5rem;

        .summary-card {
          display: flex;
          align-items: center;
          padding: 1.75rem;
          background: linear-gradient(135deg, var(--absa-gray-50) 0%, var(--absa-white) 100%);
          border-radius: 16px;
          border: 1px solid var(--absa-gray-200);
          box-shadow: var(--absa-shadow-sm);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--absa-gradient-primary);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--absa-shadow-md);
            border-color: var(--absa-red-200);
          }

          .summary-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56px;
            height: 56px;
            background: var(--absa-gradient-primary);
            border-radius: 16px;
            margin-right: 1.25rem;
            flex-shrink: 0;
            box-shadow: var(--absa-shadow-sm);

            mat-icon {
              color: var(--absa-white);
              font-size: 1.5rem;
              width: 1.5rem;
              height: 1.5rem;
            }
          }

          .summary-content {
            flex: 1;

            .summary-value {
              font-size: 1.5rem;
              font-weight: 700;
              color: var(--absa-gray-900);
              margin: 0 0 0.25rem 0;
              line-height: 1.2;
            }

            .summary-label {
              font-size: 0.875rem;
              color: var(--absa-gray-600);
              margin: 0;
              font-weight: 500;
            }
          }

          &.value-card {
            &::before {
              background: var(--absa-gradient-gold);
            }

            .summary-icon {
              background: var(--absa-gradient-gold);
            }
          }
        }
      }

      // Specific styling for coin tab's Total Coin Value card
      // Target the batches-card that has a sibling singles-card (only in coin tab)
      .series-summary .summary-card.singles-card + .summary-card.batches-card {
        &::before {
          background: var(--absa-gold);
        }

        .summary-icon {
          background: var(--absa-gradient-gold);
        }
      }

      // ===== SERIES TOTALS SUMMARY =====
      .series-totals-summary {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;

        .total-card {
          display: flex;
          align-items: center;
          padding: 1.5rem;
          background: white;
          border-radius: 10px;
          border: 1px solid #e2e8f0;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }

          .total-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            border-radius: 10px;
            margin-right: 1rem;
            flex-shrink: 0;

            mat-icon {
              font-size: 1.5rem;
              width: 1.5rem;
              height: 1.5rem;
              color: white;
            }
          }

          .total-content {
            flex: 1;

            .total-value {
              font-size: 1.25rem;
              font-weight: 700;
              color: #1f2937;
              margin: 0 0 0.25rem 0;
              line-height: 1.2;
            }

            .total-label {
              font-size: 0.875rem;
              color: #6b7280;
              margin: 0;
              font-weight: 500;
            }
          }

          &.total-notes {
            .total-icon {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            }
          }

          &.total-value {
            .total-icon {
              background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
          }
        }

        // Special styling for fake notes series
        &.fake-notes-summary {
          background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
          border-color: #f59e0b;

          .total-card {
            border-color: #f59e0b;

            &.total-value {
              .total-icon {
                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
              }

              .total-content .total-label {
                color: #92400e;
              }
            }
          }
        }

        // Special styling for normal mode
        &.normal-mode-summary {
          background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
          border-color: #6b7280;

          .total-card {
            border-color: #6b7280;

            &.total-notes .total-icon {
              background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            }

            &.total-value .total-icon {
              background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            }
          }
        }

        // Special styling for dye-stained mode
        &.dye-stained-summary {
          background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
          border-color: #8b5cf6;

          .total-card {
            border-color: #8b5cf6;

            &.total-notes .total-icon {
              background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            }

            &.total-value .total-icon {
              background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            }
          }
        }

        // Special styling for coins mode
        &.coins-mode-summary {
          background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
          border-color: var(--absa-gold);

          .total-card {
            border-color: var(--absa-gold);

            &.total-notes .total-icon {
              background: linear-gradient(135deg, var(--absa-gold) 0%, #d97706 100%);
            }

            &.total-value .total-icon {
              background: linear-gradient(135deg, var(--absa-gold) 0%, #d97706 100%);
            }
          }
        }

        // Responsive design for totals
        @include tablet {
          grid-template-columns: 1fr;
          gap: 1rem;
          padding: 1rem;

          .total-card {
            padding: 1.25rem;

            .total-icon {
              width: 40px;
              height: 40px;

              mat-icon {
                font-size: 1.25rem;
                width: 1.25rem;
                height: 1.25rem;
              }
            }

            .total-content {
              .total-value {
                font-size: 1.125rem;
              }

              .total-label {
                font-size: 0.8rem;
              }
            }
          }
        }

        @include mobile {
          .total-card {
            padding: 1rem;

            .total-icon {
              width: 36px;
              height: 36px;
              margin-right: 0.75rem;

              mat-icon {
                font-size: 1.125rem;
                width: 1.125rem;
                height: 1.125rem;
              }
            }

            .total-content {
              .total-value {
                font-size: 1rem;
              }

              .total-label {
                font-size: 0.75rem;
              }
            }
          }
        }
      }

      // ===== TABLE LAYOUT =====
      .inventory-table-container {
        background: white;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);

        .inventory-table {
          width: 100%;
          border-collapse: collapse;

          thead {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;

            th {
              padding: 1rem;
              text-align: left;
              font-weight: 600;
              color: #374151;
              font-size: 0.875rem;
              text-transform: uppercase;
              letter-spacing: 0.05em;
              border-bottom: 1px solid #e2e8f0;

              &:first-child {
                padding-left: 1.5rem;
              }

              &:last-child {
                padding-right: 1.5rem;
                text-align: center;
              }
            }
          }

          tbody {
            tr {
              border-bottom: 1px solid #f1f5f9;
              transition: background-color 0.2s ease;

              &:hover {
                background: #f8fafc;
              }

              &.low-stock-row {
                background: rgba(245, 158, 11, 0.05);
                border-left: 4px solid #f59e0b;
              }

              &.out-of-stock-row {
                background: rgba(239, 68, 68, 0.05);
                border-left: 4px solid #ef4444;
              }

              &.dye-stained-row {
                background: rgba(139, 92, 246, 0.05);
                border-left: 4px solid #8b5cf6;
              }

              td {
                padding: 1rem;
                vertical-align: middle;
                color: #374151;

                &:first-child {
                  padding-left: 1.5rem;
                }

                &:last-child {
                  padding-right: 1.5rem;
                }
              }
            }
          }
        }

        // ===== CELL-SPECIFIC STYLING =====
        .denomination-cell {
          .denomination-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .denomination-value {
              font-weight: 600;
              font-size: 1rem;
              color: #1f2937;
            }

            .dye-indicator {
              display: flex;
              align-items: center;
              gap: 0.25rem;
              padding: 0.25rem 0.5rem;
              border-radius: 4px;
              font-size: 0.75rem;
              font-weight: 500;
              text-transform: uppercase;

              &.dye-red {
                background: rgba(239, 68, 68, 0.1);
                color: #dc2626;
              }

              &.dye-blue {
                background: rgba(59, 130, 246, 0.1);
                color: #2563eb;
              }

              &.dye-green {
                background: rgba(16, 185, 129, 0.1);
                color: #059669;
              }

              &.dye-purple {
                background: rgba(139, 92, 246, 0.1);
                color: #7c3aed;
              }

              &.dye-orange {
                background: rgba(245, 158, 11, 0.1);
                color: #d97706;
              }

              mat-icon {
                font-size: 0.875rem;
                width: 0.875rem;
                height: 0.875rem;
              }
            }
          }
        }

        .quantity-cell {
          font-weight: 500;
          color: #6b7280;
        }

        .value-cell {
          font-weight: 600;
          color: #059669;
          font-size: 1rem;
        }

        .status-cell {
          .status-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            min-width: 100px;
            width: 100px;
            height: 32px;
            text-align: center;
            transition: all 0.2s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            // In Stock - Green background
            &.in-stock,
            &.status-normal,
            &.status-good {
              background: #22c55e;
              color: white;
              border: 1px solid #16a34a;
            }

            // Medium Stock - Blue/Teal background
            &.medium-stock,
            &.status-medium {
              background: #06b6d4;
              color: white;
              border: 1px solid #0891b2;
            }

            // Low Stock - Gold/Orange background (unchanged)
            &.low-stock,
            &.status-low {
              background: #f59e0b;
              color: white;
              border: 1px solid #d97706;
            }

            // Out of Stock - Red background
            &.out-of-stock,
            &.status-out-of-stock {
              background: #ef4444;
              color: white;
              border: 1px solid #dc2626;
            }

            // Critical - Dark red background
            &.critical-stock,
            &.status-critical {
              background: #dc2626;
              color: white;
              border: 1px solid #b91c1c;
            }

            // Hover effects for better interactivity
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
              opacity: 0.9;
            }

            // Responsive adjustments
            @include tablet {
              padding: 0.375rem 0.75rem;
              font-size: 0.7rem;
              min-width: 85px;
              width: 85px;
              height: 28px;
            }

            @include mobile {
              padding: 0.25rem 0.5rem;
              font-size: 0.65rem;
              min-width: 70px;
              width: 70px;
              height: 24px;
              border-radius: 4px;
            }
          }
        }

        .actions-cell {
          text-align: center;

          .action-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;

            button {
              width: 36px;
              height: 36px;
              min-width: 36px;

              mat-icon {
                font-size: 1.25rem;
                width: 1.25rem;
                height: 1.25rem;
              }

              &.add-btn {
                color: #059669;

                &:hover {
                  background: rgba(16, 185, 129, 0.1);
                }
              }

              &.remove-btn {
                color: #dc2626;

                &:hover {
                  background: rgba(239, 68, 68, 0.1);
                }
              }

              &.edit-btn {
                color: #6b7280;

                &:hover {
                  background: rgba(107, 114, 128, 0.1);
                }
              }
            }
          }
        }

        // ===== RESPONSIVE TABLE =====
        @include tablet {
          .inventory-table {
            font-size: 0.875rem;

            thead th {
              padding: 0.75rem 0.5rem;
              font-size: 0.75rem;

              &:first-child {
                padding-left: 1rem;
              }

              &:last-child {
                padding-right: 1rem;
              }
            }

            tbody tr td {
              padding: 0.75rem 0.5rem;

              &:first-child {
                padding-left: 1rem;
              }

              &:last-child {
                padding-right: 1rem;
              }
            }

            .actions-cell .action-buttons button {
              width: 32px;
              height: 32px;
              min-width: 32px;

              mat-icon {
                font-size: 1rem;
                width: 1rem;
                height: 1rem;
              }
            }
          }
        }

        @include mobile {
          .inventory-table {
            thead {
              display: none;
            }

            tbody tr {
              display: block;
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              margin-bottom: 1rem;
              padding: 1rem;
              background: white;

              &.low-stock-row,
              &.out-of-stock-row,
              &.dye-stained-row {
                border-left: 4px solid;
              }

              td {
                display: block;
                padding: 0.5rem 0;
                border: none;

                &:before {
                  content: attr(data-label) ": ";
                  font-weight: 600;
                  color: #6b7280;
                  display: inline-block;
                  width: 100px;
                }

                &.actions-cell {
                  text-align: left;
                  margin-top: 1rem;
                  padding-top: 1rem;
                  border-top: 1px solid #f1f5f9;

                  &:before {
                    content: "";
                  }

                  .action-buttons {
                    justify-content: flex-start;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// ===== REDESIGNED COIN INVENTORY STYLES =====
.coin-denominations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.25rem;
  margin-top: 2rem;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .coin-card {
    background: var(--absa-white);
    border-radius: 16px;
    border: 1px solid var(--absa-gray-200);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border-color: var(--absa-red-300);
    }

    &.low-stock {
      border-color: var(--absa-warning);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--absa-warning);
        z-index: 1;
      }
    }

    &.out-of-stock {
      border-color: var(--absa-error);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--absa-error);
        z-index: 1;
      }

      .coin-header,
      .coin-metrics {
        opacity: 0.6;
      }
    }

    // Coin denomination color coding
    &[data-denomination="0.1"] .coin-badge {
      background: linear-gradient(135deg, #cd7f32 0%, #a0522d 100%);
      color: white;
    }

    &[data-denomination="0.2"] .coin-badge {
      background: linear-gradient(135deg, #cd7f32 0%, #a0522d 100%);
      color: white;
    }

    &[data-denomination="0.5"] .coin-badge {
      background: linear-gradient(135deg, #cd7f32 0%, #a0522d 100%);
      color: white;
    }

    &[data-denomination="1"] .coin-badge {
      background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
      color: #333;
    }

    &[data-denomination="2"] .coin-badge {
      background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
      color: #333;
    }

    &[data-denomination="5"] .coin-badge {
      background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
      color: #333;
    }
  }

  .coin-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--absa-gray-100);
    background: linear-gradient(135deg, var(--absa-gray-50) 0%, var(--absa-white) 100%);

    .coin-denomination {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .coin-badge {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        border-radius: 12px;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        .coin-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }

        .coin-value {
          font-size: 1.25rem;
          font-weight: 800;
          letter-spacing: -0.025em;
        }
      }

      .stock-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        .status-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }

        &.status-normal,
        &.status-in-stock {
          background: #22c55e;
          color: white;
          border: 1px solid #16a34a;
        }

        &.status-medium {
          background: #06b6d4;
          color: white;
          border: 1px solid #0891b2;
        }

        &.status-low {
          background: #f59e0b;
          color: white;
          border: 1px solid #d97706;
        }

        &.status-out-of-stock {
          background: #ef4444;
          color: white;
          border: 1px solid #dc2626;
        }

        &.status-critical {
          background: #dc2626;
          color: white;
          border: 1px solid #b91c1c;
        }
      }
    }
  }

  .coin-metrics {
    padding: 1.25rem;

    .metrics-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
      margin-bottom: 1rem;

      .metric-card {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--absa-gray-50);
        border: 1px solid var(--absa-gray-200);
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--absa-red-50);
          border-color: var(--absa-red-200);
          transform: translateY(-1px);
        }

        .metric-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: var(--absa-white);
          border-radius: 10px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
            color: var(--absa-gray-700);
          }
        }

        .metric-content {
          flex: 1;

          .metric-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--absa-gray-900);
            line-height: 1.2;
            margin-bottom: 0.125rem;
          }

          .metric-label {
            font-size: 0.625rem;
            color: var(--absa-gray-600);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }

        &.batches .metric-icon mat-icon {
          color: var(--absa-red);
        }

        &.quantity .metric-icon mat-icon {
          color: var(--absa-gray-700);
        }
      }
    }

    .total-value-card {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.25rem;
      background: linear-gradient(135deg, var(--absa-red-50) 0%, var(--absa-red-25) 100%);
      border: 1px solid var(--absa-red-200);
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, var(--absa-red-100) 0%, var(--absa-red-50) 100%);
        border-color: var(--absa-red-300);
      }

      .value-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: var(--absa-white);
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
          color: var(--absa-red);
        }
      }

      .value-content {
        flex: 1;

        .value-amount {
          font-size: 1.375rem;
          font-weight: 800;
          color: var(--absa-red);
          letter-spacing: -0.025em;
          line-height: 1.2;
          margin-bottom: 0.125rem;
        }

        .value-label {
          font-size: 0.75rem;
          color: var(--absa-red-700);
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  .coin-actions {
    padding: 1rem 1.25rem 1.25rem;
    border-top: 1px solid var(--absa-gray-100);
    background: var(--absa-gray-50);
    display: flex;
    gap: 0.75rem;

    .action-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      border: none;
      border-radius: 12px;
      padding: 0.875rem 1rem;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
      }

      &:active {
        transform: translateY(0);
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      span {
        font-weight: 600;
        letter-spacing: 0.025em;
      }

      &.add-btn {
        background: var(--absa-gradient-primary);
        color: var(--absa-white);

        &:hover {
          background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
      }

      &.remove-btn {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: var(--absa-white);

        &:hover {
          background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
        }
      }
    }
  }
}

// ===== RESPONSIVE DESIGN FOR DETAILED INVENTORY =====
@media (max-width: 1200px) {
  .detailed-inventory-section {
    .section-header {
      padding: 1.5rem 2rem;
      flex-direction: column;
      gap: 1rem;
      text-align: center;

      .section-title {
        font-size: 1.5rem;
      }
    }

    .series-tabs-container .tab-content {
      padding: 2rem;

      .series-summary {
        gap: 1rem;
      }

      .denominations-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      }
    }
  }
}

@media (max-width: 768px) {
  .detailed-inventory-section {
    margin-top: 2rem;

    .section-header {
      padding: 1.25rem 1.5rem;
      border-radius: 12px 12px 0 0;

      .section-title {
        font-size: 1.25rem;
      }

      .export-audit-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;

        mat-icon {
          font-size: 0.875rem;
          width: 0.875rem;
          height: 0.875rem;
        }
      }
    }

    .series-tabs-container {
      border-radius: 0 0 12px 12px;

      ::ng-deep .mat-mdc-tab-group .mat-mdc-tab-header .mat-mdc-tab-label-container .mat-mdc-tab {
        min-width: 100px;
        padding: 0 1rem;
        height: 50px;

        .mdc-tab__content .mdc-tab__text-label {
          font-size: 0.8rem;
        }
      }

      .tab-content {
        padding: 1.5rem;

        .series-summary {
          grid-template-columns: 1fr;
          gap: 1rem;
          margin-bottom: 1.5rem;

          .summary-card {
            padding: 1.25rem;

            .summary-icon {
              width: 48px;
              height: 48px;
              margin-right: 1rem;

              mat-icon {
                font-size: 1.25rem;
                width: 1.25rem;
                height: 1.25rem;
              }
            }

            .summary-content .summary-value {
              font-size: 1.25rem;
            }
          }
        }

        .denominations-grid {
          .denomination-card {
            .card-header {
              padding: 1.25rem;

              .denomination-info {
                .denomination-icon {
                  width: 40px;
                  height: 40px;

                  mat-icon {
                    font-size: 1rem;
                    width: 1rem;
                    height: 1rem;
                  }
                }

                .denomination-details .denomination-title {
                  font-size: 1.25rem;
                }
              }
            }

            .card-body {
              padding: 1.25rem;

              .inventory-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
                margin-bottom: 1.25rem;

                .stat-item {
                  padding: 1rem;

                  .stat-value {
                    font-size: 1rem;
                  }
                }
              }
            }

            .card-actions {
              padding: 1rem 1.25rem;

              .add-cash-btn {
                padding: 0.75rem 1.25rem;
                font-size: 0.8rem;
              }
            }
          }
        }
      }
    }
  }
}

// ===== ANIMATIONS =====
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

// ===== MODE INDICATOR ANIMATIONS =====
@keyframes normalModePulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(107, 114, 128, 0.3);
    border-color: #6b7280;
  }
  50% {
    box-shadow: 0 0 16px rgba(107, 114, 128, 0.6);
    border-color: #9ca3af;
  }
}

@keyframes dyeStainedPulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
    border-color: #8b5cf6;
  }
  50% {
    box-shadow: 0 0 16px rgba(139, 92, 246, 0.6);
    border-color: #a78bfa;
  }
}

@keyframes coinsPulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(212, 175, 55, 0.3);
    border-color: var(--absa-gold);
  }
  50% {
    box-shadow: 0 0 16px rgba(212, 175, 55, 0.6);
    border-color: var(--absa-gold-light);
  }
}

// ===== PAINT STAIN EFFECTS =====
.denomination-card {
  &.dye-stained {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// ===== PAINT STAIN CONTAINER =====
.paint-stain-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
  opacity: 0.8;
  transition: opacity 0.4s ease;

  // Main Paint Stain - Large irregular blob
  .paint-stain.main-stain {
    position: absolute;
    top: 15%;
    right: 10%;
    width: 45px;
    height: 35px;
    background: var(--paint-color, #e74c3c);
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    opacity: 0.7;
    transform: rotate(-15deg);
    filter: blur(0.5px);
    animation: paintPulse 4s ease-in-out infinite;

    &::before {
      content: '';
      position: absolute;
      top: -5px;
      left: -8px;
      width: 25px;
      height: 20px;
      background: inherit;
      border-radius: 50% 30% 70% 40%;
      opacity: 0.6;
      transform: rotate(25deg);
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -3px;
      right: -6px;
      width: 18px;
      height: 15px;
      background: inherit;
      border-radius: 40% 60% 30% 70%;
      opacity: 0.5;
      transform: rotate(-30deg);
    }
  }

  // Paint Splatters - Small irregular drops
  .paint-splatter {
    position: absolute;
    background: var(--paint-color, #e74c3c);
    border-radius: 50%;
    opacity: 0.6;
    filter: blur(0.3px);

    &.splatter-1 {
      top: 8%;
      right: 25%;
      width: 8px;
      height: 6px;
      border-radius: 60% 40% 50% 50%;
      transform: rotate(45deg);
      animation: splatterFloat 3s ease-in-out infinite 0.5s;
    }

    &.splatter-2 {
      top: 35%;
      right: 5%;
      width: 5px;
      height: 5px;
      animation: splatterFloat 3.5s ease-in-out infinite 1s;
    }

    &.splatter-3 {
      top: 25%;
      right: 35%;
      width: 4px;
      height: 7px;
      border-radius: 50% 30% 70% 50%;
      transform: rotate(-20deg);
      animation: splatterFloat 4s ease-in-out infinite 1.5s;
    }

    &.splatter-4 {
      top: 45%;
      right: 15%;
      width: 6px;
      height: 4px;
      border-radius: 70% 30% 50% 50%;
      transform: rotate(60deg);
      animation: splatterFloat 3.2s ease-in-out infinite 0.8s;
    }
  }

  // Paint Drips - Vertical streaks
  .paint-drip {
    position: absolute;
    background: linear-gradient(to bottom, var(--paint-color, #e74c3c), transparent);
    opacity: 0.5;
    filter: blur(0.2px);

    &.drip-1 {
      top: 40%;
      right: 20%;
      width: 3px;
      height: 25px;
      border-radius: 50% 50% 0 0;
      animation: dripFlow 5s ease-in-out infinite;
    }

    &.drip-2 {
      top: 30%;
      right: 8%;
      width: 2px;
      height: 35px;
      border-radius: 50% 50% 0 0;
      animation: dripFlow 6s ease-in-out infinite 1s;
    }
  }

  // Paint Smear - Brush stroke effect
  .paint-smear {
    position: absolute;
    top: 60%;
    right: 15%;
    width: 30px;
    height: 8px;
    background: linear-gradient(90deg, transparent, var(--paint-color, #e74c3c), transparent);
    border-radius: 50%;
    opacity: 0.4;
    transform: rotate(-10deg);
    filter: blur(1px);
    animation: smearShimmer 4s ease-in-out infinite 2s;
  }

  // Stain Label - Top Right Corner
  .stain-label {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    gap: 3px;
    padding: 3px 8px;
    background: rgba(0, 0, 0, 0.85);
    border-radius: 12px;
    font-size: 9px;
    font-weight: 700;
    color: var(--absa-white);
    letter-spacing: 0.3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 10;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: scale(1.05);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    mat-icon {
      font-size: 11px;
      width: 11px;
      height: 11px;
      color: var(--paint-color, #e74c3c);
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }

    span {
      font-size: 8px;
      font-weight: 800;
      letter-spacing: 0.5px;
    }
  }
}

// ===== PAINT COLOR VARIATIONS =====
.paint-stain-container {
  &.stain-red {
    --paint-color: #dc2626;
  }

  &.stain-blue {
    --paint-color: #2563eb;
  }

  &.stain-green {
    --paint-color: #16a34a;
  }

  &.stain-purple {
    --paint-color: #9333ea;
  }

  &.stain-orange {
    --paint-color: #ea580c;
  }

  &.stain-teal {
    --paint-color: #0d9488;
  }

  &.stain-pink {
    --paint-color: #db2777;
  }

  &.stain-amber {
    --paint-color: #d97706;
  }
}

// ===== PAINT STAIN ANIMATIONS =====
@keyframes paintPulse {
  0%, 100% {
    transform: rotate(-15deg) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: rotate(-12deg) scale(1.05);
    opacity: 0.8;
  }
}

@keyframes splatterFloat {
  0%, 100% {
    transform: translateY(0) rotate(var(--rotation, 0deg));
    opacity: 0.6;
  }
  50% {
    transform: translateY(-2px) rotate(calc(var(--rotation, 0deg) + 5deg));
    opacity: 0.8;
  }
}

@keyframes dripFlow {
  0% {
    height: 15px;
    opacity: 0.5;
  }
  50% {
    height: 30px;
    opacity: 0.7;
  }
  100% {
    height: 25px;
    opacity: 0.5;
  }
}

@keyframes smearShimmer {
  0%, 100% {
    opacity: 0.4;
    filter: blur(1px);
  }
  50% {
    opacity: 0.6;
    filter: blur(0.5px);
  }
}

// ===== RESPONSIVE PAINT STAINS =====
@include tablet {
  .paint-stain-container {
    .paint-stain.main-stain {
      width: 35px;
      height: 28px;
    }

    .paint-splatter {
      &.splatter-1 {
        width: 6px;
        height: 5px;
      }

      &.splatter-2,
      &.splatter-3,
      &.splatter-4 {
        width: 4px;
        height: 4px;
      }
    }

    .paint-drip {
      &.drip-1 {
        width: 2px;
        height: 20px;
      }

      &.drip-2 {
        width: 2px;
        height: 25px;
      }
    }

    .paint-smear {
      width: 25px;
      height: 6px;
    }

    .stain-label {
      top: 6px;
      right: 6px;
      padding: 2px 6px;
      border-radius: 10px;

      mat-icon {
        font-size: 9px;
        width: 9px;
        height: 9px;
      }

      span {
        font-size: 7px;
      }
    }
  }
}

@include mobile {
  .paint-stain-container {
    .paint-stain.main-stain {
      width: 28px;
      height: 22px;
    }

    .paint-splatter {
      &.splatter-1 {
        width: 5px;
        height: 4px;
      }

      &.splatter-2,
      &.splatter-3,
      &.splatter-4 {
        width: 3px;
        height: 3px;
      }
    }

    .paint-drip {
      &.drip-1 {
        width: 2px;
        height: 15px;
      }

      &.drip-2 {
        width: 1px;
        height: 20px;
      }
    }

    .paint-smear {
      width: 20px;
      height: 5px;
    }

    .stain-label {
      top: 4px;
      right: 4px;
      padding: 2px 5px;
      border-radius: 8px;
      gap: 2px;

      mat-icon {
        font-size: 8px;
        width: 8px;
        height: 8px;
      }

      span {
        font-size: 6px;
      }
    }
  }
}

// ===== GOLD PULSE ANIMATION =====
@keyframes goldPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 12px rgba(212, 175, 55, 0.6);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.9);
  }
}

// ===== RESPONSIVE DESIGN FOR HEADER =====
@include tablet {
  .detailed-inventory-section .section-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem 2rem;

    .header-left,
    .header-right {
      justify-content: center;
    }

    .header-center {
      position: static;
      transform: none;
      order: 2;
    }

    .header-right {
      order: 3;
    }
  }
}

@include mobile {
  .detailed-inventory-section .section-header {
    padding: 1rem 1.5rem;
    gap: 1rem;

    .header-right {
      .export-audit-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
      }
    }
  }
}
