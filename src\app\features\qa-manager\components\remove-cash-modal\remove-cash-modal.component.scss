// Remove this duplicate - will be handled in the global styles section

// Make the Material dialog container transparent
:host ::ng-deep .add-cash-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

:host ::ng-deep .modern-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

// Modern Container with Better Colors and Transparency
.remove-cash-modal-container {
  width: 100%; // Use full available width from service configuration
  max-width: 100%; // Remove max-width constraint
  max-height: 90vh;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.85) 0%,
    rgba(241, 245, 249, 0.90) 50%,
    rgba(236, 242, 248, 0.92) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 32px 64px rgba(15, 23, 42, 0.15),
    0 16px 32px rgba(15, 23, 42, 0.10),
    0 8px 16px rgba(15, 23, 42, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.8);
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // Dye Stained Mode Styling
  &.dye-stained-mode {
    background: linear-gradient(135deg,
      rgba(248, 250, 252, 0.75) 0%,
      rgba(241, 245, 249, 0.80) 50%,
      rgba(236, 242, 248, 0.85) 100%);
    border: 2px solid rgba(212, 175, 55, 0.4);
    box-shadow:
      0 32px 64px rgba(15, 23, 42, 0.15),
      0 16px 32px rgba(15, 23, 42, 0.10),
      0 8px 16px rgba(15, 23, 42, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.6),
      0 0 0 1px rgba(212, 175, 55, 0.2);

    // Keep the original Absa header styling - don't override it
    // .modal-header maintains its original reddish Absa colors
  }
}

// Gradient Header
.modal-header {
  background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 1;
    position: relative;

    .header-icon {
      width: 64px;
      height: 64px;
      background: rgba(255,255,255,0.2);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.3);

      mat-icon {
        font-size: 2rem;
        color: white;
      }
    }

    .header-text {
      h2 {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }

      p {
        color: rgba(255,255,255,0.9);
        margin: 0.5rem 0 0 0;
        font-size: 1rem;
        font-weight: 400;
      }
    }
  }

  .close-button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
    z-index: 1;
    position: relative;

    &:hover {
      background: rgba(255,255,255,0.3);
      transform: scale(1.05);
    }
  }
}

// Main Content Area
.modal-content {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.6) transparent;
  background: rgba(255, 255, 255, 0.4);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.6);
    border-radius: 3px;

    &:hover {
      background: rgba(148, 163, 184, 0.8);
    }
  }
}

// Form Sections
.form-section {
  margin-bottom: 2.5rem;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;

    .step-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
      color: white;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    h3 {
      color: var(--absa-dark-blue, #1e293b);
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
      flex: 1;
    }

    .pre-selected-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
      height: 40px; // Match the step-icon height for perfect alignment
      min-width: fit-content;

      mat-icon {
        font-size: 1rem;
        line-height: 1;
        width: 1rem;
        height: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// Pre-selected Information Display
.pre-selected-info {
  .pre-selected-display {
    .selected-item-card {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 2px solid #0ea5e9;
      border-radius: 16px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      box-shadow:
        0 8px 25px -5px rgba(14, 165, 233, 0.25),
        0 4px 10px -5px rgba(14, 165, 233, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);

      .item-icon {
        width: 56px;
        height: 56px;
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        color: white;
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
        flex-shrink: 0;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }

      .item-info {
        flex: 1;

        h4 {
          color: #0f172a;
          font-size: 1.125rem;
          font-weight: 600;
          margin: 0 0 0.25rem 0;
        }

        .denomination-info {
          color: #0ea5e9;
          font-size: 1rem;
          font-weight: 500;
          margin: 0 0 0.25rem 0;
        }

        .series-description {
          color: #64748b;
          font-size: 0.875rem;
          margin: 0;
        }
      }

      .confirmation-badge {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        flex-shrink: 0;

        mat-icon {
          font-size: 1.25rem;
          width: 1.25rem;
          height: 1.25rem;
        }
      }
    }
  }
}

// Series Selection Grid
.series-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;

  .series-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);

    &:hover {
      transform: translateY(-4px);
      box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
      border-color: var(--absa-red);
      background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
    }

    &.selected {
      border-color: var(--absa-red);
      background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
      box-shadow:
        0 20px 25px -5px rgba(220, 38, 38, 0.25),
        0 10px 10px -5px rgba(220, 38, 38, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);

      .series-icon {
        background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
        color: white;
        transform: scale(1.05);

        mat-icon {
          color: white !important;
        }
      }
    }

    .series-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border: 1px solid #cbd5e1;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      transition: all 0.3s ease;
      box-shadow:
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 1px 2px -1px rgba(0, 0, 0, 0.1);

      mat-icon {
        font-size: 1.5rem;
        color: var(--absa-dark-blue, #1e293b);
      }
    }

    .series-info {
      h4 {
        color: var(--absa-dark-blue, #1e293b);
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
      }

      p {
        color: var(--absa-gray-medium, #64748b);
        font-size: 0.875rem;
        margin: 0;
        line-height: 1.4;
      }
    }

    .selection-indicator {
      position: absolute;
      top: 1rem;
      right: 1rem;
      color: var(--absa-red);
      animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      mat-icon {
        font-size: 1.25rem;
      }
    }
  }
}

// Denomination Selection Grid
.denomination-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;

  .denomination-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.25rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    text-align: center;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
      border-color: var(--absa-red);
      background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
    }

    &.selected {
      border-color: var(--absa-red);
      background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
      box-shadow:
        0 10px 15px -3px rgba(220, 38, 38, 0.25),
        0 4px 6px -2px rgba(220, 38, 38, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);

      .denomination-content .denomination-icon {
        background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
        border-color: var(--absa-red);
        color: white;
        transform: scale(1.05);

        mat-icon {
          color: white !important;
        }
      }
    }

    .denomination-content {
      .denomination-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        border: 1px solid #cbd5e1;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem auto;
        transition: all 0.3s ease;
        box-shadow:
          0 2px 4px -1px rgba(0, 0, 0, 0.06),
          0 1px 2px -1px rgba(0, 0, 0, 0.1);

        mat-icon {
          font-size: 1.25rem;
          color: var(--absa-dark-blue, #1e293b);
        }
      }

      .denomination-info {
        h4 {
          color: var(--absa-dark-blue, #1e293b);
          font-size: 1.1rem;
          font-weight: 600;
          margin: 0 0 0.25rem 0;
        }

        p {
          color: var(--absa-gray-medium, #64748b);
          font-size: 0.8rem;
          margin: 0;
        }
      }
    }

    .selection-indicator {
      position: absolute;
      top: 0.75rem;
      right: 0.75rem;
      color: var(--absa-red);
      animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      mat-icon {
        font-size: 1rem;
      }
    }
  }
}

// Quantity Controls
.quantity-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;

  .quantity-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);

    &:hover {
      border-color: var(--absa-red);
      box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
      background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;

      mat-icon {
        color: var(--absa-dark-blue);
        font-size: 1.25rem;
      }

      h4 {
        color: var(--absa-dark-blue);
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        flex: 1;
      }

      .helper-text {
        color: var(--absa-gray-medium);
        font-size: 0.8rem;
        font-weight: 400;
      }
    }

    .input-container {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;

      .quantity-btn {
        width: 40px;
        height: 40px;
        min-width: 40px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        color: var(--absa-dark-blue);
        border-radius: 8px;
        box-shadow:
          0 2px 4px -1px rgba(0, 0, 0, 0.06),
          0 1px 2px -1px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
          color: white;
          border-color: var(--absa-red);
          transform: scale(1.05);
          box-shadow:
            0 4px 6px -1px rgba(220, 38, 38, 0.25),
            0 2px 4px -1px rgba(220, 38, 38, 0.1);
        }

        &:disabled {
          opacity: 0.4;
          cursor: not-allowed;
          background: #f1f5f9;
          border-color: #e2e8f0;
          color: #94a3b8;
        }

        mat-icon {
          font-size: 1.1rem;
          font-weight: 600;
        }
      }

      .quantity-input {
        flex: 1;
        text-align: center;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--absa-dark-blue);
        border: 2px solid rgba(226, 232, 240, 0.8);
        border-radius: 8px;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--absa-light-blue);
          box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
        }
      }
    }

    .quantity-display {
      text-align: center;

      .notes-count {
        color: var(--absa-gray-dark);
        font-size: 0.9rem;
        font-weight: 500;
      }
    }
  }
}

// Reason Field
.reason-field {
  width: 100%;

  mat-label {
    color: var(--absa-dark-blue);
    font-weight: 500;
  }

  textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
  }

  .mat-form-field-outline {
    color: #E5E7EB;
  }

  &.mat-focused .mat-form-field-outline-thick {
    color: var(--absa-red);
  }
}

// Enhanced Summary Display
.summary-display {
  margin-top: 2rem;

  .summary-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 20px;
    padding: 0;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg,
        #f59e0b 0%,
        #d97706 50%,
        #f59e0b 100%);
    }

    // Summary Header
    .summary-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1.5rem 2rem;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      border-bottom: 1px solid #fbbf24;

      .header-content {
        display: flex;
        align-items: center;
        gap: 1rem;

        .header-icon {
          width: 56px;
          height: 56px;
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow:
            0 10px 15px -3px rgba(245, 158, 11, 0.4),
            0 4px 6px -2px rgba(245, 158, 11, 0.1);

          mat-icon {
            color: white;
            font-size: 1.75rem;
            font-weight: 600;
          }
        }

        .header-text {
          h3 {
            margin: 0 0 0.25rem 0;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--absa-dark-blue);
            letter-spacing: -0.025em;
          }

          .series-info {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
          }
        }
      }

      .status-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
        box-shadow:
          0 4px 6px -1px rgba(245, 158, 11, 0.4),
          0 2px 4px -1px rgba(245, 158, 11, 0.1);

        &.warning {
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          box-shadow:
            0 4px 6px -1px rgba(239, 68, 68, 0.4),
            0 2px 4px -1px rgba(239, 68, 68, 0.1);
        }

        mat-icon {
          font-size: 1rem;
        }
      }
    }

    // Metrics Grid
    .metrics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      padding: 2rem;

      .metric-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
          transform: translateY(-2px);
          box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        &.quantity-card {
          border-color: #ef4444;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
          }

          .metric-icon {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          }
        }

        &.remaining-card {
          border-color: #10b981;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
          }

          .metric-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          }
        }

        .metric-header {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          margin-bottom: 1rem;

          .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
              0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);

            mat-icon {
              color: white;
              font-size: 1.25rem;
              font-weight: 600;
            }
          }

          .metric-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }
        }

        .metric-value {
          font-size: 2rem;
          font-weight: 800;
          color: var(--absa-dark-blue);
          line-height: 1;
          margin-bottom: 0.25rem;
        }

        .metric-subtitle {
          font-size: 0.875rem;
          color: #9ca3af;
          font-weight: 500;
          text-transform: lowercase;
        }
      }
    }

    // Value Summary
    .value-summary {
      padding: 1.5rem 2rem;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-top: 1px solid #e2e8f0;
      border-bottom: 1px solid #e2e8f0;

      .value-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;

        &:not(:last-child) {
          border-bottom: 1px solid #e2e8f0;
        }

        .value-label {
          font-size: 0.95rem;
          font-weight: 600;
          color: #6b7280;
        }

        .value-amount {
          font-size: 1.1rem;
          font-weight: 700;
        }

        &.removing .value-amount {
          color: #ef4444;
        }

        &.remaining .value-amount {
          color: #10b981;
        }
      }
    }
    // Breakdown Section
    .breakdown-section {
      border-top: 2px solid #f1f5f9;
      padding: 1.5rem 2rem 2rem;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

      .breakdown-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;

        mat-icon {
          font-size: 1.25rem;
          color: #f59e0b;
        }

        span {
          font-size: 1rem;
          font-weight: 700;
          color: var(--absa-dark-blue);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }

      .breakdown-grid {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .breakdown-item {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem 1.5rem;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          border: 2px solid #e2e8f0;
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateX(4px);
            border-color: #f59e0b;
            box-shadow:
              0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }

          .breakdown-icon {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border: 1px solid #cbd5e1;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            mat-icon {
              font-size: 1.25rem;
              color: var(--absa-dark-blue);
            }
          }

          .breakdown-content {
            flex: 1;

            .breakdown-title {
              font-size: 0.95rem;
              font-weight: 600;
              color: var(--absa-dark-blue);
              margin: 0 0 0.25rem 0;
            }

            .breakdown-subtitle {
              font-size: 0.8rem;
              color: #6b7280;
              font-weight: 500;
              margin: 0;
            }
          }

          .breakdown-value {
            font-size: 1rem;
            font-weight: 700;
            color: #ef4444;
            text-align: right;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

// Enhanced Current Inventory Section
.current-inventory-section {
  margin-top: 2rem;

  .inventory-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 20px;
    padding: 0;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg,
        #10b981 0%,
        #059669 50%,
        #10b981 100%);
    }

    // Inventory Header
    .inventory-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1.5rem 2rem;
      background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
      border-bottom: 1px solid #a7f3d0;

      .header-content {
        display: flex;
        align-items: center;
        gap: 1rem;

        .header-icon {
          width: 56px;
          height: 56px;
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow:
            0 10px 15px -3px rgba(16, 185, 129, 0.4),
            0 4px 6px -2px rgba(16, 185, 129, 0.1);

          mat-icon {
            color: white;
            font-size: 1.75rem;
            font-weight: 600;
          }
        }

        .header-text {
          h3 {
            margin: 0 0 0.25rem 0;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--absa-dark-blue);
            letter-spacing: -0.025em;
          }

          .series-info {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
          }
        }
      }

      .status-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
        box-shadow:
          0 4px 6px -1px rgba(16, 185, 129, 0.4),
          0 2px 4px -1px rgba(16, 185, 129, 0.1);

        &.available {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        mat-icon {
          font-size: 1rem;
        }
      }
    }

    // Inventory Metrics Grid
    .inventory-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      padding: 2rem;

      .metric-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
          transform: translateY(-2px);
          box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        &.primary {
          border-color: #3b82f6;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
          }

          .metric-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          }
        }

        &.value {
          border-color: #10b981;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
          }

          .metric-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          }
        }

        .metric-header {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          margin-bottom: 1rem;

          .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
              0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);

            mat-icon {
              color: white;
              font-size: 1.25rem;
              font-weight: 600;
            }
          }

          .metric-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }
        }

        .metric-value {
          font-size: 2rem;
          font-weight: 800;
          color: var(--absa-dark-blue);
          line-height: 1;
          margin-bottom: 0.25rem;
        }

        .metric-subtitle {
          font-size: 0.875rem;
          color: #9ca3af;
          font-weight: 500;
          text-transform: lowercase;
        }
      }
    }

    // Inventory Breakdown
    .inventory-breakdown {
      border-top: 2px solid #f1f5f9;
      padding: 1.5rem 2rem 2rem;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

      .breakdown-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;

        mat-icon {
          font-size: 1.25rem;
          color: #10b981;
        }

        span {
          font-size: 1rem;
          font-weight: 700;
          color: var(--absa-dark-blue);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }

      .breakdown-grid {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .breakdown-item {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem 1.5rem;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          border: 2px solid #e2e8f0;
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateX(4px);
            border-color: #10b981;
            box-shadow:
              0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }

          .breakdown-icon {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border: 1px solid #cbd5e1;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            mat-icon {
              font-size: 1.25rem;
              color: var(--absa-dark-blue);
            }
          }

          .breakdown-content {
            flex: 1;

            .breakdown-title {
              font-size: 0.95rem;
              font-weight: 600;
              color: var(--absa-dark-blue);
              margin: 0 0 0.25rem 0;
            }

            .breakdown-subtitle {
              font-size: 0.8rem;
              color: #6b7280;
              font-weight: 500;
              margin: 0;
            }
          }

          .breakdown-value {
            font-size: 1rem;
            font-weight: 700;
            color: #10b981;
            text-align: right;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

// ===== NO INVENTORY SECTION =====
.no-inventory-section {
  padding: 2rem 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;

  .no-inventory-card {
    background: linear-gradient(135deg,
      rgba(248, 250, 252, 0.95) 0%,
      rgba(241, 245, 249, 0.98) 100%);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    max-width: 400px;
    width: 100%;
    box-shadow:
      0 8px 32px rgba(15, 23, 42, 0.08),
      0 4px 16px rgba(15, 23, 42, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 12px 40px rgba(15, 23, 42, 0.12),
        0 6px 20px rgba(15, 23, 42, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #94a3b8;
      margin-bottom: 1rem;
      display: block;
      margin-left: auto;
      margin-right: auto;
    }

    h3 {
      font-size: 1.25rem;
      font-weight: 700;
      color: #334155;
      margin: 0 0 0.75rem 0;
      letter-spacing: -0.025em;
    }

    p {
      font-size: 0.95rem;
      color: #64748b;
      margin: 0;
      line-height: 1.6;
      font-weight: 500;
    }
  }
}

// Modal Actions
.modal-actions {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;

  .cancel-btn {
    border: 2px solid var(--absa-gray-medium);
    color: var(--absa-gray-dark);
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--absa-red);
      color: var(--absa-red);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(227, 24, 55, 0.2);
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }



  .add-btn {
    background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(227, 24, 55, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(227, 24, 55, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Responsive Design - Much smaller content for mobile
@media (max-width: 768px) {
  .add-cash-modal-container {
    width: 100%; // Use full available width
    max-height: 90vh;
    transform: scale(0.9);
  }

  .modal-header {
    padding: 0.75rem;

    .header-content {
      gap: 0.5rem;

      .header-icon {
        width: 28px;
        height: 28px;

        mat-icon {
          font-size: 0.875rem;
        }
      }

      .header-text h2 {
        font-size: 1rem;
      }

      .header-text p {
        font-size: 0.625rem;
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .series-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .series-card {
    padding: 0.5rem;
    border-radius: 6px;

    .series-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.875rem;
      }
    }

    .series-info h4 {
      font-size: 0.625rem;
      margin-bottom: 0.125rem;
    }

    .series-info p {
      font-size: 0.5rem;
    }
  }

  .denomination-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.375rem;
  }

  .denomination-card {
    padding: 0.375rem;
    border-radius: 4px;

    .denomination-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.75rem;
      }
    }

    .denomination-info h4 {
      font-size: 0.5rem;
      margin-bottom: 0.125rem;
    }

    .denomination-info p {
      font-size: 0.375rem;
    }
  }

  .quantity-controls {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .quantity-input-group {
    .quantity-label {
      font-size: 0.625rem;
      margin-bottom: 0.25rem;
    }

    .quantity-input {
      padding: 0.375rem;
      font-size: 0.75rem;
      border-radius: 4px;
    }
  }

  .no-inventory-section {
    padding: 1rem 0.75rem;
    min-height: 150px;

    .no-inventory-card {
      padding: 1.5rem;
      border-radius: 12px;

      mat-icon {
        font-size: 2.5rem;
        width: 2.5rem;
        height: 2.5rem;
        margin-bottom: 0.75rem;
      }

      h3 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }

  .modal-actions {
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    gap: 0.375rem;

    .cancel-btn,
    .add-btn {
      width: 100%;
      justify-content: center;
      padding: 0.5rem;
      font-size: 0.625rem;
      border-radius: 4px;
    }
  }
}

@media (max-width: 480px) {
  .add-cash-modal-container {
    width: 100%; // Use full available width
    max-height: 100vh;
    border-radius: 0;
    transform: scale(0.85);
  }

  .modal-header {
    padding: 0.5rem;

    .header-content {
      gap: 0.25rem;

      .header-icon {
        width: 24px;
        height: 24px;

        mat-icon {
          font-size: 0.75rem;
        }
      }

      .header-text h2 {
        font-size: 0.875rem;
      }

      .header-text p {
        font-size: 0.5rem;
      }
    }
  }

  .modal-content {
    padding: 0.5rem;
  }

  .series-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.25rem;
  }

  .series-card {
    padding: 0.25rem;

    .series-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 0.125rem;

      mat-icon {
        font-size: 0.625rem;
      }
    }

    .series-info h4 {
      font-size: 0.5rem;
      margin-bottom: 0.0625rem;
    }

    .series-info p {
      font-size: 0.375rem;
    }
  }

  .denomination-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.25rem;
  }

  .denomination-card {
    padding: 0.25rem;

    .denomination-icon {
      width: 16px;
      height: 16px;
      margin-bottom: 0.125rem;

      mat-icon {
        font-size: 0.625rem;
      }
    }

    .denomination-info h4 {
      font-size: 0.375rem;
      margin-bottom: 0.0625rem;
    }

    .denomination-info p {
      font-size: 0.25rem;
    }
  }

  .quantity-controls {
    gap: 0.25rem;
  }

  .quantity-input-group {
    .quantity-label {
      font-size: 0.5rem;
      margin-bottom: 0.125rem;
    }

    .quantity-input {
      padding: 0.25rem;
      font-size: 0.625rem;
      border-radius: 3px;
    }
  }

  .no-inventory-section {
    padding: 0.75rem 0.5rem;
    min-height: 120px;

    .no-inventory-card {
      padding: 1rem;
      border-radius: 8px;

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        margin-bottom: 0.5rem;
      }

      h3 {
        font-size: 0.9rem;
        margin-bottom: 0.375rem;
      }

      p {
        font-size: 0.7rem;
      }
    }
  }

  .modal-actions {
    padding: 0.375rem 0.5rem;
    gap: 0.25rem;

    .cancel-btn,
    .add-btn {
      padding: 0.375rem;
      font-size: 0.5rem;
      border-radius: 3px;
    }
  }
}

@media (max-width: 320px) {
  .add-cash-modal-container {
    transform: scale(0.8);
  }

  .modal-header {
    padding: 0.375rem;

    .header-content {
      gap: 0.125rem;

      .header-icon {
        width: 20px;
        height: 20px;

        mat-icon {
          font-size: 0.625rem;
        }
      }

      .header-text h2 {
        font-size: 0.75rem;
      }

      .header-text p {
        font-size: 0.375rem;
      }
    }
  }

  .modal-content {
    padding: 0.375rem;
  }

  .series-grid {
    grid-template-columns: 1fr;
    gap: 0.125rem;
  }

  .series-card {
    padding: 0.125rem;

    .series-icon {
      width: 16px;
      height: 16px;
      margin-bottom: 0.0625rem;

      mat-icon {
        font-size: 0.5rem;
      }
    }

    .series-info h4 {
      font-size: 0.375rem;
      margin-bottom: 0.03125rem;
    }

    .series-info p {
      font-size: 0.25rem;
    }
  }

  .denomination-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.125rem;
  }

  .denomination-card {
    padding: 0.125rem;

    .denomination-icon {
      width: 12px;
      height: 12px;
      margin-bottom: 0.0625rem;

      mat-icon {
        font-size: 0.5rem;
      }
    }

    .denomination-info h4 {
      font-size: 0.25rem;
      margin-bottom: 0.03125rem;
    }

    .denomination-info p {
      font-size: 0.1875rem;
    }
  }

  .quantity-input-group {
    .quantity-label {
      font-size: 0.375rem;
      margin-bottom: 0.0625rem;
    }

    .quantity-input {
      padding: 0.125rem;
      font-size: 0.5rem;
      border-radius: 2px;
    }
  }

  .no-inventory-section {
    padding: 0.5rem 0.375rem;
    min-height: 100px;

    .no-inventory-card {
      padding: 0.75rem;
      border-radius: 6px;

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        margin-bottom: 0.375rem;
      }

      h3 {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
      }

      p {
        font-size: 0.6rem;
      }
    }
  }

  .modal-actions {
    padding: 0.25rem 0.375rem;
    gap: 0.125rem;

    .cancel-btn,
    .add-btn {
      padding: 0.25rem;
      font-size: 0.375rem;
      border-radius: 2px;
    }
  }
}

// Global Modal Styles - Subtle backdrop blur that preserves background visibility
:host ::ng-deep {
  // Target all possible backdrop classes with maximum specificity
  .add-cash-modal-backdrop,
  .cdk-overlay-backdrop.add-cash-modal-backdrop,
  .mat-dialog-backdrop.add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
    animation: backdropFadeIn 0.4s ease-out !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;

    // Add subtle hover effect to indicate interactivity
    &:hover {
      backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      background: rgba(15, 23, 42, 0.5) !important;
    }
  }

  .add-cash-modal-panel {
    border-radius: 24px !important;
    overflow: hidden !important;
    box-shadow: 0 32px 64px rgba(0,0,0,0.12) !important;

    .mat-mdc-dialog-container {
      border-radius: 24px !important;
      overflow: hidden !important;
      padding: 0 !important;
    }
  }

  .modern-modal-panel {
    .mat-mdc-dialog-container {
      max-width: none !important;
      max-height: none !important;
    }
  }
}

// ===== DYE STAINED EFFECTS =====
// Paint Stain Container
.paint-stain-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
  opacity: 0.8;
  transition: opacity 0.4s ease;

  // Main Paint Stain - Large irregular blob
  .paint-stain.main-stain {
    position: absolute;
    top: 15%;
    right: 10%;
    width: 45px;
    height: 35px;
    background: var(--paint-color, #dc2626);
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: rotate(-15deg);
    filter: blur(0.5px);
    animation: paintPulse 3s ease-in-out infinite;
  }

  // Secondary Stains
  .paint-stain.secondary-stain {
    position: absolute;
    background: var(--paint-color, #dc2626);
    border-radius: 50%;
    filter: blur(1px);
    animation: paintFloat 4s ease-in-out infinite;

    &.stain-1 {
      top: 25%;
      left: 15%;
      width: 12px;
      height: 12px;
      animation-delay: 0.5s;
    }

    &.stain-2 {
      bottom: 30%;
      right: 20%;
      width: 8px;
      height: 8px;
      animation-delay: 1s;
    }

    &.stain-3 {
      top: 60%;
      left: 25%;
      width: 6px;
      height: 6px;
      animation-delay: 1.5s;
    }
  }

  // Paint Drips
  .paint-drip {
    position: absolute;
    background: var(--paint-color, #dc2626);
    border-radius: 0 0 50% 50%;
    filter: blur(0.5px);
    animation: paintDrip 5s ease-in-out infinite;

    &.drip-1 {
      top: 45%;
      right: 25%;
      width: 3px;
      height: 15px;
      animation-delay: 2s;
    }

    &.drip-2 {
      top: 35%;
      left: 30%;
      width: 2px;
      height: 12px;
      animation-delay: 2.5s;
    }

    &.drip-3 {
      bottom: 25%;
      right: 15%;
      width: 2px;
      height: 10px;
      animation-delay: 3s;
    }
  }
}

// Paint Color Variations
.paint-stain-container {
  &.stain-red { --paint-color: #dc2626; }
  &.stain-blue { --paint-color: #2563eb; }
  &.stain-green { --paint-color: #16a34a; }
  &.stain-purple { --paint-color: #9333ea; }
  &.stain-orange { --paint-color: #ea580c; }
  &.stain-teal { --paint-color: #0d9488; }
  &.stain-pink { --paint-color: #db2777; }
  &.stain-amber { --paint-color: #d97706; }
}

// Paint Animations
@keyframes paintPulse {
  0%, 100% { transform: rotate(-15deg) scale(1); opacity: 0.8; }
  50% { transform: rotate(-12deg) scale(1.05); opacity: 0.9; }
}

@keyframes paintFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.6; }
  50% { transform: translateY(-2px) rotate(5deg); opacity: 0.8; }
}

@keyframes paintDrip {
  0%, 100% { transform: scaleY(1); opacity: 0.7; }
  50% { transform: scaleY(1.1); opacity: 0.9; }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

// Dye Stained Indicator - Matching toggle component style
.dye-stained-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background: rgba(212, 175, 55, 0.2);
  border: 1px solid var(--absa-gold);
  border-radius: 12px;
  margin-top: 0.5rem;
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  transition: all 0.4s ease;

  .indicator-icon {
    font-size: 1rem;
    width: 1rem;
    height: 1rem;
    color: var(--absa-gold);
    animation: pulse 2s ease-in-out infinite;
  }

  .indicator-text {
    font-size: 0.7rem;
    font-weight: 700;
    color: var(--absa-gold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.4s ease;
  }

  // Add a pulsing dot similar to the toggle
  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--absa-gold);
    box-shadow: 0 0 12px rgba(212, 175, 55, 0.6);
    animation: pulse 2s ease-in-out infinite;
    margin-right: 0.25rem;
  }
}

// Header Icon Dye Stained Effects
.header-icon {
  &.dye-stained-icon {
    position: relative;

    .icon-paint-splash {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 8px;
      height: 8px;
      background: var(--paint-color, #d97706);
      border-radius: 50%;
      animation: paintSplash 2s ease-in-out infinite;
    }
  }
}

// Dynamic paint color based on data attribute
.remove-cash-modal-container {
  &[data-dye-color="red"] .paint-stain-container { --paint-color: #dc2626; }
  &[data-dye-color="blue"] .paint-stain-container { --paint-color: #2563eb; }
  &[data-dye-color="green"] .paint-stain-container { --paint-color: #16a34a; }
  &[data-dye-color="purple"] .paint-stain-container { --paint-color: #9333ea; }
  &[data-dye-color="orange"] .paint-stain-container { --paint-color: #ea580c; }
  &[data-dye-color="teal"] .paint-stain-container { --paint-color: #0d9488; }
  &[data-dye-color="pink"] .paint-stain-container { --paint-color: #db2777; }
  &[data-dye-color="amber"] .paint-stain-container { --paint-color: #d97706; }

  &[data-dye-color="red"] .header-icon .icon-paint-splash { background: #dc2626; }
  &[data-dye-color="blue"] .header-icon .icon-paint-splash { background: #2563eb; }
  &[data-dye-color="green"] .header-icon .icon-paint-splash { background: #16a34a; }
  &[data-dye-color="purple"] .header-icon .icon-paint-splash { background: #9333ea; }
  &[data-dye-color="orange"] .header-icon .icon-paint-splash { background: #ea580c; }
  &[data-dye-color="teal"] .header-icon .icon-paint-splash { background: #0d9488; }
  &[data-dye-color="pink"] .header-icon .icon-paint-splash { background: #db2777; }
  &[data-dye-color="amber"] .header-icon .icon-paint-splash { background: #d97706; }
}

@keyframes paintSplash {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
}

// Additional global styles to ensure backdrop blur works
::ng-deep {
  // Target CDK overlay backdrop
  .cdk-overlay-backdrop.add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
  }

  // Target Material dialog backdrop
  .mat-dialog-backdrop.add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
  }

  // Fallback for any backdrop with our class
  .add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) !important;
    -webkit-backdrop-filter: blur(0px) !important;
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
