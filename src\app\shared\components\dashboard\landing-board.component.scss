.dashboard-container {
  display: flex;
  flex-direction: column;
 padding-bottom: 20px;
}

.dashboard-card {
  max-width: 1200px;

  margin-top: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .mat-mdc-card-content {
    padding: 10px;
  }
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-title {
  color: #800020;
  font-size: xx-large;
  font-family: "Source Sans Pro", "Roboto", sans-serif;
  font-weight: bold;
  margin: 0 0 10px 0;
  line-height: 1.2;
}

.user-code {
  color: #800020;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.description {
  color: #800020;
  font-size: 16px;
  margin: 0 0 30px 0;
  line-height: 1.4;
}

.add-request-btn {
  background-color: #800020 !important;
  color: white !important;
  border-radius: 6px !important;
  padding: 12px 20px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  min-height: 48px !important;

  &:hover {
    background-color: #a61829 !important;
  }

  mat-icon {
    margin-right: 8px;
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-card {
    .mat-mdc-card-content {
      padding: 20px !important;
    }
  }

  .welcome-title {
    font-size: 32px;
  }

  .add-request-btn {
    width: 100%;
    justify-content: center;
  }
}
