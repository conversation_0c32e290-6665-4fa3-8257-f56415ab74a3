<mat-toolbar>
    <div class="left" (click)="navigateToDashboard()" role="button" tabindex="0" (keydown.enter)="navigateToDashboard()" (keydown.space)="navigateToDashboard()">
        <div id="image">
            <img src="/absa_logo.png" alt="ABSA Logo">
        </div>
        <div id="header">
            FFA CMS
        </div>
    </div>
    <!-- <div class="center">
        <button matIconButton class="example-icon cart-icon" aria-label="Example icon-button with heart icon">
            <mat-icon>shopping_cart</mat-icon>
        </button>
    </div> -->
    <div class="right">
        <div class="profile" *ngIf="authService.currentUser$ | async as userData">
            <div class="user-avatar" aria-label="User avatar">
                {{ getUserInitials(userData.name) }}
            </div>
            <span class="username">{{ userData.name }}</span>
        </div>
        <div class="menu-items">
            <button mat-button [matMenuTriggerFor]="menu">
                <mat-icon class="expand_more">expand_more</mat-icon>
            </button>

            <mat-menu #menu="matMenu">
                <button mat-menu-item (click)="logout()">
                    <mat-icon>logout</mat-icon>
                    <span>Logout</span>
                </button>
            </mat-menu>
        </div>
    </div>
</mat-toolbar>
