import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';

import { InventoryService } from '../../services/inventory.service';
import { ExcelExportService } from '../../services/excel-export.service';
import {
  CashInventory,
  CoinInventory,
  DyeStainedCashInventory,
  NoteSeries,
  NoteDenomination,
  CoinSeries,
  CoinDenomination,
  NOTE_SERIES_LABELS,
  DENOMINATION_LABELS,
  COIN_SERIES_LABELS,
  COIN_DENOMINATION_LABELS,
  InventorySummary
} from '../../../../shared/models/inventory.model';

export interface AuditReportData {
  // Optional data can be passed to the modal
}

export interface AuditReportSummary {
  totalInventoryValue: number;
  totalNotes: number;
  totalCoins: number;
  totalCoinValue: number;
  noteSeriesSummary: NoteSeriesSummary[];
  coinSummary: CoinSummaryItem[];
  denominationBreakdown: DenominationSummaryItem[];
  lowStockItems: LowStockItem[];
  dyeStainedSummary: DyeStainedAuditSummary;
  generatedDate: Date;
}

export interface NoteSeriesSummary {
  series: NoteSeries;
  seriesName: string;
  totalValue: number;
  totalNotes: number;
  totalBatches: number;
  totalSingles: number;
  denominations: DenominationDetail[];
}

export interface DenominationDetail {
  denomination: NoteDenomination;
  denominationLabel: string;
  quantity: number;
  batches: number;
  singles: number;
  value: number;
  stockLevel: 'normal' | 'low' | 'out-of-stock';
}

export interface CoinSummaryItem {
  denomination: CoinDenomination;
  denominationLabel: string;
  totalQuantity: number;
  totalBatches: number;
  totalValue: number;
  seriesBreakdown: CoinSeriesDetail[];
}

export interface CoinSeriesDetail {
  series: CoinSeries;
  seriesName: string;
  quantity: number;
  batches: number;
  value: number;
}

export interface DenominationSummaryItem {
  denomination: number;
  denominationLabel: string;
  totalQuantity: number;
  totalValue: number;
  type: 'note' | 'coin';
}

export interface LowStockItem {
  type: 'note' | 'coin';
  series: string;
  denomination: string;
  currentQuantity: number;
  stockLevel: string;
  value: number;
}

export interface DyeStainedAuditSummary {
  totalValue: number;
  totalNotes: number;
  seriesSummary: DyeStainedSeriesSummary[];
  denominationBreakdown: DyeStainedDenominationSummary[];
}

export interface DyeStainedSeriesSummary {
  series: NoteSeries | string;
  seriesName: string;
  totalValue: number;
  totalNotes: number;
  totalBatches: number;
  totalSingles: number;
  denominations: DyeStainedDenominationDetail[];
}

export interface DyeStainedDenominationDetail {
  denomination: NoteDenomination;
  denominationLabel: string;
  quantity: number;
  batches: number;
  singles: number;
  value: number;
  stockLevel: 'normal' | 'low' | 'out-of-stock';
}

export interface DyeStainedDenominationSummary {
  denomination: NoteDenomination;
  denominationLabel: string;
  totalQuantity: number;
  totalValue: number;
  seriesBreakdown: { series: NoteSeries | string; seriesName: string; quantity: number; value: number }[];
}

@Component({
  selector: 'app-audit-report-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatTabsModule,
    MatCardModule,
    MatDividerModule
  ],
  templateUrl: './audit-report-modal.component.html',
  styleUrls: ['./audit-report-modal.component.scss']
})
export class AuditReportModalComponent implements OnInit {
  auditReport: AuditReportSummary | null = null;
  isLoading = true;
  isExporting = false;

  // Table columns
  noteSeriesColumns: string[] = ['series', 'totalNotes', 'totalBatches', 'totalSingles', 'totalValue'];
  denominationColumns: string[] = ['denomination', 'quantity', 'batches', 'singles', 'value', 'stockLevel'];
  coinColumns: string[] = ['denomination', 'totalQuantity', 'totalBatches', 'totalValue'];
  lowStockColumns: string[] = ['type', 'series', 'denomination', 'currentQuantity', 'stockLevel', 'value'];

  constructor(
    public dialogRef: MatDialogRef<AuditReportModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AuditReportData,
    private inventoryService: InventoryService,
    private excelExportService: ExcelExportService
  ) {}

  ngOnInit(): void {
    this.generateAuditReport();
  }

  private generateAuditReport(): void {
    this.isLoading = true;

    try {
      const cashInventory = this.inventoryService.getCashInventory();
      const coinInventory = this.inventoryService.getCoinInventory();
      const dyeStainedInventory = this.inventoryService.getDyeStainedCashInventory();
      const inventorySummary = this.inventoryService.getInventorySummary();

      // Calculate individual category totals
      const cashOnlyValue = this.calculateCashOnlyValue(cashInventory);
      const dyeStainedValue = this.calculateDyeStainedValue(dyeStainedInventory);
      const coinValue = this.calculateTotalCoinValue(coinInventory);

      this.auditReport = {
        totalInventoryValue: cashOnlyValue, // Only cash inventory value (excluding dye-stained and coins)
        totalNotes: inventorySummary.totalNotes,
        totalCoins: this.calculateTotalCoins(coinInventory),
        totalCoinValue: coinValue,
        noteSeriesSummary: this.generateNoteSeriesSummary(cashInventory),
        coinSummary: this.generateCoinSummary(coinInventory),
        denominationBreakdown: this.generateDenominationBreakdown(cashInventory, coinInventory),
        lowStockItems: this.generateLowStockItems(cashInventory, coinInventory),
        dyeStainedSummary: this.generateDyeStainedSummary(dyeStainedInventory),
        generatedDate: new Date()
      };

      this.isLoading = false;
    } catch (error) {
      console.error('Error generating audit report:', error);
      this.isLoading = false;
    }
  }

  private calculateTotalCoins(coinInventory: CoinInventory[]): number {
    return coinInventory.reduce((total, coin) => total + coin.quantity, 0);
  }

  private calculateTotalCoinValue(coinInventory: CoinInventory[]): number {
    return coinInventory.reduce((total, coin) => total + coin.value, 0);
  }

  /**
   * Calculate cash inventory value only (excluding fake notes)
   */
  private calculateCashOnlyValue(cashInventory: CashInventory[]): number {
    return cashInventory.reduce((total, item) => {
      // Exclude fake notes from total value calculations
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        return total + item.value;
      }
      return total;
    }, 0);
  }

  /**
   * Calculate dye-stained inventory value
   */
  private calculateDyeStainedValue(dyeStainedInventory: DyeStainedCashInventory[]): number {
    return dyeStainedInventory.reduce((total, item) => {
      // Dye-stained money has real monetary value
      return total + item.value;
    }, 0);
  }

  private generateNoteSeriesSummary(cashInventory: CashInventory[]): NoteSeriesSummary[] {
    const seriesMap = new Map<NoteSeries, NoteSeriesSummary>();

    cashInventory.forEach(item => {
      if (!seriesMap.has(item.noteSeries)) {
        seriesMap.set(item.noteSeries, {
          series: item.noteSeries,
          seriesName: NOTE_SERIES_LABELS[item.noteSeries],
          totalValue: 0,
          totalNotes: 0,
          totalBatches: 0,
          totalSingles: 0,
          denominations: []
        });
      }

      const summary = seriesMap.get(item.noteSeries)!;
      const batches = Math.floor(item.quantity / 100);
      const singles = item.quantity % 100;

      // Exclude fake notes from value calculations
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        summary.totalValue += item.value;
      }
      summary.totalNotes += item.quantity;
      summary.totalBatches += batches;
      summary.totalSingles += singles;

      summary.denominations.push({
        denomination: item.denomination,
        denominationLabel: DENOMINATION_LABELS[item.denomination],
        quantity: item.quantity,
        batches,
        singles,
        value: item.noteSeries === NoteSeries.FAKE_NOTES ? 0 : item.value,
        stockLevel: this.getStockLevel(item.quantity, item.denomination)
      });
    });

    return Array.from(seriesMap.values()).sort((a, b) => a.seriesName.localeCompare(b.seriesName));
  }

  private generateCoinSummary(coinInventory: CoinInventory[]): CoinSummaryItem[] {
    const denominationMap = new Map<CoinDenomination, CoinSummaryItem>();

    coinInventory.forEach(coin => {
      if (!denominationMap.has(coin.denomination)) {
        denominationMap.set(coin.denomination, {
          denomination: coin.denomination,
          denominationLabel: COIN_DENOMINATION_LABELS[coin.denomination],
          totalQuantity: 0,
          totalBatches: 0,
          totalValue: 0,
          seriesBreakdown: []
        });
      }

      const summary = denominationMap.get(coin.denomination)!;
      summary.totalQuantity += coin.quantity;
      summary.totalBatches += coin.batches;
      summary.totalValue += coin.value;

      summary.seriesBreakdown.push({
        series: coin.series,
        seriesName: COIN_SERIES_LABELS[coin.series],
        quantity: coin.quantity,
        batches: coin.batches,
        value: coin.value
      });
    });

    return Array.from(denominationMap.values()).sort((a, b) => b.denomination - a.denomination);
  }

  private generateDenominationBreakdown(cashInventory: CashInventory[], coinInventory: CoinInventory[]): DenominationSummaryItem[] {
    const breakdown: DenominationSummaryItem[] = [];

    // Add note denominations (exclude fake notes from value calculations)
    const noteMap = new Map<NoteDenomination, { quantity: number; value: number }>();
    cashInventory.forEach(item => {
      if (!noteMap.has(item.denomination)) {
        noteMap.set(item.denomination, { quantity: 0, value: 0 });
      }
      const existing = noteMap.get(item.denomination)!;
      existing.quantity += item.quantity;
      // Exclude fake notes from value calculations
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        existing.value += item.value;
      }
    });

    noteMap.forEach((data, denomination) => {
      breakdown.push({
        denomination,
        denominationLabel: DENOMINATION_LABELS[denomination],
        totalQuantity: data.quantity,
        totalValue: data.value,
        type: 'note'
      });
    });

    // Add coin denominations
    const coinMap = new Map<CoinDenomination, { quantity: number; value: number }>();
    coinInventory.forEach(coin => {
      if (!coinMap.has(coin.denomination)) {
        coinMap.set(coin.denomination, { quantity: 0, value: 0 });
      }
      const existing = coinMap.get(coin.denomination)!;
      existing.quantity += coin.quantity;
      existing.value += coin.value;
    });

    coinMap.forEach((data, denomination) => {
      breakdown.push({
        denomination,
        denominationLabel: COIN_DENOMINATION_LABELS[denomination],
        totalQuantity: data.quantity,
        totalValue: data.value,
        type: 'coin'
      });
    });

    return breakdown.sort((a, b) => b.denomination - a.denomination);
  }

  private generateLowStockItems(cashInventory: CashInventory[], coinInventory: CoinInventory[]): LowStockItem[] {
    const lowStockItems: LowStockItem[] = [];

    // Check cash inventory for low stock (exclude fake notes)
    cashInventory.forEach(item => {
      // Exclude fake notes from low stock alerts
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        const stockLevel = this.getStockLevel(item.quantity, item.denomination);
        if (stockLevel === 'low' || stockLevel === 'out-of-stock') {
          lowStockItems.push({
            type: 'note',
            series: NOTE_SERIES_LABELS[item.noteSeries],
            denomination: DENOMINATION_LABELS[item.denomination],
            currentQuantity: item.quantity,
            stockLevel,
            value: item.value
          });
        }
      }
    });

    // Check coin inventory for low stock
    coinInventory.forEach(coin => {
      const stockLevel = this.getCoinStockLevel(coin.quantity);
      if (stockLevel === 'low' || stockLevel === 'out-of-stock') {
        lowStockItems.push({
          type: 'coin',
          series: COIN_SERIES_LABELS[coin.series],
          denomination: COIN_DENOMINATION_LABELS[coin.denomination],
          currentQuantity: coin.quantity,
          stockLevel,
          value: coin.value
        });
      }
    });

    return lowStockItems.sort((a, b) => a.stockLevel.localeCompare(b.stockLevel));
  }

  private generateDyeStainedSummary(dyeStainedInventory: DyeStainedCashInventory[]): DyeStainedAuditSummary {
    const seriesMap = new Map<NoteSeries | string, DyeStainedSeriesSummary>();
    const denominationMap = new Map<NoteDenomination, DyeStainedDenominationSummary>();

    let totalValue = 0;
    let totalNotes = 0;

    dyeStainedInventory.forEach(item => {
      totalValue += item.value;
      totalNotes += item.quantity;

      // Build series summary
      if (!seriesMap.has(item.noteSeries)) {
        const seriesName = typeof item.noteSeries === 'string'
          ? this.getCustomSeriesName(item.noteSeries)
          : NOTE_SERIES_LABELS[item.noteSeries];

        seriesMap.set(item.noteSeries, {
          series: item.noteSeries,
          seriesName,
          totalValue: 0,
          totalNotes: 0,
          totalBatches: 0,
          totalSingles: 0,
          denominations: []
        });
      }

      const seriesSummary = seriesMap.get(item.noteSeries)!;
      const batches = Math.floor(item.quantity / 100);
      const singles = item.quantity % 100;

      seriesSummary.totalValue += item.value;
      seriesSummary.totalNotes += item.quantity;
      seriesSummary.totalBatches += batches;
      seriesSummary.totalSingles += singles;

      seriesSummary.denominations.push({
        denomination: item.denomination,
        denominationLabel: DENOMINATION_LABELS[item.denomination],
        quantity: item.quantity,
        batches,
        singles,
        value: item.value,
        stockLevel: this.getStockLevel(item.quantity, item.denomination)
      });

      // Build denomination summary
      if (!denominationMap.has(item.denomination)) {
        denominationMap.set(item.denomination, {
          denomination: item.denomination,
          denominationLabel: DENOMINATION_LABELS[item.denomination],
          totalQuantity: 0,
          totalValue: 0,
          seriesBreakdown: []
        });
      }

      const denomSummary = denominationMap.get(item.denomination)!;
      denomSummary.totalQuantity += item.quantity;
      denomSummary.totalValue += item.value;

      const seriesName = typeof item.noteSeries === 'string'
        ? this.getCustomSeriesName(item.noteSeries)
        : NOTE_SERIES_LABELS[item.noteSeries];

      denomSummary.seriesBreakdown.push({
        series: item.noteSeries,
        seriesName,
        quantity: item.quantity,
        value: item.value
      });
    });

    return {
      totalValue,
      totalNotes,
      seriesSummary: Array.from(seriesMap.values()).sort((a, b) => a.seriesName.localeCompare(b.seriesName)),
      denominationBreakdown: Array.from(denominationMap.values()).sort((a, b) => b.denomination - a.denomination)
    };
  }

  private getCustomSeriesName(seriesId: string): string {
    const customSeries = this.inventoryService.getCustomNoteSeries();
    const series = customSeries.find(s => s.id === seriesId);
    return series ? series.name : `Custom Series (${seriesId})`;
  }

  private getStockLevel(quantity: number, denomination: NoteDenomination): 'normal' | 'low' | 'out-of-stock' {
    if (quantity === 0) return 'out-of-stock';

    const thresholds: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 50,
      [NoteDenomination.R20]: 40,
      [NoteDenomination.R50]: 30,
      [NoteDenomination.R100]: 20,
      [NoteDenomination.R200]: 10
    };

    return quantity <= thresholds[denomination] ? 'low' : 'normal';
  }

  private getCoinStockLevel(quantity: number): 'normal' | 'low' | 'out-of-stock' {
    if (quantity === 0) return 'out-of-stock';
    return quantity < 500 ? 'low' : 'normal';
  }

  onClose(): void {
    this.dialogRef.close();
  }

  async onDownloadExcel(): Promise<void> {
    if (!this.auditReport) {
      console.error('No audit report data available for export');
      return;
    }

    this.isExporting = true;

    try {
      console.log('Generating professional Excel report...');

      // Use setTimeout to allow UI to update with loading state
      setTimeout(async () => {
        try {
          await this.excelExportService.exportAuditReportToExcel(this.auditReport!);
          console.log('Professional Excel report generated and download initiated');
          this.isExporting = false;
        } catch (error) {
          console.error('Error generating Excel report:', error);
          this.isExporting = false;
          // You could show a snackbar or error message here
        }
      }, 100);

    } catch (error) {
      console.error('Error initiating Excel export:', error);
      this.isExporting = false;
    }
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(value);
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-ZA').format(value);
  }
}
