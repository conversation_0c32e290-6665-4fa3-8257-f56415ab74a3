<div class="remove-cash-modal-container"
     [class.dye-stained-mode]="shouldShowDyeStainedEffects()"
     [attr.data-dye-color]="shouldShowDyeStainedEffects() ? getDyeColor(selectedDenomination!) : null">



  <!-- Modern Header with Gradient -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon" [class.dye-stained-icon]="shouldShowDyeStainedEffects()">
        <mat-icon>{{ shouldShowDyeStainedEffects() ? 'colorize' : 'money_off' }}</mat-icon>
        <div *ngIf="shouldShowDyeStainedEffects()" class="icon-paint-splash"></div>
      </div>
      <div class="header-text">
        <h2>{{ shouldShowDyeStainedEffects() ? 'Remove Dye Stained Cash' : 'Remove Cash Inventory' }}</h2>
        <p>{{ shouldShowDyeStainedEffects() ? 'Removing dye stained currency from inventory' : 'Manage your cash reserves with precision' }}</p>
        <div *ngIf="shouldShowDyeStainedEffects()" class="dye-stained-indicator">
          <mat-icon class="indicator-icon">palette</mat-icon>
          <span class="indicator-text">Dye Stained Mode Active</span>
        </div>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Main Content Area -->
  <div class="modal-content">
    <form #removeCashForm="ngForm" class="remove-cash-form">

      <!-- Pre-selected Information Display (when series and denomination are pre-selected) -->
      <div class="form-section pre-selected-info" *ngIf="data?.series && data?.denomination">
        <div class="section-header">
          <mat-icon class="step-icon">info</mat-icon>
          <h3>Selected Item</h3>
        </div>
        <div class="pre-selected-display">
          <div class="selected-item-card">
            <div class="item-icon">
              <mat-icon>{{ getSeriesIcon(selectedSeries!) }}</mat-icon>
            </div>
            <div class="item-info">
              <h4>{{ NOTE_SERIES_LABELS[selectedSeries!] }}</h4>
              <p class="denomination-info">{{ DENOMINATION_LABELS[selectedDenomination!] }}</p>
              <p class="series-description">{{ getSeriesDescription(selectedSeries!) }}</p>
            </div>
            <div class="confirmation-badge">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 1: Series Selection (only show when not pre-selected) -->
      <div class="form-section series-selection" *ngIf="!data?.series">
        <div class="section-header">
          <mat-icon class="step-icon">category</mat-icon>
          <h3>Select Note Series</h3>
        </div>
        <div class="series-grid">
          <div *ngFor="let series of availableSeries"
               class="series-card"
               [class.selected]="selectedSeries === series"
               (click)="selectedSeries = series; onSeriesChange()">
            <div class="series-icon">
              <mat-icon>{{ getSeriesIcon(series) }}</mat-icon>
            </div>
            <div class="series-info">
              <h4>{{ NOTE_SERIES_LABELS[series] }}</h4>
              <p>{{ getSeriesDescription(series) }}</p>
            </div>
            <div class="selection-indicator" *ngIf="selectedSeries === series">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Denomination Selection (only show when not pre-selected) -->
      <div class="form-section denomination-section" *ngIf="selectedSeries && !data?.denomination">
        <div class="section-header">
          <mat-icon class="step-icon">payments</mat-icon>
          <h3>Choose Denomination</h3>
        </div>
        <div class="denomination-grid">
          <div class="denomination-card"
               *ngFor="let denomination of availableDenominations"
               [class.selected]="selectedDenomination === denomination"
               (click)="selectedDenomination = denomination; onDenominationChange()">
            <div class="denomination-icon">
              <mat-icon>{{ +denomination >= 100 ? 'credit_card' : 'receipt' }}</mat-icon>
            </div>
            <div class="denomination-info">
              <h4>{{ DENOMINATION_LABELS[denomination] }}</h4>
              <p>{{ +denomination >= 100 ? 'High value note' : 'Standard note' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Quantity Input -->
      <div class="form-section quantity-section" *ngIf="selectedDenomination && currentQuantity > 0">
        <div class="section-header">
          <mat-icon class="step-icon">calculate</mat-icon>
          <h3>Specify Removal Quantity</h3>
        </div>
        <div class="quantity-controls">
          <div class="quantity-card batches-card">
            <div class="card-header">
              <mat-icon>inventory_2</mat-icon>
              <h4>Batches</h4>
              <span class="helper-text">100 notes each</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(-1)" [disabled]="batches <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="batches"
                     name="batches"
                     min="0"
                     [max]="currentBatches"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(1)" [disabled]="batches >= currentBatches">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="available">Available: {{ currentBatches }} batches</span>
            </div>
          </div>

          <div class="quantity-card singles-card">
            <div class="card-header">
              <mat-icon>receipt_long</mat-icon>
              <h4>Singles</h4>
              <span class="helper-text">Individual notes</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(-1)" [disabled]="singles <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="singles"
                     name="singles"
                     min="0"
                     [max]="currentQuantity - (batches * 100)"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(1)" [disabled]="singles >= (currentQuantity - (batches * 100))">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="available">Available: {{ currentQuantity - (batches * 100) }} singles</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Reason (Optional) -->
      <div class="form-section reason-section" *ngIf="selectedDenomination && currentQuantity > 0">
        <div class="section-header">
          <mat-icon class="step-icon">description</mat-icon>
          <h3>Reason (Optional)</h3>
        </div>
        <mat-form-field class="reason-field" appearance="outline">
          <mat-label>Reason for removal</mat-label>
          <input matInput
                 [(ngModel)]="reason"
                 name="reason"
                 placeholder="e.g., Damaged notes, End of day reconciliation"
                 maxlength="200">
          <mat-hint>Optional: Provide a reason for this removal</mat-hint>
        </mat-form-field>
      </div>
      <!-- Enhanced Current Inventory Display -->
      <div class="current-inventory-section" *ngIf="selectedSeries && selectedDenomination">
        <div class="inventory-card">
          <!-- Header Section -->
          <div class="inventory-header">
            <div class="header-content">
              <div class="header-icon">
                <mat-icon>inventory</mat-icon>
              </div>
              <div class="header-text">
                <h3>Current Inventory</h3>
                <p class="series-info">{{ getSelectedSeriesLabel() }} • {{ getSelectedDenominationLabel() }}</p>
              </div>
            </div>
            <div class="status-badge available">
              <mat-icon>check_circle</mat-icon>
              <span>Available</span>
            </div>
          </div>

          <!-- Inventory Metrics Grid -->
          <div class="inventory-metrics">
            <div class="metric-card primary">
              <div class="metric-header">
                <div class="metric-icon">
                  <mat-icon>receipt_long</mat-icon>
                </div>
                <span class="metric-label">Total Notes</span>
              </div>
              <div class="metric-value">{{ currentQuantity | number }}</div>
              <div class="metric-subtitle">available</div>
            </div>

            <div class="metric-card value">
              <div class="metric-header">
                <div class="metric-icon">
                  <mat-icon>account_balance_wallet</mat-icon>
                </div>
                <span class="metric-label">Total Value</span>
              </div>
              <div class="metric-value">{{ formatCurrency(currentQuantity * selectedDenomination) }}</div>
              <div class="metric-subtitle">ZAR</div>
            </div>
          </div>

          <!-- Breakdown Details -->
          <div class="inventory-breakdown">
            <div class="breakdown-header">
              <mat-icon>analytics</mat-icon>
              <span>Inventory Breakdown</span>
            </div>
            <div class="breakdown-grid">
              <div class="breakdown-item">
                <div class="breakdown-icon">
                  <mat-icon>inventory_2</mat-icon>
                </div>
                <div class="breakdown-content">
                  <div class="breakdown-title">{{ currentBatches }} Batch{{ currentBatches !== 1 ? 'es' : '' }}</div>
                  <div class="breakdown-subtitle">{{ currentBatches * 100 }} notes</div>
                </div>
                <div class="breakdown-value">{{ formatCurrency(selectedDenomination * currentBatches * 100) }}</div>
              </div>
              <div class="breakdown-item">
                <div class="breakdown-icon">
                  <mat-icon>receipt_long</mat-icon>
                </div>
                <div class="breakdown-content">
                  <div class="breakdown-title">{{ currentSingles }} Single{{ currentSingles !== 1 ? 's' : '' }}</div>
                  <div class="breakdown-subtitle">{{ currentSingles }} notes</div>
                </div>
                <div class="breakdown-value">{{ formatCurrency(selectedDenomination * currentSingles) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Summary Display -->
      <div class="summary-display" *ngIf="totalQuantity > 0 && selectedDenomination">
        <div class="summary-card">
          <!-- Header Section -->
          <div class="summary-header">
            <div class="header-content">
              <div class="header-icon">
                <mat-icon>remove</mat-icon>
              </div>
              <div class="header-text">
                <h3>Removing from Inventory</h3>
                <p class="series-info">{{ getSelectedSeriesLabel() }} • {{ getSelectedDenominationLabel() }}</p>
              </div>
            </div>
            <div class="status-badge warning">
              <mat-icon>warning</mat-icon>
              <span>Removing</span>
            </div>
          </div>

          <!-- Main Metrics -->
          <div class="metrics-grid">
            <div class="metric-card quantity-card">
              <div class="metric-header">
                <div class="metric-icon">
                  <mat-icon>remove</mat-icon>
                </div>
                <span class="metric-label">Removing</span>
              </div>
              <div class="metric-value">{{ totalQuantity }}</div>
              <div class="metric-subtitle">notes</div>
            </div>

            <div class="metric-card remaining-card">
              <div class="metric-header">
                <div class="metric-icon">
                  <mat-icon>account_balance</mat-icon>
                </div>
                <span class="metric-label">Remaining</span>
              </div>
              <div class="metric-value">{{ remainingQuantity }}</div>
              <div class="metric-subtitle">notes</div>
            </div>
          </div>

          <!-- Value Summary -->
          <div class="value-summary">
            <div class="value-item removing">
              <span class="value-label">Removing Value:</span>
              <span class="value-amount">{{ formatCurrency(totalValue) }}</span>
            </div>
            <div class="value-item remaining">
              <span class="value-label">Remaining Value:</span>
              <span class="value-amount">{{ formatCurrency(remainingQuantity * selectedDenomination) }}</span>
            </div>
          </div>

          <!-- Breakdown Section -->
          <div class="breakdown-section" *ngIf="batches > 0 || singles > 0">
            <div class="breakdown-header">
              <mat-icon>analytics</mat-icon>
              <span>Breakdown</span>
            </div>
            <div class="breakdown-grid">
              <div class="breakdown-item" *ngIf="batches > 0">
                <div class="breakdown-icon">
                  <mat-icon>inventory_2</mat-icon>
                </div>
                <div class="breakdown-content">
                  <div class="breakdown-title">{{ batches }} Batch{{ batches !== 1 ? 'es' : '' }}</div>
                  <div class="breakdown-subtitle">{{ batches * 100 }} notes</div>
                </div>
                <div class="breakdown-value">{{ formatCurrency(selectedDenomination * batches * 100) }}</div>
              </div>
              <div class="breakdown-item" *ngIf="singles > 0">
                <div class="breakdown-icon">
                  <mat-icon>receipt_long</mat-icon>
                </div>
                <div class="breakdown-content">
                  <div class="breakdown-title">{{ singles }} Single{{ singles !== 1 ? 's' : '' }}</div>
                  <div class="breakdown-subtitle">{{ singles }} notes</div>
                </div>
                <div class="breakdown-value">{{ formatCurrency(selectedDenomination * singles) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Inventory Message -->
      <div class="no-inventory-section" *ngIf="selectedDenomination && currentQuantity === 0">
        <div class="no-inventory-card">
          <mat-icon>inventory_2</mat-icon>
          <h3>No Inventory Available</h3>
          <p>There are currently no {{ getSelectedDenominationLabel() }} notes available for removal.</p>
        </div>
      </div>
    </form>
  </div>

  <!-- Action Buttons -->
  <div class="modal-actions">
    <button mat-stroked-button class="cancel-btn" (click)="onCancel()">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>

    <button mat-raised-button
            class="remove-btn"
            [disabled]="!isFormValid()"
            (click)="onRemoveCash()">
      <mat-icon>remove</mat-icon>
      Remove from Inventory
    </button>
  </div>
</div>
