<div [formGroup]="bankNoteSelectionForm" class="bank-note-selection-form">
  <mat-tab-group>
    <!-- CASH TAB -->
    <mat-tab label="Cash">
      <mat-accordion>
        <!-- Mandela Series -->
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>Mandela Series</mat-panel-title>
          </mat-expansion-panel-header>

          <mat-accordion>
            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-checkbox>R10</mat-checkbox>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>Bundle input for R10</div>
              <input matInput type="Bundle input for R10">
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-checkbox>R20</mat-checkbox>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>Bundle input for R20</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-checkbox>R50</mat-checkbox>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>Bundle input for R50</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-checkbox>R100</mat-checkbox>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>Bundle input for R100</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-checkbox>R200</mat-checkbox>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div>Bundle input for R200</div>
            </mat-expansion-panel>
          </mat-accordion>
        </mat-expansion-panel>

        <!-- Repeat same for V6, Big 5, and Commemorative Series -->
        <!-- Just replace the series title, the inner accordion structure stays the same -->
      </mat-accordion>
    </mat-tab>

    <!-- COINS TAB -->
    <mat-tab label="Coins">
      <mat-accordion>
        <!-- Old Coins -->
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>Old Coins</mat-panel-title>
          </mat-expansion-panel-header>

          <mat-accordion>
            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title
                  ><mat-checkbox>10c</mat-checkbox></mat-panel-title
                >
              </mat-expansion-panel-header>
              <div>Bundle input for 10c</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title
                  ><mat-checkbox>20c</mat-checkbox></mat-panel-title
                >
              </mat-expansion-panel-header>
              <div>Bundle input for 20c</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title
                  ><mat-checkbox>50c</mat-checkbox></mat-panel-title
                >
              </mat-expansion-panel-header>
              <div>Bundle input for 50c</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title
                  ><mat-checkbox>R1</mat-checkbox></mat-panel-title
                >
              </mat-expansion-panel-header>
              <div>Bundle input for R1</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title
                  ><mat-checkbox>R2</mat-checkbox></mat-panel-title
                >
              </mat-expansion-panel-header>
              <div>Bundle input for R2</div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title
                  ><mat-checkbox>R5</mat-checkbox></mat-panel-title
                >
              </mat-expansion-panel-header>
              <div>Bundle input for R5</div>
            </mat-expansion-panel>
          </mat-accordion>
        </mat-expansion-panel>

        <!-- New Coins (same structure) -->
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>New Coins</mat-panel-title>
          </mat-expansion-panel-header>

          <mat-accordion>
            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title
                  ><mat-checkbox>10c</mat-checkbox></mat-panel-title
                >
              </mat-expansion-panel-header>
              <div>Bundle input for 10c</div>
            </mat-expansion-panel>
            <!-- ... repeat for 20c, 50c, R1, R2, R5 ... -->
          </mat-accordion>
        </mat-expansion-panel>
      </mat-accordion>
    </mat-tab>
  </mat-tab-group>

  <mat-radio-group formControlName="dyeStained" class="dye-stained">
    <mat-radio-button value="no">No</mat-radio-button>
    <mat-radio-button value="yes">Yes</mat-radio-button>
  </mat-radio-group>

  <mat-radio-group formControlName="fakeNotes" class="fake-notes">
    <mat-radio-button value="no">No</mat-radio-button>
    <mat-radio-button value="yes">Yes</mat-radio-button>
  </mat-radio-group>
</div>
