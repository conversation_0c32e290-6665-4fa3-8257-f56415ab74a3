<div [formGroup]="bankNoteSelectionForm" class="bank-note-selection-form">
  <mat-tab-group mat-stretch-tabs>
    <mat-tab
      *ngFor="let categoryGroup of categorizedCashTypes"
      [label]="categoryGroup.category"
    >
      <mat-accordion>
        <mat-expansion-panel
          *ngFor="let group of categoryGroup.cashTypes"
          class="series-panel"
        >
          <mat-expansion-panel-header>
            <mat-panel-title>{{ group.name }}</mat-panel-title>
          </mat-expansion-panel-header>

          <mat-accordion>
            <mat-expansion-panel
              class="denomination-panel"
              *ngFor="let denomination of group.denominations"
            >
              <mat-expansion-panel-header>
                <mat-checkbox class="denomination-checkbox">{{
                  denomination | currency : "R" : "symbol"
                }}</mat-checkbox>
              </mat-expansion-panel-header>
              <mat-form-field appearance="outline" floatLabel="always">
                <mat-label>Bundles for R{{ denomination }}.00</mat-label>
                <input matInput type="number" />
              </mat-form-field>
            </mat-expansion-panel>
          </mat-accordion>
        </mat-expansion-panel>
      </mat-accordion>
    </mat-tab>
  </mat-tab-group>
</div>
