<div class="login-page">
  <div class="login-card">
    <!-- Absa <PERSON>go -->
    <div class="logo-container">
      <img src="/absa_logo.png" alt="Absa Logo" class="absa-logo">
    </div>

    <!-- Welcome Text -->
    <h1 class="welcome-title">Welcome To FFA-CMS</h1>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-section">

        <!-- Email Field -->
        <div class="input-group">
          <label class="input-label">Email Address</label>
          <input
            type="email"
            formControlName="email"
            placeholder="<EMAIL>"
            class="login-input"
            [class.error]="loginForm.get('email')?.touched && loginForm.get('email')?.invalid"
          >
        </div>

        <!-- Password Field -->
        <div class="input-group">
          <label class="input-label">Password</label>
          <input
            type="password"
            formControlName="password"
            placeholder="Enter your password"
            class="login-input"
            [class.error]="loginForm.get('password')?.touched && loginForm.get('password')?.invalid"
          >
        </div>

        <!-- Login Button -->
        <button
          type="submit"
          class="login-button"
          [disabled]="loading || loginForm.invalid"
        >
          {{ loading ? 'Logging in...' : 'Log In' }}
        </button>

        <!-- Error Message -->
        <div class="error-message" *ngIf="error">
          {{ error }}
        </div>


      </div>
    </form>
  </div>
</div>
