// ===== RESPONSIVE BREAKPOINTS =====
$mobile-small: 320px;
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$desktop-large: 1200px;

// ===== RESPONSIVE MIXINS =====
@mixin mobile-small {
  @media (max-width: #{$mobile-small - 1px}) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: #{$tablet - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $desktop) {
    @content;
  }
}

// ===== QA DASHBOARD CONTAINER =====
.qa-dashboard-container {
  min-height: 100vh;
  background: white;
  padding: 2rem;
  position: relative;

  @include tablet {
    padding: 1.5rem;
  }

  @include mobile {
    padding: 1rem;
  }



  > * {
    position: relative;
    z-index: 1;
  }
}

// ===== DASHBOARD HEADER =====
.dashboard-header {
  padding: 0 0.5rem;
  margin-bottom: 3rem;
  text-align: center;

  @include tablet {
    margin-bottom: 2rem;
  }

  @include mobile {
    margin-bottom: 1.5rem;
    padding: 0;
  }

  h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--absa-gray-900);

    @include tablet {
      font-size: 1.75rem;
    }

    @include mobile {
      font-size: 1.5rem;
    }
  }
}

// ===== DASHBOARD CONTENT =====
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;

  @include tablet {
    gap: 2rem;
  }

  @include mobile {
    gap: 1.5rem;
  }
}

// ===== UNIFIED CONTENT CARD =====
.unified-content-card {
  background: var(--absa-white);
  border-radius: 16px;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid var(--absa-gray-200);

  @include mobile {
    border-radius: 12px;
  }

  // Override the landing board component's card styling to remove duplicate background
  ::ng-deep app-landingboard {
    .dashboard-card {
      background: transparent;
      box-shadow: none;
      border-radius: 0;
      margin-top: 0;
    }
  }
}

// ===== QUICK ACTIONS SECTION =====
.quick-actions-section {
  padding: 0 2rem 2rem 2rem;
  border-top: 1px solid var(--absa-gray-100);

  @include tablet {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }

  @include mobile {
    padding: 0 1rem 1rem 1rem;
  }

  .section-header {
    margin-bottom: 2rem;
    padding: 2rem 0 0 0;

    @include tablet {
      padding: 1.5rem 0 0 0;
    }

    @include mobile {
      margin-bottom: 1.5rem;
      padding: 1rem 0 0 0;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      mat-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
        color: var(--absa-red);

        @include mobile {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }
      }

      h2 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--absa-gray-900);

        @include tablet {
          font-size: 1.5rem;
        }

        @include mobile {
          font-size: 1.25rem;
        }
      }
    }
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;

    @include tablet {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    @include mobile {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }
}

// ===== ACTION CARDS =====
.action-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--absa-gray-200);
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);

  @include mobile {
    border-radius: 12px;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow:
      0 12px 24px rgba(0, 0, 0, 0.1),
      0 4px 8px rgba(0, 0, 0, 0.06);

    .card-background {
      opacity: 1;
      transform: scale(1.05);
    }

    .card-arrow {
      transform: translateX(4px);
    }
  }

  &:active {
    transform: translateY(-2px);
  }

  .card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 0;
  }

  &.primary .card-background {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  }

  &.secondary .card-background {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  }

  &.tertiary .card-background {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
  }

  ::ng-deep .mat-mdc-card-content {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    z-index: 1;

    @include tablet {
      padding: 1.5rem;
      gap: 1.25rem;
    }

    @include mobile {
      padding: 1.25rem;
      gap: 1rem;
    }
  }

  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 56px;
    border-radius: 12px;
    transition: all 0.3s ease;

    @include mobile {
      width: 48px;
      height: 48px;
      border-radius: 10px;
    }

    mat-icon {
      font-size: 28px;
      width: 28px;
      height: 28px;

      @include mobile {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }
  }

  &.primary .card-icon {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid rgba(16, 185, 129, 0.2);

    mat-icon {
      color: #10b981;
    }
  }

  &.secondary .card-icon {
    background: rgba(245, 158, 11, 0.1);
    border: 2px solid rgba(245, 158, 11, 0.2);

    mat-icon {
      color: #f59e0b;
    }
  }

  &.tertiary .card-icon {
    background: rgba(139, 92, 246, 0.1);
    border: 2px solid rgba(139, 92, 246, 0.2);

    mat-icon {
      color: #8b5cf6;
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--absa-gray-900);
      line-height: 1.2;

      @include tablet {
        font-size: 1.125rem;
      }

      @include mobile {
        font-size: 1rem;
      }
    }

    p {
      margin: 0;
      font-size: 0.875rem;
      color: var(--absa-gray-600);
      line-height: 1.4;

      @include mobile {
        font-size: 0.8rem;
      }
    }

    .coming-soon-badge {
      display: inline-flex;
      align-items: center;
      padding: 0.25rem 0.75rem;
      background: rgba(139, 92, 246, 0.1);
      color: #8b5cf6;
      border: 1px solid rgba(139, 92, 246, 0.2);
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-top: 0.5rem;
      width: fit-content;

      @include mobile {
        font-size: 0.7rem;
        padding: 0.2rem 0.6rem;
      }
    }
  }

  .card-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    @include mobile {
      width: 28px;
      height: 28px;
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: var(--absa-gray-500);

      @include mobile {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }

  &:hover .card-arrow {
    background: rgba(0, 0, 0, 0.1);

    mat-icon {
      color: var(--absa-gray-700);
    }
  }

  // Disabled state for coming soon card
  &.tertiary {
    cursor: default;

    &:hover {
      transform: none;
      box-shadow:
        0 4px 6px rgba(0, 0, 0, 0.05),
        0 1px 3px rgba(0, 0, 0, 0.1);

      .card-background {
        opacity: 0.5;
        transform: scale(1);
      }

      .card-arrow {
        transform: none;
        background: rgba(139, 92, 246, 0.1);

        mat-icon {
          color: #8b5cf6;
        }
      }
    }
  }
}

// ===== RESPONSIVE ADJUSTMENTS =====
@include tablet {
  .action-card {
    ::ng-deep .mat-mdc-card-content {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .card-content {
      align-items: center;
      text-align: center;
    }

    .card-arrow {
      align-self: center;
    }
  }
}

@include mobile {
  .qa-dashboard-container {
    min-height: auto;
  }

  .action-card {
    ::ng-deep .mat-mdc-card-content {
      flex-direction: row;
      text-align: left;
    }

    .card-content {
      align-items: flex-start;
      text-align: left;
    }
  }
}
