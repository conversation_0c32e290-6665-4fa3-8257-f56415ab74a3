import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import {MatIconModule} from '@angular/material/icon';
import {MatButtonModule} from '@angular/material/button';
import {MatToolbarModule} from '@angular/material/toolbar';
import {MatMenuModule} from '@angular/material/menu';
import { AuthService } from '../../features/login/auth.service';


@Component({
  selector: 'navbar',
  imports: [CommonModule, MatIconModule,MatButtonModule,MatToolbarModule,MatMenuModule],
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.scss'
})
export class NavbarComponent {

  constructor(
    public authService: AuthService,
    private router: Router
  ) {}

  /**
   * Get user initials from the user's name
   * @param name - Full name of the user
   * @returns Initials (first letter of first name + first letter of last name)
   */
  getUserInitials(name: string): string {
    if (!name) return 'U';

    const nameParts = name.trim().split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    }

    const firstInitial = nameParts[0].charAt(0).toUpperCase();
    const lastInitial = nameParts[nameParts.length - 1].charAt(0).toUpperCase();

    return firstInitial + lastInitial;
  }

  /**
   * Navigate to the user's role-specific dashboard
   */
  navigateToDashboard(): void {
    const dashboardRoute = this.authService.getRoleDashboardRoute();
    this.router.navigate([dashboardRoute]);
  }

  logout(): void {
    this.authService.logout().subscribe(() => {
      this.router.navigate(['/login']);
    });
  }
}
