import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError, Observable, tap } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { CashType } from '../../../shared/models';

@Injectable({
  providedIn: 'root'
})
export class RequestCashService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  // Add service methods here
  getAllCashTypes(): Observable<CashType[]> {
    return this.http.get<CashType[]>(`${this.apiUrl}/cashTypes`)
      .pipe(
        tap(data => console.log(JSON.stringify(data)))
      )
  }
}
