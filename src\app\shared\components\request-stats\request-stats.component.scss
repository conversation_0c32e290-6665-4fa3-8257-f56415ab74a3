// ===== REQUEST STATS DASHBOARD =====
.absa-dashboard-section {
  margin-bottom: 2rem;

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.25rem;
    }

    @media (max-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }
  }
}

// ===== METRIC CARDS =====
.absa-metric-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  position: relative;
  border: 1px solid #e2e8f0;
  border-left: 4px solid #10b981; // Default green highlight
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  @media (max-width: 768px) {
    padding: 1.25rem;
  }

  @media (max-width: 640px) {
    padding: 1rem;
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
  }

  &:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  &:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .card-content {
    position: relative;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    @media (max-width: 640px) {
      margin-bottom: 0.75rem;
    }

    @media (max-width: 480px) {
      margin-bottom: 0.5rem;
    }

    .metric-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(16, 185, 129, 0.1); // Default light green background
      border: none;

      @media (max-width: 768px) {
        width: 44px;
        height: 44px;
      }

      @media (max-width: 640px) {
        width: 40px;
        height: 40px;
      }

      @media (max-width: 480px) {
        width: 36px;
        height: 36px;
      }

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        color: #10b981; // Default green color

        @media (max-width: 768px) {
          font-size: 1.375rem;
          width: 1.375rem;
          height: 1.375rem;
        }

        @media (max-width: 640px) {
          font-size: 1.25rem;
          width: 1.25rem;
          height: 1.25rem;
        }

        @media (max-width: 480px) {
          font-size: 1.125rem;
          width: 1.125rem;
          height: 1.125rem;
        }
      }
    }

    .metric-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.75rem;
      font-weight: 600;
      color: #6b7280;
      background: #f3f4f6;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;

      @media (max-width: 640px) {
        font-size: 0.625rem;
        padding: 0.2rem 0.4rem;
      }

      @media (max-width: 480px) {
        font-size: 0.5rem;
        padding: 0.15rem 0.3rem;
      }

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;

        @media (max-width: 640px) {
          font-size: 0.75rem;
          width: 0.75rem;
          height: 0.75rem;
        }

        @media (max-width: 480px) {
          font-size: 0.625rem;
          width: 0.625rem;
          height: 0.625rem;
        }
      }

      &.success {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }

      &.warning {
        color: #f59e0b;
        background: rgba(245, 158, 11, 0.1);
      }

      &.critical {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }
    }
  }

  .card-body {
    .metric-value {
      font-size: 2rem;
      font-weight: 700;
      color: #111827;
      line-height: 1;
      margin-bottom: 0.5rem;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }

      @media (max-width: 640px) {
        font-size: 1.5rem;
        margin-bottom: 0.375rem;
      }

      @media (max-width: 480px) {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
      }
    }

    .metric-label {
      font-size: 0.875rem;
      color: #374151;
      font-weight: 600;
      margin-bottom: 0.25rem;

      @media (max-width: 768px) {
        font-size: 0.8125rem;
      }

      @media (max-width: 640px) {
        font-size: 0.75rem;
      }

      @media (max-width: 480px) {
        font-size: 0.625rem;
      }
    }

    .metric-description {
      font-size: 0.875rem;
      color: #6b7280;
      font-weight: 400;

      @media (max-width: 768px) {
        font-size: 0.75rem;
      }

      @media (max-width: 640px) {
        font-size: 0.625rem;
      }

      @media (max-width: 480px) {
        font-size: 0.5rem;
      }
    }
  }
}

// ===== CARD-SPECIFIC STYLING =====
.absa-metric-card {
  // Issued Card - Blue
  &.issued-card {
    border-left-color: #3b82f6; // Blue
    .metric-icon {
      background: rgba(59, 130, 246, 0.1) !important; // Light blue background

      mat-icon {
        color: #3b82f6 !important; // Blue icon
      }
    }
  }

  // Completed Card - Green
  &.completed-card {
    border-left-color: #10b981; // Green
    .metric-icon {
      background: rgba(16, 185, 129, 0.1) !important; // Light green background

      mat-icon {
        color: #10b981 !important; // Green icon
      }
    }
  }

  // Rejected Card - Red
  &.rejected-card {
    border-left-color: #ef4444; // Red
    .metric-icon {
      background: rgba(239, 68, 68, 0.1) !important; // Light red background

      mat-icon {
        color: #ef4444 !important; // Red icon
      }
    }
  }

  // Pending Card - Orange/Yellow
  &.pending-card {
    border-left-color: #f59e0b; // Orange
    .metric-icon {
      background: rgba(245, 158, 11, 0.1) !important; // Light orange background

      mat-icon {
        color: #f59e0b !important; // Orange icon
      }
    }
  }

  // Approved Card - Green (similar to completed but slightly different shade)
  &.approved-card {
    border-left-color: #059669; // Darker green
    .metric-icon {
      background: rgba(5, 150, 105, 0.1) !important; // Light darker green background

      mat-icon {
        color: #059669 !important; // Darker green icon
      }
    }
  }

  // Total Requests Card - Purple
  &.total-requests-card {
    border-left-color: #8b5cf6; // Purple
    .metric-icon {
      background: rgba(139, 92, 246, 0.1) !important; // Light purple background

      mat-icon {
        color: #8b5cf6 !important; // Purple icon
      }
    }
  }
}
