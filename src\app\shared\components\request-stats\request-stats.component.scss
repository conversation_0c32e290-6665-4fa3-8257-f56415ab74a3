.dashboard-container {
  padding: 40px 20px;
  background-color: #faf9f6;
  font-family: Arial, sans-serif;

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;

    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      position: relative;
      min-height: 100px;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 10px;
        transform: translateY(-50%);
        width: 6px;
        height: 60%; 
        background-color: #b50232;
        border-radius: 4px;
      }

      .stat-label {
        color: #666;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
        line-height: 1.2;
        margin-left: 12px; 
      }

      .stat-number {
        font-size: 48px;
        font-weight: bold;
        color: #333;
        line-height: 1;
        margin-left: 12px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 20px 10px;

    .dashboard-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;

      .stat-card {
        .stat-number {
          font-size: 36px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .dashboard-grid {
      grid-template-columns: 1fr;
    }
  }
}
