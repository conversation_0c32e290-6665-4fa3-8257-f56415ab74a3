import { Component, Inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Subject, takeUntil } from 'rxjs';

import {
  NoteSeries,
  NoteDenomination,
  NOTE_SERIES_LABELS,
  DENOMINATION_LABELS
} from '../../../../shared/models/inventory.model';
import { InventoryService } from '../../services/inventory.service';

export interface NoteSeriesManagementData {
  // Add properties as needed
}

export interface NoteSeriesItem {
  id: string;
  name: string;
  displayName: string;
  description: string;
  totalNotes: number;
  totalValue: number;
  totalBatches: number;
  totalSingles: number;
  denominations: NoteDenomination[];
  isActive: boolean;
  isPredefined: boolean;
  icon: string;
  createdBy?: string;
  createdDate?: Date;
}

@Component({
  selector: 'app-note-series-management-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDividerModule,
    MatTooltipModule,
    MatSnackBarModule
  ],
  templateUrl: './note-series-management-modal.component.html',
  styleUrls: ['./note-series-management-modal.component.scss']
})
export class NoteSeriesManagementModalComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Component state
  isLoading = true;
  showAddForm = false;

  // Note series data
  noteSeriesList: NoteSeriesItem[] = [];

  // Add form data
  newSeriesName = '';
  newSeriesDescription = '';
  selectedDenominations: NoteDenomination[] = [];

  // Available denominations for selection
  availableDenominations = [
    { value: NoteDenomination.R10, label: DENOMINATION_LABELS[NoteDenomination.R10] },
    { value: NoteDenomination.R20, label: DENOMINATION_LABELS[NoteDenomination.R20] },
    { value: NoteDenomination.R50, label: DENOMINATION_LABELS[NoteDenomination.R50] },
    { value: NoteDenomination.R100, label: DENOMINATION_LABELS[NoteDenomination.R100] },
    { value: NoteDenomination.R200, label: DENOMINATION_LABELS[NoteDenomination.R200] }
  ];

  constructor(
    public dialogRef: MatDialogRef<NoteSeriesManagementModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: NoteSeriesManagementData,
    private inventoryService: InventoryService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadNoteSeriesData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load all note series data
   * Excludes fake notes since they are not real money series
   */
  private loadNoteSeriesData(): void {
    this.isLoading = true;

    try {
      // Get all note series from inventory service, excluding fake notes
      const allSeries = this.inventoryService.getAllNoteSeries()
        .filter(series => series.id !== NoteSeries.FAKE_NOTES);
      const cashInventory = this.inventoryService.getCashInventory();

      this.noteSeriesList = allSeries.map(series => {
        // Calculate totals for this series
        const seriesInventory = cashInventory.filter(item => item.noteSeries === series.id);
        const totalNotes = seriesInventory.reduce((sum, item) => sum + item.quantity, 0);
        const totalValue = seriesInventory.reduce((sum, item) => sum + item.value, 0);
        const totalBatches = Math.floor(totalNotes / 100);
        const totalSingles = totalNotes % 100;

        // Get denominations for this series
        const denominations = [...new Set(seriesInventory.map(item => item.denomination))];

        return {
          id: series.id,
          name: series.id,
          displayName: series.name,
          description: series.description || `${series.name} note series`,
          totalNotes,
          totalValue,
          totalBatches,
          totalSingles,
          denominations,
          isActive: totalNotes > 0,
          isPredefined: Object.values(NoteSeries).includes(series.id as NoteSeries),
          icon: this.getSeriesIcon(series.id),
          createdBy: (series as any).createdBy || 'system',
          createdDate: (series as any).createdDate || new Date()
        };
      });

      this.isLoading = false;
    } catch (error) {
      console.error('Error loading note series data:', error);
      this.isLoading = false;
      this.snackBar.open('Error loading note series data', 'Close', { duration: 3000 });
    }
  }

  /**
   * Show the add new series form
   */
  onShowAddForm(): void {
    this.showAddForm = true;
    this.resetAddForm();
  }

  /**
   * Cancel adding new series
   */
  onCancelAdd(): void {
    this.showAddForm = false;
    this.resetAddForm();
  }

  /**
   * Reset the add form
   */
  private resetAddForm(): void {
    this.newSeriesName = '';
    this.newSeriesDescription = '';
    this.selectedDenominations = [];
  }

  /**
   * Add a new custom note series
   */
  onAddNewSeries(): void {
    if (!this.newSeriesName.trim() || this.selectedDenominations.length === 0) {
      this.snackBar.open('Please fill in all required fields', 'Close', { duration: 3000 });
      return;
    }

    const success = this.inventoryService.addCustomNoteSeries(
      this.newSeriesName.trim(),
      this.newSeriesDescription.trim() || `Custom ${this.newSeriesName.trim()} series`,
      this.selectedDenominations,
      'user'
    );

    if (success) {
      this.snackBar.open(`Successfully added ${this.newSeriesName} series`, 'Close', { duration: 3000 });
      this.showAddForm = false;
      this.resetAddForm();
      this.loadNoteSeriesData(); // Refresh the list
    } else {
      this.snackBar.open('Failed to add series. Name may already exist.', 'Close', { duration: 3000 });
    }
  }

  /**
   * View details of a specific series
   */
  onViewSeriesDetails(seriesItem: NoteSeriesItem): void {
    this.dialogRef.close({
      action: 'view-details',
      series: seriesItem.name,
      seriesName: seriesItem.displayName
    });
  }

  /**
   * Remove a custom series
   */
  onRemoveSeries(seriesItem: NoteSeriesItem): void {
    if (seriesItem.isPredefined) {
      this.snackBar.open('Cannot remove predefined series', 'Close', { duration: 3000 });
      return;
    }

    if (seriesItem.totalNotes > 0) {
      this.snackBar.open('Cannot remove series with existing inventory', 'Close', { duration: 3000 });
      return;
    }

    const success = this.inventoryService.removeCustomNoteSeries(seriesItem.id);
    if (success) {
      this.snackBar.open(`Successfully removed ${seriesItem.displayName}`, 'Close', { duration: 3000 });
      this.loadNoteSeriesData(); // Refresh the list
    } else {
      this.snackBar.open('Failed to remove series', 'Close', { duration: 3000 });
    }
  }

  /**
   * Format currency values
   */
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(value);
  }

  /**
   * Format number values
   */
  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-ZA').format(value);
  }

  /**
   * Close the modal
   */
  onClose(): void {
    this.dialogRef.close();
  }

  /**
   * Cancel and close modal (legacy method name)
   */
  onCancel(): void {
    this.onClose();
  }

  /**
   * Get icon for series based on series ID
   */
  private getSeriesIcon(seriesId: string): string {
    switch (seriesId) {
      case NoteSeries.MANDELA:
        return 'account_balance';
      case NoteSeries.BIG_5:
        return 'nature';
      case NoteSeries.COMMEMORATIVE:
        return 'star';
      case NoteSeries.V6:
        return 'new_releases';
      case NoteSeries.FAKE_NOTES:
        return 'school';
      default:
        return 'category';
    }
  }
}
