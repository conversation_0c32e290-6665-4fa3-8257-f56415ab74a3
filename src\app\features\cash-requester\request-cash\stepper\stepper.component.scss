/* Container */
.custom-container {
  max-width: 800px;
  margin: 1rem auto;
  padding: 1rem;
  // display: flex;
  // flex-direction: column;
  max-height: 800px;
}

.form-step-content {
  flex: 1;
  min-height: 400px;
}

/* Header + Step Info */
.form-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 2rem;
}

.new-cash-request {
  color: #131010;
  font-family: "Roboto", sans-serif;
  font-size: 36px;
  line-height: 44px;
  margin: 0;
}

.step-1-of-3 {
  color: #4c4949;
  font-family: "Source Sans Pro", sans-serif;
  font-size: 24px;
  line-height: 28px;
  margin: 0;
}

/* Progress bar container */
.progress-indicator-base {
  background: #c7c5c5;
  border-radius: 100px;
  height: 4px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  background: #dc0037;
  height: 100%;
  width: 0%;
  transition: width 0.3s ease-in-out;
}

.stepper-buttons {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 100px;
  margin-left: 65%;
  padding: 7rem 0;
}

button.block-shape {
  padding: 8px 40px;
  width: 120px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  flex-shrink: 0;
  border: none;
  cursor: pointer;
}

.block-shape.secondary {
  background: var(--brand-serene, #ffffff);
  border: 1px solid var(--brand-hope, #95052a) !important;
  color: #95052a;
}

.block-shape.primary {
  background: var(--brand-hope, #95052a);
  color: var(--brand-serene, #ffffff);
}

.text-label {
  font-family: "SourceSansPro-SemiBold", sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 14px;
  text-align: center;
  color: inherit !important;
}