<div class="request-cash-container">
  <h2 class="title">Request Cash</h2>
  <div class="content">
    <mat-horizontal-stepper class="mat-elevation-z5" labelPosition="bottom" [linear]="true">

      <mat-step errorMessage="Department not selected">
        <ng-template matStepLabel>New Cash Request</ng-template>

        <app-request-cash-step-1></app-request-cash-step-1>

        <div class="stepper-buttons">
          <button mat-raised-button color="primary" matStepperNext>
            Next
          </button>
        </div>
      </mat-step>

      <mat-step>
        <ng-template matStepLabel>Bank Note Selection</ng-template>

        <app-request-cash-step-2></app-request-cash-step-2>

        <div class="stepper-buttons">
          <button mat-raised-button matStepperPrevious>Back</button>
          <button mat-raised-button color="primary" matStepperNext>
            Next
          </button>
        </div>
      </mat-step>

      <mat-step>
        <ng-template matStepLabel>Submit Request</ng-template>

        <app-request-cash-step-3></app-request-cash-step-3>

        <div class="stepper-buttons">
          <button mat-raised-button matStepperPrevious>Back</button>
          <button matButton color="primary" matStepperNext>
            Submit Request
          </button>
        </div>
      </mat-step>


    </mat-horizontal-stepper>
  </div>
</div>
