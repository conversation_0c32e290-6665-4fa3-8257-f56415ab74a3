import { Injectable } from '@angular/core';
import { Request } from '../../shared/models/request.model';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';
import { CashRequest } from '../../shared/models/cashrequest.model';


@Injectable({
  providedIn: 'root'
})
export class RequestService {

  private apiUrl = 'http://localhost:3000/requests';

   constructor(private http: HttpClient) {}

  getAllRequests(): Observable<CashRequest[]> {
    return this.http.get<CashRequest[]>(this.apiUrl);
  }

  getRequestsByStatus(status: string): Observable<CashRequest[]> {
    return this.http.get<CashRequest[]>(`${this.apiUrl}?status=${status}`);
  }

<<<<<<< HEAD
  getRequestsByUsername(username: string): Observable<CashRequest[]> {
    return this.http.get<CashRequest[]>(`${this.apiUrl}?requesterName=${username}`);
  }

=======
  
    
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
  }
