import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { AuthService } from '../../login/auth.service';
import { LandingboardComponent } from '../../../shared/components/dashboard/landing-board.component';
import { RequestStatsComponent } from '../../../shared/components/request-stats/request-stats.component';

@Component({
  selector: 'app-cash-issuer-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    LandingboardComponent,
    RequestStatsComponent
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class CashIssuerDashboardComponent implements OnInit {

  constructor(
    private router: Router,
    public authService: AuthService
  ) {}

  ngOnInit(): void {
    // Initialize component
  }

  onManageRequests(): void {
    this.router.navigate(['/cash-issuer/manage-requests']);
  }

  onInventoryManagement(): void {
    this.router.navigate(['/cash-issuer/inventory-management']);
  }
}
