import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { AddCashModalComponent, AddCashData } from './add-cash-modal.component';


describe('AddCashModalComponent', () => {
  let component: AddCashModalComponent;
  let fixture: ComponentFixture<AddCashModalComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddCashModalComponent>>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;

  const mockData: AddCashData = {

  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockSnackBar = jasmine.createSpyObj('MatSnackBar', ['open']);

    await TestBed.configureTestingModule({
      imports: [
        AddCashModalComponent,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockData },
        { provide: MatSnackBar, useValue: mockSnackBar }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AddCashModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
});
