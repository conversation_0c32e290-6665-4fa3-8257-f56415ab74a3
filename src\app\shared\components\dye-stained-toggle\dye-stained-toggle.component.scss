// ===== CREATIVE DYE STAINED TOGGLE COMPONENT =====

// ===== RESPONSIVE MIXINS =====
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: 1023px) {
    @content;
  }
}

// ===== MAIN CONTAINER =====
.dye-stained-toggle-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
  backdrop-filter: none;
  position: relative;
  overflow: visible;
  min-height: auto;
}

// ===== CUSTOM TOGGLE WRAPPER =====
.custom-toggle-wrapper {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 6px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
    filter: grayscale(0.3);
  }

  // Animated background gradient
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(212, 175, 55, 0.1),
      transparent
    );
    transition: left 0.6s ease;
    z-index: 0;
  }

  &:hover::before {
    left: 100%;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

// ===== PAINT BRUSH ICON CONTAINER =====
.paint-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;

  .paint-brush-icon {
    color: var(--absa-gold);
    font-size: 16px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    transition: all 0.4s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }

  .paint-drip {
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 8px;
    background: var(--absa-red);
    border-radius: 0 0 50% 50%;
    opacity: 0;
    transition: all 0.4s ease;

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 2px;
      height: 2px;
      background: var(--absa-red);
      border-radius: 50%;
    }

    &.active {
      opacity: 1;
      animation: drip 2s ease-in-out infinite;
    }
  }

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);

    .paint-brush-icon {
      color: var(--absa-white);
      transform: rotate(15deg);
    }
  }
}

// ===== TOGGLE LABELS =====
.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;

  &:hover {
    color: var(--absa-red);
  }

  .toggle-icon {
    font-size: 1.125rem;
    width: 1.125rem;
    height: 1.125rem;
    color: var(--absa-red);
    transition: all 0.2s ease;
  }

  .label-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--absa-gray-700);
    letter-spacing: 0.025em;
    transition: all 0.2s ease;
  }

  &.label-before {
    order: 1;
  }

  &.label-after {
    order: 3;
  }

  @include mobile {
    .label-text {
      font-size: 0.8rem;
    }

    .toggle-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
  }
}

// ===== MATERIAL SLIDE TOGGLE CUSTOMIZATION =====
.dye-stained-toggle {
  order: 2;
  display: flex;
  align-items: center;

  ::ng-deep {
    .mdc-switch {
      width: 52px;
      height: 32px;

      .mdc-switch__track {
        width: 52px;
        height: 32px;
        border-radius: 16px;
        background-color: var(--absa-gray-300);
        border: none;
        opacity: 1;
        transition: all 0.3s ease;
      }

      .mdc-switch__handle-track {
        width: 52px;
        height: 32px;
      }

      .mdc-switch__handle {
        width: 28px;
        height: 28px;
        left: 2px;
        top: 2px;
        transform: translateX(0);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        .mdc-switch__shadow {
          width: 28px;
          height: 28px;
          box-shadow: var(--absa-shadow-sm);
        }

        .mdc-switch__ripple {
          width: 48px;
          height: 48px;
          top: -10px;
          left: -10px;
        }

        // Handle background
        &::before {
          content: '';
          position: absolute;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background: var(--absa-white);
          box-shadow: var(--absa-shadow-sm);
          z-index: 1;
        }

        // Icon positioning within handle
        mat-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 16px;
          width: 16px;
          height: 16px;
          color: var(--absa-gray-500);
          z-index: 2;
          transition: all 0.3s ease;
          pointer-events: none;
        }
      }

      // Icons styling - Material Design 3 approach
      .mdc-switch__icons {
        width: 28px;
        height: 28px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;

        .mdc-switch__icon {
          width: 16px;
          height: 16px;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &--on {
            color: var(--absa-red);
            opacity: 0;
          }

          &--off {
            color: var(--absa-gray-500);
            opacity: 1;
          }
        }
      }



      // Checked state
      &.mdc-switch--checked {
        .mdc-switch__track {
          background-color: var(--absa-red-200);
        }

        .mdc-switch__handle {
          transform: translateX(20px);

          .mdc-switch__shadow {
            box-shadow: var(--absa-shadow-md);
          }

          &::before {
            background: var(--absa-white);
          }

          // Icon styling for checked state
          mat-icon {
            color: var(--absa-red);
          }
        }

        .mdc-switch__icons {
          .mdc-switch__icon--on {
            opacity: 1;
            color: var(--absa-red);
          }

          .mdc-switch__icon--off {
            opacity: 0;
          }
        }


      }

      // Hover states
      &:hover {
        .mdc-switch__handle .mdc-switch__ripple {
          background-color: rgba(145, 29, 47, 0.04);
        }

        &.mdc-switch--checked .mdc-switch__handle .mdc-switch__ripple {
          background-color: rgba(145, 29, 47, 0.08);
        }
      }

      // Focus states
      &:focus-within {
        .mdc-switch__handle .mdc-switch__ripple {
          background-color: rgba(145, 29, 47, 0.12);
        }
      }
    }
  }

  @include mobile {
    ::ng-deep .mdc-switch {
      width: 44px;
      height: 28px;

      .mdc-switch__track {
        width: 44px;
        height: 28px;
        border-radius: 14px;
      }

      .mdc-switch__handle {
        width: 24px;
        height: 24px;
        left: 2px;
        top: 2px;

        &::before {
          width: 24px;
          height: 24px;
        }

        mat-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }

      .mdc-switch__icons {
        width: 24px;
        height: 24px;

        .mdc-switch__icon {
          width: 16px;
          height: 16px;
        }
      }

      &.mdc-switch--checked .mdc-switch__handle {
        transform: translateX(16px);
      }
    }
  }
}

// ===== CUSTOM TOGGLE SWITCH =====
.custom-toggle-switch {
  position: relative;
  width: 90px;
  height: 38px;
  cursor: pointer;
  user-select: none;
  transition: all 0.4s ease;

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
    border-radius: 19px;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  // Toggle Track
  .toggle-track {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 19px;
    overflow: hidden;
    transition: all 0.4s ease;

    .track-fill {
      position: absolute;
      top: 0;
      left: 0;
      width: 0%;
      height: 100%;
      background: linear-gradient(135deg, var(--absa-red) 0%, var(--absa-gold) 100%);
      transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 17px;

      &.active {
        width: 100%;
      }
    }

    .track-labels {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px;
      z-index: 1;

      .track-label {
        font-size: 8px;
        font-weight: 700;
        letter-spacing: 0.1em;
        transition: all 0.4s ease;

        &.off-label {
          color: rgba(255, 255, 255, 0.7);
        }

        &.on-label {
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }

  // Toggle Handle
  .toggle-handle {
    position: absolute;
    top: 3px;
    left: 3px;
    width: 32px;
    height: 32px;
    background: var(--absa-white);
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
    box-shadow:
      0 3px 8px rgba(0, 0, 0, 0.12),
      0 1px 3px rgba(0, 0, 0, 0.08);

    .handle-inner {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 26px;
      height: 26px;
      border-radius: 50%;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.4s ease;

      .handle-icon {
        font-size: 14px;
        color: var(--absa-gray-500);
        transition: all 0.4s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 14px;
        height: 14px;
        line-height: 1;
      }
    }

    .handle-glow {
      position: absolute;
      top: -2px;
      left: -2px;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(212, 175, 55, 0.3) 0%, transparent 70%);
      opacity: 0;
      transition: all 0.4s ease;

      &.active {
        opacity: 1;
        animation: pulse 2s ease-in-out infinite;
      }
    }

    &.checked {
      transform: translateX(52px);

      .handle-inner {
        background: linear-gradient(135deg, var(--absa-red) 0%, var(--absa-gold) 100%);

        .handle-icon {
          color: var(--absa-white);
        }
      }
    }
  }

  &.checked {
    .toggle-track {
      background: rgba(212, 175, 55, 0.2);
      border-color: var(--absa-gold);

      .track-labels {
        .off-label {
          color: rgba(255, 255, 255, 0.3);
        }

        .on-label {
          color: var(--absa-white);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
}

// ===== TOGGLE LABEL CONTAINER =====
.toggle-label-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2rem;

  .toggle-label-text {
    font-size: 0.8rem;
    font-weight: 700;
    color: var(--absa-white);
    letter-spacing: 0.05em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
  }

  .label-underline {
    width: 0;
    height: 1.5px;
    background: var(--absa-gold);
    border-radius: 1px;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &.active {
      width: 100%;
    }
  }
}

// ===== STATUS BADGE =====
.status-badge {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.3rem 0.6rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  transition: all 0.4s ease;

  .badge-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transition: all 0.4s ease;
  }

  .badge-text {
    font-size: 0.7rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.05em;
    transition: all 0.4s ease;
  }

  &.active {
    background: rgba(212, 175, 55, 0.2);
    border-color: var(--absa-gold);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);

    .badge-dot {
      background: var(--absa-gold);
      box-shadow: 0 0 12px rgba(212, 175, 55, 0.6);
      animation: pulse 2s ease-in-out infinite;
    }

    .badge-text {
      color: var(--absa-gold);
    }
  }
}

// ===== ANIMATIONS =====
@keyframes drip {
  0%, 100% {
    transform: translateX(-50%) scaleY(1);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scaleY(1.2);
    opacity: 0.8;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

// ===== RESPONSIVE DESIGN =====
@include tablet {
  .custom-toggle-wrapper {
    gap: 0.75rem;
    padding: 0.625rem 0.875rem;
    border-radius: 14px;
  }

  .paint-icon-container {
    width: 28px;
    height: 28px;

    .paint-brush-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }

  .custom-toggle-switch {
    width: 75px;
    height: 32px;

    .toggle-track {
      border-radius: 16px;

      .track-fill {
        border-radius: 14px;
      }

      .track-labels {
        padding: 0 10px;

        .track-label {
          font-size: 7px;
        }
      }
    }

    .toggle-handle {
      width: 26px;
      height: 26px;
      top: 3px;
      left: 3px;

      .handle-inner {
        width: 20px;
        height: 20px;

        .handle-icon {
          font-size: 12px;
          width: 12px;
          height: 12px;
        }
      }

      &.checked {
        transform: translateX(43px);
      }
    }
  }

  .toggle-label-container .toggle-label-text {
    font-size: 0.75rem;
  }

  .status-badge {
    padding: 0.25rem 0.5rem;

    .badge-dot {
      width: 5px;
      height: 5px;
    }

    .badge-text {
      font-size: 0.65rem;
    }
  }
}

@include mobile {
  .custom-toggle-wrapper {
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .paint-icon-container {
    width: 24px;
    height: 24px;

    .paint-brush-icon {
      font-size: 12px;
      width: 12px;
      height: 12px;
    }

    .paint-drip {
      width: 3px;
      height: 6px;
      bottom: -4px;

      &::before {
        width: 1.5px;
        height: 1.5px;
        top: -1.5px;
      }
    }
  }

  .custom-toggle-switch {
    width: 65px;
    height: 28px;

    .toggle-track {
      border-radius: 14px;

      .track-fill {
        border-radius: 12px;
      }

      .track-labels {
        padding: 0 8px;

        .track-label {
          font-size: 6px;
        }
      }
    }

    .toggle-handle {
      width: 22px;
      height: 22px;
      top: 3px;
      left: 3px;

      .handle-inner {
        width: 18px;
        height: 18px;

        .handle-icon {
          font-size: 10px;
          width: 10px;
          height: 10px;
        }
      }

      .handle-glow {
        width: 26px;
        height: 26px;
      }

      &.checked {
        transform: translateX(37px);
      }
    }
  }

  .toggle-label-container .toggle-label-text {
    font-size: 0.7rem;
  }

  .status-badge {
    padding: 0.2rem 0.4rem;

    .badge-dot {
      width: 4px;
      height: 4px;
    }

    .badge-text {
      font-size: 0.6rem;
    }
  }
}

// ===== STATE INDICATOR =====
.state-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 10px;
  background: var(--absa-gray-100);
  border: 1px solid var(--absa-gray-200);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  min-width: 60px;

  .indicator-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--absa-gray-400);
    transition: all 0.3s ease;
  }

  .state-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--absa-gray-600);
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
  }

  &.active {
    background: rgba(145, 29, 47, 0.1);
    border-color: var(--absa-red-200);

    .indicator-dot {
      background: var(--absa-red);
      box-shadow: 0 0 8px rgba(145, 29, 47, 0.3);
    }

    .state-text {
      color: var(--absa-red);
    }
  }

  @include mobile {
    padding: 0.2rem 0.4rem;

    .state-text {
      font-size: 0.7rem;
    }

    .indicator-dot {
      width: 5px;
      height: 5px;
    }
  }
}

// ===== ANIMATIONS =====
@keyframes toggleSlide {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(24px);
  }
}

@keyframes indicatorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

// Apply pulse animation to active indicator
.state-indicator.active .indicator-dot {
  animation: indicatorPulse 2s ease-in-out infinite;
}
