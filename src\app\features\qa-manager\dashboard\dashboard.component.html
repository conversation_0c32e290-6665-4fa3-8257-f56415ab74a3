<div class="qa-dashboard-container">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <h1>QA Manager Dashboard</h1>
  </div>

  <!-- Main Dashboard Content -->
  <div class="dashboard-content">
    <!-- Unified Welcome and Quick Actions Container -->
    <div class="unified-content-card">
      <!-- Welcome Section -->
      <app-landingboard></app-landingboard>

      <!-- Quick Actions Section -->
      <div class="quick-actions-section">
        <div class="section-header">
          <div class="section-title">
            <mat-icon>flash_on</mat-icon>
            <h2>Quick Actions</h2>
          </div>
        </div>

        <div class="actions-grid">
          <!-- Manage Inventory Card -->
          <mat-card class="action-card primary" (click)="onManageInventory()">
            <div class="card-background"></div>
            <mat-card-content>
              <div class="card-icon">
                <mat-icon>inventory_2</mat-icon>
              </div>
              <div class="card-content">
                <h3>Manage Inventory</h3>
                <p>View and manage cash, coins, and note series inventory</p>
              </div>
              <div class="card-arrow">
                <mat-icon>arrow_forward</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Generate Audit Report Card -->
          <mat-card class="action-card secondary" (click)="onGenerateAuditReport()">
            <div class="card-background"></div>
            <mat-card-content>
              <div class="card-icon">
                <mat-icon>assessment</mat-icon>
              </div>
              <div class="card-content">
                <h3>Generate Audit Report</h3>
                <p>Create comprehensive inventory audit reports</p>
              </div>
              <div class="card-arrow">
                <mat-icon>arrow_forward</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- View System Logs Card -->
          <mat-card class="action-card tertiary" (click)="onViewSystemLogs()">
            <div class="card-background"></div>
            <mat-card-content>
              <div class="card-icon">
                <mat-icon>visibility</mat-icon>
              </div>
              <div class="card-content">
                <h3>View System Logs</h3>
                <p>Monitor system activities and audit trails</p>
                <span class="coming-soon-badge">Coming Soon</span>
              </div>
              <div class="card-arrow">
                <mat-icon>arrow_forward</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>

    <!-- Request Stats Section -->
    <app-request-stats></app-request-stats>
  </div>
</div>
