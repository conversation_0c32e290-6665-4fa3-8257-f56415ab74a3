import { Component, Input, Output, EventEmitter, forwardRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

// Angular Material Imports
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-dye-stained-toggle',
  standalone: true,
  imports: [
    CommonModule,
    MatSlideToggleModule,
    MatIconModule,
    MatTooltipModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DyeStainedToggleComponent),
      multi: true
    }
  ],
  templateUrl: './dye-stained-toggle.component.html',
  styleUrls: ['./dye-stained-toggle.component.scss']
})
export class DyeStainedToggleComponent implements ControlValueAccessor, OnInit {
  @Input() label: string = 'Dye Stained Mode';
  @Input() disabled: boolean = false;
  @Input() color: 'primary' | 'accent' | 'warn' = 'primary';
  @Input() labelPosition: 'before' | 'after' = 'after';
  @Input() showIcon: boolean = true;
  @Input() tooltipText: string = 'Toggle to display dye stained note series names';
  @Input() checked: boolean = false;

  @Output() toggleChange = new EventEmitter<boolean>();

  private _internalChecked: boolean = false;
  private _onChange = (value: boolean) => {};
  private _onTouched = () => {};

  ngOnInit(): void {
    // Initialize internal state with input value
    this._internalChecked = this.checked;
  }

  get internalChecked(): boolean {
    return this._internalChecked;
  }

  set internalChecked(value: boolean) {
    if (this._internalChecked !== value) {
      this._internalChecked = value;
      this.checked = value; // Update input property
      this._onChange(value);
      this.toggleChange.emit(value);
    }
  }

  // ControlValueAccessor implementation
  writeValue(value: boolean): void {
    this._internalChecked = value || false;
    this.checked = this._internalChecked;
  }

  registerOnChange(fn: (value: boolean) => void): void {
    this._onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this._onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Event handlers
  onToggleChange(event: any): void {
    this._onTouched();
    this.internalChecked = event.checked;
  }

  onToggleClick(): void {
    if (!this.disabled) {
      this.internalChecked = !this.internalChecked;
    }
  }

  // Accessibility
  getAriaLabel(): string {
    return `${this.label} toggle switch, currently ${this.internalChecked ? 'enabled' : 'disabled'}`;
  }

  getToggleId(): string {
    return `dye-stained-toggle-${Math.random().toString(36).substr(2, 9)}`;
  }
}
