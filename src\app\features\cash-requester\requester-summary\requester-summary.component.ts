import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { RequestService } from '../../../core/services/request.service';
import { AuthService } from '../../login/auth.service';
import { User } from '../../../shared/models/user.model';
import { CashRequest } from '../../../shared/models/cashrequest.model';

@Component({
  selector: 'app-cash-requests',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatCardModule,

  ],
  templateUrl: './requester-summary.component.html',
  styleUrls: ['./requester-summary.component.scss']
})
export class RequesterSummaryComponent implements OnInit {

  dataSource: CashRequest[] = [];
   userData?: User;
 displayedColumns: string[] = [
  'id',
  'createdAt',
  'cashTotal',
  'requestStatus',
  'issuedTo',
  'action'
];

  currentStatus = 'All';



  constructor(private requestService: RequestService,public authService: AuthService) {}

  ngOnInit() {
    this.authService.currentUser$.subscribe((user) => {
      if (user) {
        this.userData = user;
        this.filterByUsername(user.name);
      }
    });
    this.fetchRequests();
    console.log('Component initialized, fetching requests');
    console.log('Kamogelo'+ this.filterByUsername(this.userData?.name || ''));
  }

  fetchRequests(status: string = 'All') {
    this.currentStatus = status;
    if (status === 'All') {
      this.requestService.getAllRequests().subscribe((data: CashRequest[]) => this.dataSource = data);
    } else {
      this.requestService.getRequestsByStatus(status).subscribe((data: CashRequest[]) => this.dataSource = data);
    }
  }

  filterByUsername(username: string) {
    console.log(`Filtering requests for username: ${username}`);
    this.requestService.getRequestsByUsername(username).subscribe((data: CashRequest[]) => this.dataSource = data);
    console.log(`Filtered requests for username: ${username}`);
  }
}
