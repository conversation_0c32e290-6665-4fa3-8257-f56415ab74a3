<div class="absa-inventory-container">
  <!-- Simple Header Section -->
  <header class="inventory-header">
    <h1 class="page-title">Inventory Overview</h1>
  </header>

  <main class="absa-main-content">
    <!-- Enhanced Summary Dashboard -->
    <section
      class="absa-dashboard-section"
      *ngIf="inventorySummary"
      aria-label="Inventory Summary"
    >
      <div class="dashboard-grid">
        <!-- Total Value Card with Breakdown -->
        <article
          class="absa-metric-card value-card total-inventory"
          tabindex="0"
          role="button"
          aria-label="Total inventory value across all categories"
        >
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>account_balance_wallet</mat-icon>
              </div>
            </header>
            <div class="card-body">
              <div
                class="metric-value"
                [attr.aria-label]="
                  'Total inventory value: ' + formatCurrency(inventorySummary.totalValue)
                "
              >
                {{ formatCurrency(inventorySummary.totalValue) }}
              </div>
              <div class="metric-label">Total Inventory Value</div>
              <div class="metric-description">All categories combined</div>
            </div>
          </div>
        </article>


        <!-- Low Stock Alerts Card -->
        <article
          class="absa-metric-card alerts-card"
          [class.critical]="inventorySummary.lowStockAlerts.length > 5"
          tabindex="0"
          role="button"
          [attr.aria-label]="
            'Low stock alerts: ' + inventorySummary.lowStockAlerts.length
          "
        >
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>warning</mat-icon>
              </div>
              <div
                class="metric-trend"
                [class.warning]="inventorySummary.lowStockAlerts.length > 0"
              >
                <mat-icon>priority_high</mat-icon>
                <span>{{
                  inventorySummary.lowStockAlerts.length > 0 ? "Alert" : "OK"
                }}</span>
              </div>
            </header>
            <div class="card-body">
              <div class="metric-value">
                {{ inventorySummary.lowStockAlerts.length }}
              </div>
              <div class="metric-label">Low Stock Alerts</div>
              <div class="metric-description">Require attention</div>
            </div>
          </div>
        </article>

        <!-- Out of Stock Alerts Card -->
        <article
          class="absa-metric-card out-of-stock-card"
          [class.critical]="inventorySummary.outOfStockAlerts.length > 0"
          tabindex="0"
          role="button"
          [attr.aria-label]="
            'Out of stock alerts: ' + inventorySummary.outOfStockAlerts.length
          "
        >
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>error</mat-icon>
              </div>
              <div
                class="metric-trend"
                [class.critical]="inventorySummary.outOfStockAlerts.length > 0"
              >
                <mat-icon>block</mat-icon>
                <span>{{
                  inventorySummary.outOfStockAlerts.length > 0
                    ? "Critical"
                    : "OK"
                }}</span>
              </div>
            </header>
            <div class="card-body">
              <div class="metric-value">
                {{ inventorySummary.outOfStockAlerts.length }}
              </div>
              <div class="metric-label">Out of Stock</div>
              <div class="metric-description">Immediate action required</div>
            </div>
          </div>
        </article>

        <!-- Series Count Card -->
        <article
          class="absa-metric-card series-card"
          tabindex="0"
          role="button"
          aria-label="Available note series - Click to manage"
          (click)="onManageNoteSeries()"
          (keydown.enter)="onManageNoteSeries()"
          (keydown.space)="onManageNoteSeries()"
        >
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>category</mat-icon>
              </div>
              <div class="metric-trend positive">
                <mat-icon>check_circle</mat-icon>
                <span>Active</span>
              </div>
            </header>
            <div class="card-body">
              <div class="metric-value">{{ noteSeriesCount }}</div>
              <div class="metric-label">Note Series</div>
              <div class="metric-description">Available types</div>
            </div>
          </div>
        </article>
      </div>
    </section>

    <!-- Detailed Inventory Section -->
    <app-detailed-inventory
      [title]="getInventoryTitle()"
      [seriesData]="seriesData"
      [coinInventory]="groupedCoinInventory"
      [showExportButton]="true"
      [showCoinTab]="false"
      [fakeNotesSeriesIds]="fakeNotesSeriesIds"
      [stockThreshold]="50"
      [isDyeStainedMode]="isDyeStainedMode"
      [isCoinsMode]="currentInventoryMode === InventoryMode.COINS"
      (addCash)="onAddCash($event.seriesId, $event.denomination)"
      (removeCash)="onRemoveCash($event.seriesId, $event.denomination, $event.maxQuantity)"
      (addCoin)="onAddCoinWithSeries($event.denomination, $event.seriesId)"
      (removeCoin)="onRemoveCoinWithSeries($event.denomination, $event.seriesId)"
      (exportAuditReport)="onExportAuditReport()"
      (tabChange)="onTabChange($event)"
      (dyeStainedModeChange)="onDyeStainedModeChange($event)"
    ></app-detailed-inventory>


  </main>

  <!-- Inventory Mode Toggle FAB -->
  <div class="inventory-mode-fab-container">
    <button
      mat-fab
      class="inventory-mode-fab"
      [class.normal-mode]="currentInventoryMode === 'normal'"
      [class.dye-stained-mode]="currentInventoryMode === 'dye_stained'"
      [class.coins-mode]="currentInventoryMode === 'coins'"
      (click)="onToggleInventoryMode()"
      [matTooltip]="getInventoryModeTooltip()"
      matTooltipPosition="left"
      [attr.aria-label]="getInventoryModeAriaLabel()"
    >
      <mat-icon>{{ getInventoryModeIcon() }}</mat-icon>
    </button>
  </div>
</div>


