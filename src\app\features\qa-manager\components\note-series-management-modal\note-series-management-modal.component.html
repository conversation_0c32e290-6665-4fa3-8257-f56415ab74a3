<div class="note-series-management-modal">
  <!-- <PERSON><PERSON> -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>category</mat-icon>
      </div>
      <div class="header-text">
        <h2>Note Series Management</h2>
        <p>Manage your note series inventory and configurations</p>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onClose()" matTooltip="Close">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-divider></mat-divider>

  <!-- Modal Content -->
  <div class="modal-content">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-icon class="loading-icon">hourglass_empty</mat-icon>
      <p>Loading note series data...</p>
    </div>

    <!-- Main Content -->
    <div *ngIf="!isLoading" class="content-container">
      <!-- Action Bar -->
      <div class="action-bar">
        <div class="action-info">
          <h3>Available Note Series ({{ noteSeriesList.length }})</h3>
          <p>Manage your note series inventory and add new series types</p>
        </div>
        <button mat-raised-button
                color="primary"
                class="add-series-btn"
                (click)="onShowAddForm()"
                *ngIf="!showAddForm">
          <mat-icon>add_circle</mat-icon>
          Add New Series
        </button>
      </div>

      <!-- Add New Series Form -->
      <mat-card *ngIf="showAddForm" class="add-series-form">
        <mat-card-header>
          <mat-icon mat-card-avatar>add_circle</mat-icon>
          <mat-card-title>Add New Note Series</mat-card-title>
          <mat-card-subtitle>Create a custom note series for your inventory</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="form-grid">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Series Name</mat-label>
              <input matInput
                     [(ngModel)]="newSeriesName"
                     placeholder="Enter series name"
                     maxlength="50">
              <mat-icon matSuffix>edit</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput
                        [(ngModel)]="newSeriesDescription"
                        placeholder="Enter series description"
                        rows="3"
                        maxlength="200"></textarea>
              <mat-icon matSuffix>description</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Available Denominations</mat-label>
              <mat-select [(ngModel)]="selectedDenominations" multiple>
                <mat-option *ngFor="let denom of availableDenominations" [value]="denom.value">
                  {{ denom.label }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>monetization_on</mat-icon>
            </mat-form-field>
          </div>
        </mat-card-content>

        <mat-card-actions align="end">
          <button mat-button (click)="onCancelAdd()">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          <button mat-raised-button
                  color="primary"
                  (click)="onAddNewSeries()"
                  [disabled]="!newSeriesName.trim() || selectedDenominations.length === 0">
            <mat-icon>save</mat-icon>
            Add Series
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Note Series Grid -->
      <div class="series-grid">
        <mat-card *ngFor="let seriesItem of noteSeriesList"
                  class="series-card"
                  [class.active]="seriesItem.isActive"
                  [class.inactive]="!seriesItem.isActive">

          <!-- Series Header -->
          <mat-card-header>
            <div mat-card-avatar class="series-avatar">
              <mat-icon>{{ seriesItem.icon }}</mat-icon>
            </div>
            <mat-card-title>{{ seriesItem.name }}</mat-card-title>
            <mat-card-subtitle>{{ seriesItem.description }}</mat-card-subtitle>

            <div class="series-status">
              <span class="status-badge" [class.active]="seriesItem.isActive" [class.inactive]="!seriesItem.isActive">
                <mat-icon>{{ seriesItem.isActive ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                {{ seriesItem.isActive ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </mat-card-header>

          <!-- Series Content -->
          <mat-card-content>
            <div class="series-stats">
              <div class="stat-item">
                <div class="stat-icon">
                  <mat-icon>receipt_long</mat-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatNumber(seriesItem.totalNotes) }}</div>
                  <div class="stat-label">Total Notes</div>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon">
                  <mat-icon>account_balance_wallet</mat-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatCurrency(seriesItem.totalValue) }}</div>
                  <div class="stat-label">Total Value</div>
                </div>
              </div>
            </div>

            <!-- Denominations -->
            <div class="denominations-section" *ngIf="seriesItem.denominations.length > 0">
              <h4>Available Denominations</h4>
              <div class="denomination-chips">
                <span *ngFor="let denom of seriesItem.denominations"
                      class="denomination-chip">
                  R{{ denom }}
                </span>
              </div>
            </div>

            <div *ngIf="seriesItem.denominations.length === 0" class="no-denominations">
              <mat-icon>info</mat-icon>
              <span>No denominations configured</span>
            </div>
          </mat-card-content>

          <!-- Series Actions -->
          <mat-card-actions align="end">
            <button mat-button
                    (click)="onViewSeriesDetails(seriesItem)"
                    matTooltip="View detailed inventory">
              <mat-icon>visibility</mat-icon>
              View Details
            </button>

            <button mat-button
                    color="warn"
                    (click)="onRemoveSeries(seriesItem)"
                    *ngIf="!seriesItem.isPredefined"
                    [disabled]="seriesItem.totalNotes > 0"
                    [matTooltip]="seriesItem.totalNotes > 0 ? 'Cannot remove series with existing inventory' : 'Remove custom series'">
              <mat-icon>delete</mat-icon>
              Remove
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <!-- Empty State -->
      <div *ngIf="noteSeriesList.length === 0" class="empty-state">
        <mat-icon class="empty-icon">category</mat-icon>
        <h3>No Note Series Found</h3>
        <p>Start by adding your first note series to manage your inventory.</p>
        <button mat-raised-button color="primary" (click)="onShowAddForm()">
          <mat-icon>add_circle</mat-icon>
          Add First Series
        </button>
      </div>
    </div>
  </div>

  <!-- Modal Footer -->
  <mat-divider></mat-divider>
  <div class="modal-footer">
    <div class="footer-info">
      <mat-icon>info</mat-icon>
      <span>Changes to note series will be reflected in your detailed inventory</span>
    </div>
    <button mat-raised-button (click)="onClose()">
      <mat-icon>close</mat-icon>
      Close
    </button>
  </div>
</div>
