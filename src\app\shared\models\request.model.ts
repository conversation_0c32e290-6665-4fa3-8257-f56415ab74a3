export interface Request {
  id: string;
  // TODO: Add properties for Request
  requesterName: string;
  cashTotal: number;
  issuedTo: string;
  requestStatus: string;
  requesterComments?: string | null;
  issuerComments?: string | null;
  userId: number;
  createdAt: string;
  updatedAt?: string | null;

}

enum RequestStatus {
  PENDING = 'Pending',
  APPROVED = 'Approved',
  ISSUED = 'Issued',
  COMPLETED = 'Completed',
  REJECTED = 'Rejected'
}

enum Department {
  FFA,
  BCD,
  ATM
}

enum Role {
  CASHREQUESTER = 'Cash-Requester',
  CASHISSUER = 'Cash-Issuer',
  QAMANAGER = 'QA-Manager'
}


