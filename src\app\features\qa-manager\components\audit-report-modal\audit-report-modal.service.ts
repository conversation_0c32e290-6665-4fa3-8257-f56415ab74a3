import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';

import { AuditReportModalComponent, AuditReportData } from './audit-report-modal.component';

@Injectable({
  providedIn: 'root'
})
export class AuditReportModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the audit report modal
   * @param data Optional data to pass to the modal
   * @returns Observable that emits when the modal is closed
   */
  openAuditReportModal(data: AuditReportData = {}): Observable<any> {
    const dialogRef: MatDialogRef<AuditReportModalComponent> = this.dialog.open(
      AuditReportModalComponent,
      {
        width: '95vw',
        maxWidth: '1400px',
        maxHeight: '90vh',
        data: data,
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        panelClass: ['audit-report-modal-panel'],
        hasBackdrop: true,
        backdropClass: 'audit-report-modal-backdrop'
      }
    );

    return dialogRef.afterClosed();
  }
}
