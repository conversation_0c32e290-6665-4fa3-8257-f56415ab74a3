import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';

import { NoteSeriesManagementModalComponent, NoteSeriesManagementData } from './note-series-management-modal.component';

@Injectable({
  providedIn: 'root'
})
export class NoteSeriesManagementModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the note series management modal
   * @param data Optional data to pass to the modal
   * @returns Observable that emits when the modal is closed
   */
  openNoteSeriesManagementModal(data: NoteSeriesManagementData = {}): Observable<any> {
    const dialogRef: MatDialogRef<NoteSeriesManagementModalComponent> = this.dialog.open(
      NoteSeriesManagementModalComponent,
      {
        width: '95vw',
        maxWidth: '1400px',
        maxHeight: '90vh',
        data: data,
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        panelClass: ['note-series-management-modal-panel'],
        hasBackdrop: true,
        backdropClass: ['note-series-management-modal-backdrop', 'blur-backdrop'],
        closeOnNavigation: true,
        enterAnimationDuration: '300ms',
        exitAnimationDuration: '250ms'
      }
    );

    return dialogRef.afterClosed();
  }
}
