import { CommonModule } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatRadioModule } from '@angular/material/radio';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { RequestCashService } from '../request-cash.service';
import { CashType } from '../../../../shared/models';
import { map, Subscription } from 'rxjs';
import { categorizedCashTypes, groupedCashtypes } from '../../../../shared/models/ui-models/ui-cashtype.model';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-request-cash-step-2',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatTabsModule,
    MatRadioModule,
    MatExpansionModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './request-cash-step-2.component.html',
  styleUrl: './request-cash-step-2.component.scss',
})
export class RequestCashStep2Component implements OnInit, OnDestroy {
  bankNoteSelectionForm!: FormGroup;
  cashTypesList: CashType[] = [];
  sub!: Subscription;
  groupedCashTypes: groupedCashtypes[] = [];
  groupObject: { [key: string]: groupedCashtypes } = {};
  categorizedCashTypes: categorizedCashTypes[] = [];
  categoryObject: { [key: string]: categorizedCashTypes } = {}

  constructor(
    private fb: FormBuilder,
    private cashTypeService: RequestCashService
  ) {
    this.bankNoteSelectionForm = this.fb.group({
      selection: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.sub = this.cashTypeService.getAllCashTypes().subscribe((cashTypes) => {
      this.cashTypesList = cashTypes;
      this.cashTypesList.forEach(({ name, value, category, description }) => {
        const key = `${name}-${category}`;
        if (!this.groupObject.hasOwnProperty(key)) {
          this.groupObject[key] = {
            name,
            description,
            category,
            denominations: []
          };
          this.groupObject[key].denominations.push(value);
        } else {
          this.groupObject[key].denominations.push(value);
        }
      });
      this.groupedCashTypes = Object.values(this.groupObject);
      this.groupedCashTypes.forEach((groupedCashType) => {
        if (!this.categoryObject.hasOwnProperty(groupedCashType.category)) {
          this.categoryObject[groupedCashType.category] = {
            category: groupedCashType.category,
            cashTypes: []
          }
          this.categoryObject[groupedCashType.category].cashTypes.push(groupedCashType)
        } else {
          this.categoryObject[groupedCashType.category].cashTypes.push(groupedCashType)
        }
      })
      this.categorizedCashTypes = Object.values(this.categoryObject)
    });
  }

  ngOnDestroy(): void {
    this.sub.unsubscribe();
  }
}
