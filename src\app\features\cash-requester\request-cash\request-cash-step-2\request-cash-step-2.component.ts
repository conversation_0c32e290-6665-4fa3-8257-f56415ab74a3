import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatRadioModule } from '@angular/material/radio';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-request-cash-step-2',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatTabsModule,
    MatRadioModule,
    MatExpansionModule,
    MatCheckboxModule
  ],
  templateUrl: './request-cash-step-2.component.html',
  styleUrl: './request-cash-step-2.component.scss',
})
export class RequestCashStep2Component {
  fb = inject(FormBuilder);

  bankNoteSelectionForm = this.fb.group({
    cashSelection: ['', Validators.required],
    coinSelection: ['', Validators.required],
    dyeStained: ['', Validators.required],
    fakeNotes: ['', Validators.required],
  });
}
