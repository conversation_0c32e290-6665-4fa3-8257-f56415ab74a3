<div class="cash-requester-dashboard-container">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <h1>Cash Requester Dashboard</h1>
  </div>

  <!-- Main Dashboard Content -->
  <div class="dashboard-content">
    <!-- Unified Welcome and Quick Actions Container -->
    <div class="unified-content-card">
      <!-- Welcome Section -->
      <app-landingboard></app-landingboard>

      <!-- Quick Actions Section -->
      <div class="quick-actions-section">
        <div class="section-header">
          <div class="section-title">
            <mat-icon>flash_on</mat-icon>
            <h2>Quick Actions</h2>
          </div>
        </div>

        <div class="actions-grid">
          <!-- Request Cash Card -->
          <mat-card class="action-card primary" (click)="onRequestCash()">
            <div class="card-background"></div>
            <mat-card-content>
              <div class="card-icon">
                <mat-icon>add_circle</mat-icon>
              </div>
              <div class="card-content">
                <h3>Request Cash</h3>
                <p>Submit new cash requests quickly and securely</p>
              </div>
              <div class="card-arrow">
                <mat-icon>arrow_forward</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Track Requests Card -->
          <mat-card class="action-card secondary" (click)="onTrackRequests()">
            <div class="card-background"></div>
            <mat-card-content>
              <div class="card-icon">
                <mat-icon>track_changes</mat-icon>
              </div>
              <div class="card-content">
                <h3>Track Requests</h3>
                <p>Monitor status and progress of your requests</p>
              </div>
              <div class="card-arrow">
                <mat-icon>arrow_forward</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>

    <!-- Request Stats Section -->
    <app-request-stats></app-request-stats>
  </div>
</div>
