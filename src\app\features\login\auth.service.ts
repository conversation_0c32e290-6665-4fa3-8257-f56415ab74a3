import { Injectable } from '@angular/core';
import { SessionStorageService } from '../../shared/services/session-storage.service';
import { environment } from '../../../environments/environment';
import { BehaviorSubject, map, Observable, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { User } from '../../shared/models/user.model';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private api = environment.apiUrl;
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private sessionStorageService: SessionStorageService,
    private http: HttpClient
  ) {
    // the below checks sessionStorage for existing user on service init
    const storedUser = sessionStorage.getItem('currentUser');
    if (storedUser) {
      this.currentUserSubject.next(JSON.parse(storedUser));
    }
  }

  getAllUsers(): Observable<User[]> {
    return this.http.get<User[]>(`${this.api}/users`);
  }

  login(email: string, password: string): Observable<User> {
    console.log('Login attempt:', email);

    return this.getAllUsers().pipe(
      map((users) => {
        const user = users.find(
          (u) => u.email === email && u.password === password
        );
        if (!user) {
          throw new Error('Invalid email or password');
        }
        return user;
      }),
      tap((user) => {
        this.sessionStorageService.setItem('currentUser', JSON.stringify(user));
        this.currentUserSubject.next(user);
      })
    );
  }

  logout(): Observable<void> {
    console.log('Logout service method called');

    return this.http.post<void>(`${this.api}/users`, {}).pipe(
      tap(() => {
        console.log('Logout response received, cleariong session storage');
        // Clear sessionStorage
        this.sessionStorageService.removeItem('currentUser');
        this.currentUserSubject.next(null);
        console.log('Current user reset to null');
      })
    );
  }

  get currentUserValue() {
    return this.currentUserSubject.value;
  }

  isLoggedIn(): boolean {
    return !!this.currentUserValue;
  }

  hasRole(role: string): boolean {
    const user = this.currentUserValue;
    return user ? user.role.toLowerCase() === role.toLowerCase() : false;
  }

  getRoleDashboardRoute(): string {
    const user = this.currentUserValue;
    if (!user) return '/login';

    switch (user.role.toLowerCase()) {
      case 'cash-requester':
        return '/cash-requester';
      case 'cash-issuer':
        return '/cash-issuer';
      case 'admin':
        return '/qa-manager';
      default:
        return '/login';
    }
  }
}
