import { Routes } from '@angular/router';
// import { authGuard } from './core/guards/auth.guard';
// import { roleGuard } from './core/guards/role.guard';
import { RequesterSummaryComponent } from './features/cash-requester/requester-summary/requester-summary.component';

import { LoginComponent } from './features/login/login.component';

import { RequestStatsComponent } from './shared/components/request-stats/request-stats.component';
import {LandingboardComponent} from './shared/components/dashboard/landing-board.component';
import { UnauthorizedComponent } from './shared/components/unauthorized/unauthorized.component';





export const routes: Routes = [
  {
    "path": "",
    "redirectTo": "/login",
    "pathMatch": "full"
  },
  {path:"requester-summary", component: RequesterSummaryComponent},
  {"path":"landing-board", component: LandingboardComponent},
  {
    "path": "cash-requester",
    loadChildren: () => import('./features/cash-requester/cash-requester.routes').then(m => m.cash_requesterRoutes),
    // canActivate: [
    //   authGuard,
    //   roleGuard
    // ],
    // data: {
    //   role: "Cash-Requester"
    // }
  },
  {
    "path": "cash-issuer",
    loadChildren: () => import('./features/cash-issuer/cash-issuer.routes').then(m => m.cash_issuerRoutes),
    // "canActivate": [
    //   "authGuard",
    //   "roleGuard"
    // ],
    // "data": {
    //   "role": "cash-issuer"
    // }
  },
  {
    "path": "qa-manager",
    loadChildren: () => import('./features/qa-manager/qa-manager.routes').then(m => m.qa_managerRoutes)
    // No auth guards needed for qa-manager
  },
  {
    "path": "login",
    "component": LoginComponent
  },
  {
    "path": "unauthorized",
    "component": UnauthorizedComponent
  },
  {
    "path": "**",
    "redirectTo": "/not-found"
  }
];
