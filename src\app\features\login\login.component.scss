// Override any global styles that might cause white space
:host {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  z-index: 9999;
}

.login-page {
  height: 100vh;
  width: 100vw;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--absa-red);
  padding: 20px;
  font-family: 'Roboto', sans-serif;
  margin: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 48px 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.logo-container {
  margin-bottom: 32px;

  .absa-logo {
    width: 120px;
    height: 120px;
    object-fit: contain;
    filter: brightness(0) saturate(100%) invert(18%) sepia(89%) saturate(1718%) hue-rotate(334deg) brightness(89%) contrast(89%);
  }
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 40px 0;
  letter-spacing: -0.5px;
}

.login-form {
  text-align: left;
}

.form-section {
  width: 100%;
}

.form-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin: 0 0 24px 0;
  text-align: left;
}

.input-group {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.login-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: var(--absa-red);
    box-shadow: 0 0 0 3px rgba(145, 29, 47, 0.1);
  }

  &.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
  }
}

.login-button {
  width: 100%;
  padding: 14px 24px;
  background: var(--absa-red);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;

  &:hover:not(:disabled) {
    background: var(--absa-red-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(145, 29, 47, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 12px;
  padding: 8px 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  text-align: center;
}

.forgot-password {
  text-align: center;
  margin-top: 24px;

  .forgot-link {
    color: var(--absa-red);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-page {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
    max-width: 100%;
  }

  .welcome-title {
    font-size: 20px;
  }

  .logo-container .absa-logo {
    width: 100px;
    height: 100px;
  }
}
