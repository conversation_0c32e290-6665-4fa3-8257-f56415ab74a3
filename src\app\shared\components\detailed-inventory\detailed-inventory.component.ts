import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule, MatTabChangeEvent } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';



// Interfaces for type safety
export interface SeriesData {
  id: string;
  name: string;
  totalBatches: number;
  totalSingles: number;
  totalValue: number;
  totalFakeValue?: number;
  denominations: DenominationData[];
}

export interface DenominationData {
  value: number;
  batches: number;
  singles: number;
  totalValue: number;
  fakeValue?: number;
  stockLevel: number;
}

export interface CoinInventoryItem {
  id: string;
  denomination: number;
  batches: number;
  quantity: number;
  value: number;
}

export interface ExportData {
  series: SeriesData[];
  coins: CoinInventoryItem[];
  timestamp: Date;
}

@Component({
  selector: 'app-detailed-inventory',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTabsModule,
    MatTooltipModule
  ],
  templateUrl: './detailed-inventory.component.html',
  styleUrls: ['./detailed-inventory.component.scss']
})
export class DetailedInventoryComponent implements OnInit, OnChanges {
  // Input properties
  @Input() title?: string;
  @Input() seriesData: SeriesData[] = [];
  @Input() coinInventory: CoinInventoryItem[] = [];
  @Input() showExportButton: boolean = true;
  @Input() showCoinTab: boolean = true;
  @Input() fakeNotesSeriesIds: string[] = [];
  @Input() stockThreshold: number = 50;
  @Input() isDyeStainedMode: boolean = false;
  @Input() isCoinsMode: boolean = false;

  // Output events
  @Output() addCash = new EventEmitter<{ seriesId: string; denomination: number }>();
  @Output() removeCash = new EventEmitter<{ seriesId: string; denomination: number; maxQuantity: number }>();
  @Output() addCoin = new EventEmitter<{ denomination: number; seriesId?: string }>();
  @Output() removeCoin = new EventEmitter<{ denomination: number; seriesId?: string }>();
  @Output() exportAuditReport = new EventEmitter<ExportData>();
  @Output() tabChange = new EventEmitter<MatTabChangeEvent>();
  @Output() dyeStainedModeChange = new EventEmitter<boolean>();

  // Component state
  selectedTabIndex: number = 0;

  constructor() {}

  ngOnInit(): void {
    // Component initialization
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle input changes if needed
    if (changes['seriesData'] || changes['coinInventory']) {
      // Perform any necessary data processing
    }
  }

  // Event handlers
  onAddCash(seriesId: string, denomination: number): void {
    this.addCash.emit({ seriesId, denomination });
  }

  onRemoveCash(seriesId: string, denomination: number, maxQuantity: number): void {
    this.removeCash.emit({ seriesId, denomination, maxQuantity });
  }

  onAddCoin(denomination: number, seriesId?: string): void {
    this.addCoin.emit({ denomination, seriesId });
  }

  onRemoveCoin(denomination: number, seriesId?: string): void {
    this.removeCoin.emit({ denomination, seriesId });
  }

  onExportAuditReport(): void {
    const exportData: ExportData = {
      series: this.seriesData,
      coins: this.coinInventory,
      timestamp: new Date()
    };
    this.exportAuditReport.emit(exportData);
  }

  onTabChange(event: MatTabChangeEvent): void {
    this.selectedTabIndex = event.index;
    this.tabChange.emit(event);
  }

  onDyeStainedModeChange(isDyeStained: boolean): void {
    this.dyeStainedModeChange.emit(isDyeStained);
  }

  // Utility methods
  isFakeNotesSeries(seriesId: string): boolean {
    return this.fakeNotesSeriesIds.includes(seriesId);
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-ZA').format(value);
  }

  getStatusClass(stockLevel: number): string {
    if (stockLevel === 0) return 'status-out-of-stock';
    if (stockLevel < this.stockThreshold) return 'status-low';
    if (stockLevel < 80) return 'status-medium';
    return 'status-good';
  }

  getStatusText(stockLevel: number): string {
    if (stockLevel === 0) return 'Out of Stock';
    if (stockLevel < this.stockThreshold) return 'Low Stock';
    if (stockLevel < 80) return 'Medium Stock';
    return 'In Stock';
  }

  /**
   * Get dye color based on denomination value
   */
  getDyeColor(denominationValue: number): string {
    const dyeColors = ['red', 'blue', 'green', 'purple', 'orange', 'teal', 'pink', 'amber'];

    // Create a consistent color mapping based on denomination value
    const colorIndex = denominationValue % dyeColors.length;
    return dyeColors[colorIndex];
  }

  // Coin-specific methods
  getTotalCoins(): number {
    return this.coinInventory.reduce((total, coin) => total + coin.quantity, 0);
  }

  getTotalCoinValue(): number {
    return this.coinInventory.reduce((total, coin) => total + coin.value, 0);
  }

  getCoinLabel(denomination: number): string {
    if (denomination < 1) {
      return `${Math.round(denomination * 100)}c`;
    }
    return `R${denomination}`;
  }

  getCoinStockLevel(coin: CoinInventoryItem): 'normal' | 'low' | 'out-of-stock' {
    if (coin.quantity === 0) return 'out-of-stock';
    if (coin.quantity < 500) return 'low'; // Consistent with inventory overview component
    return 'normal';
  }

  // TrackBy functions for performance
  trackByDenomination(index: number, item: DenominationData): number {
    return item.value;
  }

  trackByItemId(index: number, item: CoinInventoryItem): string {
    return item.id;
  }

  trackBySeriesId(index: number, item: SeriesData): string {
    return item.id;
  }
}
