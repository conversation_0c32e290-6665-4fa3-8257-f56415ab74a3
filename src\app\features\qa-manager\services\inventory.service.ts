import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  CashInventory,
  CoinInventory,
  DyeStainedCashInventory,
  NoteSeries,
  NoteDenomination,
  CoinSeries,
  CoinDenomination,
  InventoryTransaction,
  TransactionType,
  InventorySummary,
  DyeStainedInventorySummary,
  CustomNoteSeries,
  NoteSeriesInfo,
  COIN_BATCH_CONFIG,
  COIN_SERIES_LABELS,
  COIN_DENOMINATION_LABELS
} from '../../../shared/models/inventory.model';
import { LocalStorageService } from '../../../shared/services/local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class InventoryService {
  // Storage keys
  private readonly CASH_INVENTORY_KEY = 'ffa_cash_inventory';
  private readonly COIN_INVENTORY_KEY = 'ffa_coin_inventory';
  private readonly DYE_STAINED_CASH_INVENTORY_KEY = 'ffa_dye_stained_cash_inventory';
  private readonly TRANSACTIONS_KEY = 'ffa_inventory_transactions';
  private readonly CUSTOM_NOTE_SERIES_KEY = 'ffa_custom_note_series';

  // BehaviorSubjects for reactive data
  private cashInventorySubject = new BehaviorSubject<CashInventory[]>([]);
  private coinInventorySubject = new BehaviorSubject<CoinInventory[]>([]);
  private dyeStainedCashInventorySubject = new BehaviorSubject<DyeStainedCashInventory[]>([]);
  private transactionsSubject = new BehaviorSubject<InventoryTransaction[]>([]);
  private inventorySummarySubject = new BehaviorSubject<InventorySummary | null>(null);
  private customNoteSeriesSubject = new BehaviorSubject<CustomNoteSeries[]>([]);

  // Public observables
  public cashInventory$ = this.cashInventorySubject.asObservable();
  public coinInventory$ = this.coinInventorySubject.asObservable();
  public dyeStainedCashInventory$ = this.dyeStainedCashInventorySubject.asObservable();
  public transactions$ = this.transactionsSubject.asObservable();
  public inventorySummary$ = this.inventorySummarySubject.asObservable();
  public customNoteSeries$ = this.customNoteSeriesSubject.asObservable();

  constructor(private localStorageService: LocalStorageService) {
    this.initializeInventory();
  }

  /**
   * Initialize inventory data from localStorage or create default data
   */
  private initializeInventory(): void {
    // Load existing data or create default inventory (predefined series get default values, custom series start at 0)
    let cashInventory = this.localStorageService.getItem<CashInventory[]>(this.CASH_INVENTORY_KEY) || this.createDefaultCashInventory();
    let coinInventory = this.localStorageService.getItem<CoinInventory[]>(this.COIN_INVENTORY_KEY) || this.createDefaultCoinInventory();
    let dyeStainedCashInventory = this.localStorageService.getItem<DyeStainedCashInventory[]>(this.DYE_STAINED_CASH_INVENTORY_KEY) || this.createDefaultDyeStainedCashInventory();
    const transactions = this.localStorageService.getItem<InventoryTransaction[]>(this.TRANSACTIONS_KEY) || [];
    const customNoteSeries = this.localStorageService.getItem<CustomNoteSeries[]>(this.CUSTOM_NOTE_SERIES_KEY) || [];

    // Ensure all predefined series exist in cash inventory and remove duplicates (for backward compatibility)
    cashInventory = this.ensureAllSeriesExist(cashInventory);

    // Ensure all predefined series exist in dye-stained cash inventory
    dyeStainedCashInventory = this.ensureAllDyeStainedSeriesExist(dyeStainedCashInventory);

    // Ensure all predefined coin series exist (for backward compatibility)
    coinInventory = this.ensureAllCoinSeriesExist(coinInventory);

    // Remove any duplicate dye-stained inventory entries
    dyeStainedCashInventory = this.removeDuplicateDyeStainedInventoryEntries(dyeStainedCashInventory);

    // Remove any duplicate coin inventory entries
    coinInventory = this.removeDuplicateCoinInventoryEntries(coinInventory);

    // Set initial data
    this.cashInventorySubject.next(cashInventory);
    this.coinInventorySubject.next(coinInventory);
    this.dyeStainedCashInventorySubject.next(dyeStainedCashInventory);
    this.transactionsSubject.next(transactions);
    this.customNoteSeriesSubject.next(customNoteSeries);

    // Calculate and set initial summary
    this.updateInventorySummary();

    console.log('Inventory service initialized with data:', {
      cashInventory: cashInventory.length,
      coinInventory: coinInventory.length,
      transactions: transactions.length,
      customNoteSeries: customNoteSeries.length
    });
  }

  /**
   * Create default cash inventory data
   */
  private createDefaultCashInventory(): CashInventory[] {
    const inventory: CashInventory[] = [];
    const series = [NoteSeries.MANDELA, NoteSeries.BIG_5, NoteSeries.COMMEMORATIVE, NoteSeries.V6, NoteSeries.FAKE_NOTES];
    const denominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

    series.forEach(noteSeries => {
      denominations.forEach(denomination => {
        const id = `cash-${noteSeries}-${denomination}`;
        // Get appropriate default quantity based on series type
        const quantity = noteSeries === NoteSeries.FAKE_NOTES
          ? this.getFakeNotesDefaultQuantity(denomination)
          : this.getDefaultQuantity(denomination);
        inventory.push({
          id,
          noteSeries,
          denomination,
          quantity,
          value: noteSeries === NoteSeries.FAKE_NOTES ? 0 : quantity * denomination,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Ensure all predefined series exist in cash inventory (for backward compatibility)
   */
  private ensureAllSeriesExist(existingInventory: CashInventory[]): CashInventory[] {
    // First, remove any duplicate entries
    const inventory = this.removeDuplicateInventoryEntries(existingInventory);
    const allSeries = Object.values(NoteSeries);
    const allDenominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

    let hasChanges = false;

    allSeries.forEach(series => {
      allDenominations.forEach(denomination => {
        const id = `cash-${series}-${denomination}`;
        const existingItem = inventory.find(item => item.id === id);

        if (!existingItem) {
          // Add missing series/denomination combination
          const quantity = series === NoteSeries.FAKE_NOTES
            ? this.getFakeNotesDefaultQuantity(denomination)
            : this.getDefaultQuantity(denomination);

          inventory.push({
            id,
            noteSeries: series,
            denomination,
            quantity,
            value: series === NoteSeries.FAKE_NOTES ? 0 : quantity * denomination,
            lastUpdated: new Date(),
            updatedBy: 'system'
          });
          hasChanges = true;
        }
      });
    });

    // Save updated inventory if changes were made
    if (hasChanges) {
      this.saveCashInventory(inventory);
      console.log('Added missing series/denominations to existing inventory');
    }

    return inventory;
  }

  /**
   * Ensure all predefined coin series exist in coin inventory (for backward compatibility)
   */
  private ensureAllCoinSeriesExist(existingInventory: CoinInventory[]): CoinInventory[] {
    // First, remove any duplicate entries
    const inventory = this.removeDuplicateCoinInventoryEntries(existingInventory);
    const allSeries = Object.values(CoinSeries);
    const allDenominations = [CoinDenomination.C10, CoinDenomination.C20, CoinDenomination.C50, CoinDenomination.R1, CoinDenomination.R2, CoinDenomination.R5];

    let hasChanges = false;

    allSeries.forEach(series => {
      allDenominations.forEach(denomination => {
        const id = `coin-${series}-${denomination}`;
        const existingItem = inventory.find(item => item.id === id);

        if (!existingItem) {
          // Add missing series/denomination combination
          const quantity = this.getDefaultCoinQuantity(denomination);
          const batches = Math.floor(quantity / COIN_BATCH_CONFIG[denomination]);

          inventory.push({
            id,
            series,
            denomination,
            quantity,
            batches,
            value: quantity * denomination,
            lastUpdated: new Date(),
            updatedBy: 'system'
          });
          hasChanges = true;
        }
      });
    });

    if (hasChanges) {
      console.log('Added missing coin series/denomination combinations');
    }

    return inventory;
  }

  /**
   * Remove duplicate inventory entries based on ID
   */
  private removeDuplicateInventoryEntries(inventory: CashInventory[]): CashInventory[] {
    const seenIds = new Set<string>();
    const uniqueInventory: CashInventory[] = [];
    let duplicatesFound = false;

    inventory.forEach(item => {
      if (!seenIds.has(item.id)) {
        seenIds.add(item.id);
        uniqueInventory.push(item);
      } else {
        duplicatesFound = true;
        console.warn('Duplicate inventory entry found and removed:', item.id);
      }
    });

    if (duplicatesFound) {
      console.log(`Removed ${inventory.length - uniqueInventory.length} duplicate entries`);
    }

    return uniqueInventory;
  }

  /**
   * Create default coin inventory data
   */
  private createDefaultCoinInventory(): CoinInventory[] {
    const inventory: CoinInventory[] = [];
    const series = [CoinSeries.MANDELA, CoinSeries.BIG_5, CoinSeries.COMMEMORATIVE, CoinSeries.V6];
    const denominations = [CoinDenomination.C10, CoinDenomination.C20, CoinDenomination.C50, CoinDenomination.R1, CoinDenomination.R2, CoinDenomination.R5];

    series.forEach(coinSeries => {
      denominations.forEach(denomination => {
        const id = `coin-${coinSeries}-${denomination}`;
        const quantity = this.getDefaultCoinQuantity(denomination);
        const batches = Math.floor(quantity / COIN_BATCH_CONFIG[denomination]);
        inventory.push({
          id,
          series: coinSeries,
          denomination,
          quantity,
          batches,
          value: quantity * denomination,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Remove duplicate coin inventory entries based on ID
   */
  private removeDuplicateCoinInventoryEntries(inventory: CoinInventory[]): CoinInventory[] {
    const seenIds = new Set<string>();
    const uniqueInventory: CoinInventory[] = [];
    let duplicatesFound = false;

    inventory.forEach(item => {
      if (!seenIds.has(item.id)) {
        seenIds.add(item.id);
        uniqueInventory.push(item);
      } else {
        duplicatesFound = true;
        console.warn('Duplicate coin inventory entry found and removed:', item.id);
      }
    });

    if (duplicatesFound) {
      console.log(`Removed ${inventory.length - uniqueInventory.length} duplicate coin entries`);
    }

    return uniqueInventory;
  }

  /**
   * Remove duplicate dye-stained inventory entries based on ID and consolidate quantities
   */
  private removeDuplicateDyeStainedInventoryEntries(inventory: DyeStainedCashInventory[]): DyeStainedCashInventory[] {
    const consolidatedMap = new Map<string, DyeStainedCashInventory>();
    let duplicatesFound = false;

    inventory.forEach(item => {
      const key = `${item.noteSeries}-${item.denomination}`;

      if (consolidatedMap.has(key)) {
        // Merge duplicate entries by adding quantities
        const existing = consolidatedMap.get(key)!;
        existing.quantity += item.quantity;
        existing.value = existing.quantity * existing.denomination;
        existing.lastUpdated = new Date();
        duplicatesFound = true;
        console.warn(`Duplicate dye-stained inventory entry found and consolidated: ${item.id} -> ${existing.id}`);
      } else {
        // First occurrence, add to map
        consolidatedMap.set(key, { ...item });
      }
    });

    if (duplicatesFound) {
      console.log(`Consolidated ${inventory.length - consolidatedMap.size} duplicate dye-stained inventory entries`);
    }

    return Array.from(consolidatedMap.values());
  }

  /**
   * Clean up dye-stained inventory data and fix any inconsistencies
   */
  cleanupDyeStainedInventory(): void {
    console.log('🧹 Cleaning up dye-stained inventory data...');

    // Get current inventory
    let dyeStainedInventory = this.getDyeStainedCashInventory();

    // Log current state
    console.log('Current dye-stained inventory entries:', dyeStainedInventory.length);

    // Remove duplicates and consolidate
    dyeStainedInventory = this.removeDuplicateDyeStainedInventoryEntries(dyeStainedInventory);

    // Ensure all predefined series exist (without adding default quantities during cleanup)
    dyeStainedInventory = this.ensureAllDyeStainedSeriesExist(dyeStainedInventory, false);

    // Update the inventory
    this.dyeStainedCashInventorySubject.next(dyeStainedInventory);
    this.saveDyeStainedCashInventory(dyeStainedInventory);
    this.updateInventorySummary();

    console.log('✅ Dye-stained inventory cleanup completed. Final entries:', dyeStainedInventory.length);
  }

  /**
   * Clean up coin inventory data and fix any inconsistencies
   */
  cleanupCoinInventory(): void {
    console.log('🧹 Cleaning up coin inventory data...');

    // Get current inventory
    let coinInventory = this.getCoinInventory();

    // Log current state
    console.log('Current coin inventory entries:', coinInventory.length);

    // Remove duplicates
    coinInventory = this.removeDuplicateCoinInventoryEntries(coinInventory);

    // Ensure all predefined series exist
    coinInventory = this.ensureAllCoinSeriesExist(coinInventory);

    // Update the inventory
    this.coinInventorySubject.next(coinInventory);
    this.saveCoinInventory(coinInventory);
    this.updateInventorySummary();

    console.log('✅ Coin inventory cleanup completed. Final entries:', coinInventory.length);
  }

  /**
   * Debug method to check coin inventory data (for testing)
   */
  debugCoinInventory(): void {
    const coinInventory = this.getCoinInventory();
    console.log('=== COIN INVENTORY DEBUG ===');
    console.log('Total coin inventory entries:', coinInventory.length);

    // Group by series
    const seriesGroups = new Map<CoinSeries, CoinInventory[]>();
    Object.values(CoinSeries).forEach(series => {
      seriesGroups.set(series, []);
    });

    coinInventory.forEach(coin => {
      const group = seriesGroups.get(coin.series);
      if (group) {
        group.push(coin);
      }
    });

    seriesGroups.forEach((coins, series) => {
      console.log(`\n${COIN_SERIES_LABELS[series]} (${series}):`, coins.length, 'denominations');
      coins.forEach(coin => {
        console.log(`  - ${COIN_DENOMINATION_LABELS[coin.denomination]}: ${coin.quantity} coins, ${coin.batches} batches, R${coin.value.toFixed(2)}`);
      });
    });

    console.log('=== END DEBUG ===');
  }

  /**
   * Get default quantity for cash denominations
   */
  private getDefaultQuantity(denomination: NoteDenomination): number {
    const defaults: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 1000,
      [NoteDenomination.R20]: 800,
      [NoteDenomination.R50]: 600,
      [NoteDenomination.R100]: 400,
      [NoteDenomination.R200]: 200
    };
    return defaults[denomination];
  }

  /**
   * Get default quantity for fake notes denominations
   */
  private getFakeNotesDefaultQuantity(denomination: NoteDenomination): number {
    const defaults: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 500,
      [NoteDenomination.R20]: 400,
      [NoteDenomination.R50]: 300,
      [NoteDenomination.R100]: 200,
      [NoteDenomination.R200]: 100
    };
    return defaults[denomination];
  }

  /**
   * Get default quantity for coin denominations
   */
  private getDefaultCoinQuantity(denomination: CoinDenomination): number {
    const defaults: { [key in CoinDenomination]: number } = {
      [CoinDenomination.C10]: 2000,
      [CoinDenomination.C20]: 1500,
      [CoinDenomination.C50]: 1000,
      [CoinDenomination.R1]: 800,
      [CoinDenomination.R2]: 600,
      [CoinDenomination.R5]: 400
    };
    return defaults[denomination];
  }

  // Public getter methods
  /**
   * Get current cash inventory
   */
  getCashInventory(): CashInventory[] {
    return this.cashInventorySubject.value;
  }

  /**
   * Get current coin inventory
   */
  getCoinInventory(): CoinInventory[] {
    return this.coinInventorySubject.value;
  }

  /**
   * Get current transactions
   */
  getTransactions(): InventoryTransaction[] {
    return this.transactionsSubject.value;
  }

  /**
   * Find cash inventory item by series and denomination
   */
  findCashInventory(series: NoteSeries, denomination: NoteDenomination): CashInventory | undefined {
    return this.getCashInventory().find(item =>
      item.noteSeries === series && item.denomination === denomination
    );
  }

  /**
   * Find coin inventory item by series and denomination
   */
  findCoinInventory(series: CoinSeries, denomination: CoinDenomination): CoinInventory | undefined {
    return this.getCoinInventory().find(item =>
      item.series === series && item.denomination === denomination
    );
  }

  /**
   * Get current quantity for cash inventory
   */
  getCurrentCashQuantity(series: NoteSeries, denomination: NoteDenomination): number {
    const item = this.findCashInventory(series, denomination);
    return item ? item.quantity : 0;
  }

  /**
   * Get current quantity for coin inventory
   */
  getCurrentCoinQuantity(series: CoinSeries, denomination: CoinDenomination): number {
    const item = this.findCoinInventory(series, denomination);
    return item ? item.quantity : 0;
  }

  /**
   * Add cash to inventory
   */
  addCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCashInventory();
      let item = this.findCashInventory(series, denomination);

      if (!item) {
        // Create new inventory item if it doesn't exist
        const id = `cash-${series}-${denomination}`;
        item = {
          id,
          noteSeries: series,
          denomination,
          quantity: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;

      // Update inventory item - fake notes have no monetary value
      item.quantity = newQuantity;
      item.value = series === NoteSeries.FAKE_NOTES ? 0 : newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding cash:', error);
      return false;
    }
  }

  /**
   * Add cash to custom series inventory
   */
  addCashToCustomSeries(seriesId: string, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      // Validate input parameters
      if (!seriesId || !denomination || quantity <= 0) {
        console.error('Invalid parameters for adding cash to custom series');
        return false;
      }

      const inventory = this.getCashInventory();
      const itemId = `cash-${seriesId}-${denomination}`;
      let item = inventory.find(i => i.id === itemId);

      if (!item) {
        // Create new inventory item if it doesn't exist (should already exist from initialization)
        item = {
          id: itemId,
          noteSeries: seriesId as any, // Custom series ID
          denomination,
          quantity: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;
      const newValue = newQuantity * denomination; // Custom series always have real monetary value

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newValue;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding cash to custom series:', error);
      return false;
    }
  }

  /**
   * Remove cash from inventory
   */
  removeCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      // Validate input parameters
      if (!series || !denomination || quantity <= 0) {
        console.error('Invalid parameters for removing cash');
        return false;
      }

      const inventory = this.getCashInventory();
      const item = this.findCashInventory(series, denomination);

      if (!item) {
        console.error('Cash inventory item not found');
        return false;
      }

      // Prevent negative quantities
      if (item.quantity < quantity) {
        console.error(`Insufficient quantity in inventory. Available: ${item.quantity}, Requested: ${quantity}`);
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = Math.max(0, previousQuantity - quantity); // Ensure non-negative

      // Update inventory item - fake notes have no monetary value
      item.quantity = newQuantity;
      item.value = series === NoteSeries.FAKE_NOTES ? 0 : newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing cash:', error);
      return false;
    }
  }

  /**
   * Remove cash from custom series inventory
   */
  removeCashFromCustomSeries(seriesId: string, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      // Validate input parameters
      if (!seriesId || !denomination || quantity <= 0) {
        console.error('Invalid parameters for removing cash from custom series');
        return false;
      }

      const inventory = this.getCashInventory();
      const itemId = `cash-${seriesId}-${denomination}`;
      const item = inventory.find(i => i.id === itemId);

      if (!item) {
        console.error('Custom series cash inventory item not found');
        return false;
      }

      // Prevent negative quantities
      if (item.quantity < quantity) {
        console.error(`Insufficient quantity in custom series inventory. Available: ${item.quantity}, Requested: ${quantity}`);
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = Math.max(0, previousQuantity - quantity); // Ensure non-negative
      const newValue = newQuantity * denomination; // Custom series always have real monetary value

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newValue;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing cash from custom series:', error);
      return false;
    }
  }

  /**
   * Add coins to inventory
   */
  addCoins(series: CoinSeries, denomination: CoinDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCoinInventory();
      let item = this.findCoinInventory(series, denomination);

      if (!item) {
        // Create new inventory item if it doesn't exist
        const id = `coin-${series}-${denomination}`;
        item = {
          id,
          series,
          denomination,
          quantity: 0,
          batches: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;
      const newBatches = Math.floor(newQuantity / COIN_BATCH_CONFIG[denomination]);

      // Update inventory item
      item.quantity = newQuantity;
      item.batches = newBatches;
      item.value = newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.coinInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveCoinInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding coins:', error);
      return false;
    }
  }

  /**
   * Remove coins from inventory
   */
  removeCoins(series: CoinSeries, denomination: CoinDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCoinInventory();
      const item = this.findCoinInventory(series, denomination);

      if (!item) {
        console.error('Coin inventory item not found');
        return false;
      }

      if (item.quantity < quantity) {
        console.error('Insufficient quantity in inventory');
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity - quantity;
      const newBatches = Math.floor(newQuantity / COIN_BATCH_CONFIG[denomination]);

      // Update inventory item
      item.quantity = newQuantity;
      item.batches = newBatches;
      item.value = newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.coinInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveCoinInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing coins:', error);
      return false;
    }
  }

  /**
   * Remove coins by denomination across all series (for aggregated coin removal)
   */
  removeCoinsByDenomination(denomination: CoinDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCoinInventory();

      // Find all items with this denomination across all series
      const denominationItems = inventory.filter(item => item.denomination === denomination);

      if (denominationItems.length === 0) {
        console.error('No coin inventory items found for denomination:', denomination);
        return false;
      }

      // Calculate total available quantity
      const totalAvailable = denominationItems.reduce((sum, item) => sum + item.quantity, 0);

      if (totalAvailable < quantity) {
        console.error(`Insufficient quantity in inventory. Available: ${totalAvailable}, Requested: ${quantity}`);
        return false;
      }

      // Remove coins starting from the series with the highest quantity
      denominationItems.sort((a, b) => b.quantity - a.quantity);

      let remainingToRemove = quantity;
      const transactions: InventoryTransaction[] = [];

      for (const item of denominationItems) {
        if (remainingToRemove <= 0) break;

        const removeFromThisItem = Math.min(item.quantity, remainingToRemove);

        if (removeFromThisItem > 0) {
          const previousQuantity = item.quantity;
          const newQuantity = previousQuantity - removeFromThisItem;
          const newBatches = Math.floor(newQuantity / COIN_BATCH_CONFIG[denomination]);

          // Update inventory item
          item.quantity = newQuantity;
          item.batches = newBatches;
          item.value = newQuantity * denomination;
          item.lastUpdated = new Date();
          item.updatedBy = performedBy;

          // Create transaction record
          transactions.push({
            id: this.generateTransactionId(),
            type: TransactionType.REMOVE,
            inventoryId: item.id,
            quantityChange: -removeFromThisItem,
            previousQuantity,
            newQuantity,
            reason,
            performedBy,
            timestamp: new Date()
          });

          remainingToRemove -= removeFromThisItem;
        }
      }

      // Update data
      this.coinInventorySubject.next([...inventory]);
      this.updateInventorySummary();

      // Add all transactions
      transactions.forEach(transaction => this.addTransaction(transaction));

      this.saveCoinInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing coins by denomination:', error);
      return false;
    }
  }

  /**
   * Add transaction to the list
   */
  private addTransaction(transaction: InventoryTransaction): void {
    const transactions = this.getTransactions();
    transactions.push(transaction);
    this.transactionsSubject.next([...transactions]);
    this.saveTransactions(transactions);
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get inventory summary with totals and alerts
   */
  getInventorySummary(): InventorySummary {
    const cashInventory = this.getCashInventory();
    const coinInventory = this.getCoinInventory();
    const dyeStainedCashInventory = this.getDyeStainedCashInventory();

    let totalValue = 0;
    let totalNotes = 0;
    const lowStockAlerts: any[] = [];
    const outOfStockAlerts: any[] = [];

    // Calculate cash totals and check for low stock and out of stock
    cashInventory.forEach(item => {
      // Exclude fake notes from total value calculations
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        totalValue += item.value;
      }
      totalNotes += item.quantity;

      // Exclude fake notes from stock alerts (they are training materials, not real currency)
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        const stockLevel = this.calculateStockLevelPercentage(item.quantity, item.denomination);

        if (item.quantity === 0) {
          // Out of stock - quantity is exactly 0
          outOfStockAlerts.push({
            inventoryId: item.id,
            series: item.noteSeries,
            denomination: item.denomination,
            currentQuantity: item.quantity,
            minimumThreshold: 0,
            severity: 'CRITICAL'
          });
        } else if (stockLevel < 50) {
          // Low stock - stock level percentage is below 50%
          lowStockAlerts.push({
            inventoryId: item.id,
            series: item.noteSeries,
            denomination: item.denomination,
            currentQuantity: item.quantity,
            minimumThreshold: 0,
            severity: this.getAlertSeverityByPercentage(stockLevel)
          });
        }
      }
    });

    // Add dye-stained cash values to total and check for stock alerts
    dyeStainedCashInventory.forEach(item => {
      totalValue += item.value;
      totalNotes += item.quantity;

      // Exclude fake notes from stock alerts (they are training materials, not real currency)
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        const stockLevel = this.calculateStockLevelPercentage(item.quantity, item.denomination);

        if (item.quantity === 0) {
          // Out of stock - quantity is exactly 0
          outOfStockAlerts.push({
            inventoryId: item.id,
            series: item.noteSeries,
            denomination: item.denomination,
            currentQuantity: item.quantity,
            minimumThreshold: 0,
            severity: 'CRITICAL'
          });
        } else if (stockLevel < 50) {
          // Low stock - stock level percentage is below 50%
          lowStockAlerts.push({
            inventoryId: item.id,
            series: item.noteSeries,
            denomination: item.denomination,
            currentQuantity: item.quantity,
            minimumThreshold: 0,
            severity: this.getAlertSeverityByPercentage(stockLevel)
          });
        }
      }
    });

    // Add coin values to total
    coinInventory.forEach(item => {
      totalValue += item.value;
    });

    // Calculate dye-stained summary (for separate reporting)
    const dyeStainedSummary = this.getDyeStainedInventorySummary(dyeStainedCashInventory);

    return {
      totalValue,
      totalNotes,
      seriesBreakdown: [],
      denominationBreakdown: [],
      lowStockAlerts,
      outOfStockAlerts,
      dyeStainedSummary
    };
  }

  /**
   * Get dye-stained inventory summary
   */
  private getDyeStainedInventorySummary(dyeStainedInventory: DyeStainedCashInventory[]): DyeStainedInventorySummary {
    let totalNotes = 0;
    const seriesMap = new Map<string, { totalNotes: number; denominations: any[] }>();

    dyeStainedInventory.forEach(item => {
      totalNotes += item.quantity;

      const seriesKey = typeof item.noteSeries === 'string' ? item.noteSeries : String(item.noteSeries);

      if (!seriesMap.has(seriesKey)) {
        seriesMap.set(seriesKey, { totalNotes: 0, denominations: [] });
      }

      const seriesData = seriesMap.get(seriesKey)!;
      seriesData.totalNotes += item.quantity;
      seriesData.denominations.push({
        denomination: item.denomination,
        series: item.noteSeries,
        quantity: item.quantity
      });
    });

    const seriesBreakdown = Array.from(seriesMap.entries()).map(([seriesKey, data]) => ({
      series: seriesKey,
      totalNotes: data.totalNotes,
      denominations: data.denominations
    }));

    const denominationBreakdown = dyeStainedInventory.map(item => ({
      denomination: item.denomination,
      series: item.noteSeries,
      quantity: item.quantity
    }));

    return {
      totalNotes,
      seriesBreakdown,
      denominationBreakdown
    };
  }

  /**
   * Update the inventory summary and emit to subscribers
   */
  private updateInventorySummary(): void {
    const summary = this.getInventorySummary();
    this.inventorySummarySubject.next(summary);
  }

  /**
   * Calculate stock level as percentage based on maximum expected quantity
   */
  private calculateStockLevelPercentage(quantity: number, denomination: NoteDenomination): number {
    const maxQuantities: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 2000,
      [NoteDenomination.R20]: 1500,
      [NoteDenomination.R50]: 1200,
      [NoteDenomination.R100]: 800,
      [NoteDenomination.R200]: 400
    };

    const maxQuantity = maxQuantities[denomination];
    return Math.min(Math.round((quantity / maxQuantity) * 100), 100);
  }

  /**
   * Get alert severity based on stock level percentage
   */
  private getAlertSeverityByPercentage(percentage: number): string {
    if (percentage <= 10) return 'CRITICAL';
    if (percentage <= 25) return 'HIGH';
    if (percentage <= 50) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Reset all inventory data (for testing/demo purposes)
   */
  resetInventory(): void {
    const cashInventory = this.createDefaultCashInventory();
    const coinInventory = this.createDefaultCoinInventory();
    const dyeStainedCashInventory = this.createDefaultDyeStainedCashInventory();
    const transactions: InventoryTransaction[] = [];

    this.cashInventorySubject.next(cashInventory);
    this.coinInventorySubject.next(coinInventory);
    this.dyeStainedCashInventorySubject.next(dyeStainedCashInventory);
    this.transactionsSubject.next(transactions);
    this.updateInventorySummary();

    this.saveCashInventory(cashInventory);
    this.saveCoinInventory(coinInventory);
    this.saveDyeStainedCashInventory(dyeStainedCashInventory);
    this.saveTransactions(transactions);
  }

  /**
   * Reset all inventory to zero quantities (for fresh start)
   */
  resetInventoryToZero(): void {
    const cashInventory = this.createZeroCashInventory();
    const coinInventory = this.createZeroCoinInventory();
    const dyeStainedCashInventory = this.createZeroDyeStainedCashInventory();
    const transactions: InventoryTransaction[] = [];

    this.cashInventorySubject.next(cashInventory);
    this.coinInventorySubject.next(coinInventory);
    this.dyeStainedCashInventorySubject.next(dyeStainedCashInventory);
    this.transactionsSubject.next(transactions);
    this.updateInventorySummary();

    this.saveCashInventory(cashInventory);
    this.saveCoinInventory(coinInventory);
    this.saveDyeStainedCashInventory(dyeStainedCashInventory);
    this.saveTransactions(transactions);
  }

  /**
   * Create zero cash inventory data
   */
  private createZeroCashInventory(): CashInventory[] {
    const inventory: CashInventory[] = [];
    const series = [NoteSeries.MANDELA, NoteSeries.BIG_5, NoteSeries.COMMEMORATIVE, NoteSeries.V6, NoteSeries.FAKE_NOTES];
    const denominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

    series.forEach(noteSeries => {
      denominations.forEach(denomination => {
        const id = `cash-${noteSeries}-${denomination}`;
        inventory.push({
          id,
          noteSeries,
          denomination,
          quantity: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Create zero dye-stained cash inventory data
   */
  private createZeroDyeStainedCashInventory(): DyeStainedCashInventory[] {
    const inventory: DyeStainedCashInventory[] = [];
    const series = [NoteSeries.MANDELA, NoteSeries.BIG_5, NoteSeries.COMMEMORATIVE, NoteSeries.V6, NoteSeries.FAKE_NOTES];
    const denominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

    series.forEach(noteSeries => {
      denominations.forEach(denomination => {
        const id = `dye-stained-cash-${noteSeries}-${denomination}`;
        inventory.push({
          id,
          noteSeries,
          denomination,
          quantity: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Create zero coin inventory data
   */
  private createZeroCoinInventory(): CoinInventory[] {
    const inventory: CoinInventory[] = [];
    const series = [CoinSeries.MANDELA, CoinSeries.BIG_5, CoinSeries.COMMEMORATIVE, CoinSeries.V6];
    const denominations = [CoinDenomination.C10, CoinDenomination.C20, CoinDenomination.C50, CoinDenomination.R1, CoinDenomination.R2, CoinDenomination.R5];

    series.forEach(coinSeries => {
      denominations.forEach(denomination => {
        const id = `coin-${coinSeries}-${denomination}`;
        inventory.push({
          id,
          series: coinSeries,
          denomination,
          quantity: 0,
          batches: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Get transactions for a specific inventory item
   */
  getTransactionsForItem(inventoryId: string): InventoryTransaction[] {
    return this.getTransactions().filter(transaction => transaction.inventoryId === inventoryId);
  }

  /**
   * Get recent transactions (last N transactions)
   */
  getRecentTransactions(limit: number = 10): InventoryTransaction[] {
    return this.getTransactions()
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Export inventory data for backup
   */
  exportInventoryData(): any {
    return {
      cashInventory: this.getCashInventory(),
      coinInventory: this.getCoinInventory(),
      transactions: this.getTransactions(),
      exportDate: new Date().toISOString()
    };
  }

  /**
   * Import inventory data from backup
   */
  importInventoryData(data: any): boolean {
    try {
      if (data.cashInventory) {
        this.cashInventorySubject.next(data.cashInventory);
        this.saveCashInventory(data.cashInventory);
      }
      if (data.coinInventory) {
        this.coinInventorySubject.next(data.coinInventory);
        this.saveCoinInventory(data.coinInventory);
      }
      if (data.transactions) {
        this.transactionsSubject.next(data.transactions);
        this.saveTransactions(data.transactions);
      }
      this.updateInventorySummary();
      return true;
    } catch (error) {
      console.error('Error importing inventory data:', error);
      return false;
    }
  }

  // ===== CUSTOM NOTE SERIES MANAGEMENT =====

  /**
   * Get all custom note series
   */
  getCustomNoteSeries(): CustomNoteSeries[] {
    return this.customNoteSeriesSubject.value;
  }

  /**
   * Get all note series (predefined + custom)
   */
  getAllNoteSeries(): NoteSeriesInfo[] {
    const predefinedSeries: NoteSeriesInfo[] = Object.values(NoteSeries).map(series => ({
      id: series,
      name: this.getPredefinedSeriesName(series),
      description: this.getPredefinedSeriesDescription(series),
      denominations: Object.values(NoteDenomination).filter(d => typeof d === 'number') as NoteDenomination[],
      isActive: this.hasInventoryForSeries(series),
      isPredefined: true,
      icon: this.getPredefinedSeriesIcon(series)
    }));

    const customSeries: NoteSeriesInfo[] = this.getCustomNoteSeries().map(series => ({
      id: series.id,
      name: series.name,
      description: series.description,
      denominations: series.denominations,
      isActive: series.isActive,
      isPredefined: false
    }));

    return [...predefinedSeries, ...customSeries];
  }

  /**
   * Add a new custom note series
   */
  addCustomNoteSeries(name: string, description: string, denominations: NoteDenomination[], createdBy: string = 'user'): boolean {
    try {
      const customSeries = this.getCustomNoteSeries();

      // Check if series name already exists
      const existingPredefined = Object.values(NoteSeries).some(series =>
        this.getPredefinedSeriesName(series).toLowerCase() === name.toLowerCase()
      );
      const existingCustom = customSeries.some(series =>
        series.name.toLowerCase() === name.toLowerCase()
      );

      if (existingPredefined || existingCustom) {
        console.error('Series name already exists');
        return false;
      }

      const newSeries: CustomNoteSeries = {
        id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: name.trim(),
        description: description.trim(),
        denominations: [...denominations],
        isActive: true,
        createdBy,
        createdDate: new Date()
      };

      customSeries.push(newSeries);
      this.customNoteSeriesSubject.next([...customSeries]);
      this.saveCustomNoteSeries(customSeries);

      // Create initial inventory entries for the new series
      this.createInventoryForCustomSeries(newSeries);

      return true;
    } catch (error) {
      console.error('Error adding custom note series:', error);
      return false;
    }
  }

  /**
   * Remove a custom note series
   */
  removeCustomNoteSeries(seriesId: string): boolean {
    try {
      const customSeries = this.getCustomNoteSeries();
      const seriesIndex = customSeries.findIndex(series => series.id === seriesId);

      if (seriesIndex === -1) {
        console.error('Custom series not found');
        return false;
      }

      // Check if series has inventory
      if (this.hasInventoryForCustomSeries(seriesId)) {
        console.error('Cannot remove series with existing inventory');
        return false;
      }

      // Remove the series
      customSeries.splice(seriesIndex, 1);
      this.customNoteSeriesSubject.next([...customSeries]);
      this.saveCustomNoteSeries(customSeries);

      // Remove inventory entries for this series
      this.removeInventoryForCustomSeries(seriesId);

      return true;
    } catch (error) {
      console.error('Error removing custom note series:', error);
      return false;
    }
  }

  // ===== STORAGE METHODS =====

  /**
   * Save cash inventory to localStorage
   */
  private saveCashInventory(inventory: CashInventory[]): void {
    this.localStorageService.setItem(this.CASH_INVENTORY_KEY, inventory);
  }

  /**
   * Save coin inventory to localStorage
   */
  private saveCoinInventory(inventory: CoinInventory[]): void {
    this.localStorageService.setItem(this.COIN_INVENTORY_KEY, inventory);
  }

  /**
   * Save transactions to localStorage
   */
  private saveTransactions(transactions: InventoryTransaction[]): void {
    this.localStorageService.setItem(this.TRANSACTIONS_KEY, transactions);
  }

  /**
   * Save custom note series to localStorage
   */
  private saveCustomNoteSeries(customSeries: CustomNoteSeries[]): void {
    this.localStorageService.setItem(this.CUSTOM_NOTE_SERIES_KEY, customSeries);
  }

  // ===== UTILITY METHODS =====

  /**
   * Get predefined series name
   */
  private getPredefinedSeriesName(series: NoteSeries): string {
    const labels = {
      [NoteSeries.MANDELA]: 'Mandela Series',
      [NoteSeries.BIG_5]: 'Big 5 Series',
      [NoteSeries.COMMEMORATIVE]: 'Commemorative Series',
      [NoteSeries.V6]: 'V6 Series',
      [NoteSeries.FAKE_NOTES]: 'Fake Notes'
    };
    return labels[series] || series;
  }

  /**
   * Get predefined series description
   */
  private getPredefinedSeriesDescription(series: NoteSeries): string {
    const descriptions = {
      [NoteSeries.MANDELA]: 'Standard circulation notes featuring Nelson Mandela',
      [NoteSeries.BIG_5]: 'Wildlife themed series featuring the Big 5 animals',
      [NoteSeries.COMMEMORATIVE]: 'Special edition commemorative notes',
      [NoteSeries.V6]: 'Latest series design with enhanced security features',
      [NoteSeries.FAKE_NOTES]: 'Training and educational fake currency notes'
    };
    return descriptions[series] || 'Predefined note series';
  }

  /**
   * Get predefined series icon
   */
  private getPredefinedSeriesIcon(series: NoteSeries): string {
    const icons = {
      [NoteSeries.MANDELA]: 'account_balance',
      [NoteSeries.BIG_5]: 'nature',
      [NoteSeries.COMMEMORATIVE]: 'star',
      [NoteSeries.V6]: 'new_releases',
      [NoteSeries.FAKE_NOTES]: 'school'
    };
    return icons[series] || 'category';
  }

  /**
   * Check if series has inventory
   */
  private hasInventoryForSeries(series: NoteSeries): boolean {
    const cashInventory = this.getCashInventory();
    return cashInventory.some(item => item.noteSeries === series && item.quantity > 0);
  }

  /**
   * Check if custom series has inventory
   */
  private hasInventoryForCustomSeries(seriesId: string): boolean {
    const cashInventory = this.getCashInventory();
    return cashInventory.some(item => item.id.includes(seriesId) && item.quantity > 0);
  }

  /**
   * Create initial inventory entries for custom series
   * All custom series start with quantity=0 and value=0 for all denominations
   */
  private createInventoryForCustomSeries(series: CustomNoteSeries): void {
    // Create regular cash inventory entries
    const cashInventory = this.getCashInventory();
    const existingIds = new Set(cashInventory.map(item => item.id));

    series.denominations.forEach(denomination => {
      const id = `cash-${series.id}-${denomination}`;

      // Only add if not already exists
      if (!existingIds.has(id)) {
        const inventoryItem: CashInventory = {
          id,
          noteSeries: series.id as any, // Custom series ID stored as string
          denomination,
          quantity: 0, // Always start with 0 quantity
          value: 0,    // Always start with 0 value
          lastUpdated: new Date(),
          updatedBy: 'system'
        };

        cashInventory.push(inventoryItem);
      }
    });

    // Create dye-stained cash inventory entries
    const dyeStainedCashInventory = this.getDyeStainedCashInventory();
    const existingDyeStainedIds = new Set(dyeStainedCashInventory.map(item => item.id));

    series.denominations.forEach(denomination => {
      const id = `dye-stained-cash-${series.id}-${denomination}`;

      // Only add if not already exists
      if (!existingDyeStainedIds.has(id)) {
        const dyeStainedInventoryItem: DyeStainedCashInventory = {
          id,
          noteSeries: series.id, // Custom series ID
          denomination,
          quantity: 0, // Always start with 0 quantity
          value: 0,    // Start with 0 value (will be calculated when quantity changes)
          lastUpdated: new Date(),
          updatedBy: 'system'
        };

        dyeStainedCashInventory.push(dyeStainedInventoryItem);
      }
    });

    // Update both inventories
    this.cashInventorySubject.next([...cashInventory]);
    this.dyeStainedCashInventorySubject.next([...dyeStainedCashInventory]);
    this.updateInventorySummary();
    this.saveCashInventory(cashInventory);
    this.saveDyeStainedCashInventory(dyeStainedCashInventory);
  }

  /**
   * Remove inventory entries for custom series
   */
  private removeInventoryForCustomSeries(seriesId: string): void {
    // Remove regular cash inventory entries
    const cashInventory = this.getCashInventory();
    const filteredCashInventory = cashInventory.filter(item => !item.id.includes(seriesId));

    // Remove dye-stained cash inventory entries
    const dyeStainedCashInventory = this.getDyeStainedCashInventory();
    const filteredDyeStainedInventory = dyeStainedCashInventory.filter(item => !item.id.includes(seriesId));

    // Update both inventories
    this.cashInventorySubject.next(filteredCashInventory);
    this.dyeStainedCashInventorySubject.next(filteredDyeStainedInventory);
    this.updateInventorySummary();
    this.saveCashInventory(filteredCashInventory);
    this.saveDyeStainedCashInventory(filteredDyeStainedInventory);
  }

  // ===== DYE STAINED INVENTORY METHODS =====

  /**
   * Get dye-stained cash inventory
   */
  getDyeStainedCashInventory(): DyeStainedCashInventory[] {
    return this.dyeStainedCashInventorySubject.value;
  }

  /**
   * Save dye-stained cash inventory to localStorage
   */
  private saveDyeStainedCashInventory(inventory: DyeStainedCashInventory[]): void {
    this.localStorageService.setItem(this.DYE_STAINED_CASH_INVENTORY_KEY, inventory);
  }

  /**
   * Create default dye-stained cash inventory with all series and denominations starting at 0
   */
  private createDefaultDyeStainedCashInventory(): DyeStainedCashInventory[] {
    const inventory: DyeStainedCashInventory[] = [];
    const predefinedSeries = Object.values(NoteSeries);
    const denominations = Object.values(NoteDenomination).filter(d => typeof d === 'number') as NoteDenomination[];

    predefinedSeries.forEach(series => {
      denominations.forEach(denomination => {
        // Get appropriate default quantity based on series type (same as regular notes)
        const quantity = series === NoteSeries.FAKE_NOTES
          ? this.getFakeNotesDefaultQuantity(denomination)
          : this.getDefaultQuantity(denomination);

        inventory.push({
          id: `dye-stained-cash-${series}-${denomination}`,
          noteSeries: series,
          denomination,
          quantity,
          value: series === NoteSeries.FAKE_NOTES ? 0 : quantity * denomination, // Fake notes have no value
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Ensure all predefined series exist in dye-stained cash inventory
   */
  private ensureAllDyeStainedSeriesExist(inventory: DyeStainedCashInventory[], useDefaultQuantities: boolean = true): DyeStainedCashInventory[] {
    const predefinedSeries = Object.values(NoteSeries);
    const denominations = Object.values(NoteDenomination).filter(d => typeof d === 'number') as NoteDenomination[];
    const existingIds = new Set(inventory.map(item => item.id));

    predefinedSeries.forEach(series => {
      denominations.forEach(denomination => {
        const id = `dye-stained-cash-${series}-${denomination}`;
        if (!existingIds.has(id)) {
          // During cleanup, don't add default quantities - start with 0
          // During initialization, use default quantities
          const quantity = useDefaultQuantities
            ? (series === NoteSeries.FAKE_NOTES
                ? this.getFakeNotesDefaultQuantity(denomination)
                : this.getDefaultQuantity(denomination))
            : 0;

          inventory.push({
            id,
            noteSeries: series,
            denomination,
            quantity,
            value: series === NoteSeries.FAKE_NOTES ? 0 : quantity * denomination, // Fake notes have no value
            lastUpdated: new Date(),
            updatedBy: 'system'
          });
        }
      });
    });

    return inventory;
  }

  /**
   * Find dye-stained cash inventory item by series and denomination
   */
  findDyeStainedCashInventory(series: NoteSeries | string, denomination: NoteDenomination): DyeStainedCashInventory | undefined {
    const inventory = this.getDyeStainedCashInventory();
    const matches = inventory.filter(item =>
      item.noteSeries === series && item.denomination === denomination
    );

    if (matches.length > 1) {
      console.warn(`Multiple dye-stained inventory entries found for ${series} ${denomination}. This indicates data corruption.`);
      console.warn('Matches found:', matches.map(m => ({ id: m.id, quantity: m.quantity, value: m.value })));

      // Return the first match but log the issue
      return matches[0];
    }

    return matches[0];
  }

  /**
   * Get current quantity for dye-stained cash inventory
   */
  getCurrentDyeStainedCashQuantity(series: NoteSeries | string, denomination: NoteDenomination): number {
    const item = this.findDyeStainedCashInventory(series, denomination);
    return item ? item.quantity : 0;
  }

  /**
   * Add dye-stained cash to inventory
   */
  addDyeStainedCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getDyeStainedCashInventory();
      let item = this.findDyeStainedCashInventory(series, denomination);

      if (!item) {
        // Create new inventory item if it doesn't exist
        const id = `dye-stained-cash-${series}-${denomination}`;
        item = {
          id,
          noteSeries: series,
          denomination,
          quantity: 0,
          value: 0, // Always 0 for dye-stained money
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;

      // Update inventory item - dye-stained money has real monetary value
      item.quantity = newQuantity;
      item.value = newQuantity * denomination; // Dye-stained money has real monetary value
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date(),
        isDyeStained: true
      };

      // Update data
      this.dyeStainedCashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveDyeStainedCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding dye-stained cash:', error);
      return false;
    }
  }

  /**
   * Add dye-stained cash to custom series inventory
   */
  addDyeStainedCashToCustomSeries(seriesId: string, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      // Validate input parameters
      if (!seriesId || !denomination || quantity <= 0) {
        console.error('Invalid parameters for adding dye-stained cash to custom series');
        return false;
      }

      const inventory = this.getDyeStainedCashInventory();
      const itemId = `dye-stained-cash-${seriesId}-${denomination}`;
      let item = inventory.find(i => i.id === itemId);

      if (!item) {
        // Create new inventory item if it doesn't exist
        item = {
          id: itemId,
          noteSeries: seriesId, // Custom series ID
          denomination,
          quantity: 0,
          value: 0, // Always 0 for dye-stained money
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newQuantity * denomination; // Dye-stained money has real monetary value
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date(),
        isDyeStained: true
      };

      // Update data
      this.dyeStainedCashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveDyeStainedCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding dye-stained cash to custom series:', error);
      return false;
    }
  }

  /**
   * Remove dye-stained cash from inventory
   */
  removeDyeStainedCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getDyeStainedCashInventory();
      const item = this.findDyeStainedCashInventory(series, denomination);

      console.log(`Removing dye-stained cash: ${series} ${denomination} - ${quantity} notes`);
      console.log(`Current dye-stained inventory for ${series} ${denomination}:`, item);

      if (!item) {
        console.error('Dye-stained cash inventory item not found');
        console.log('Available dye-stained inventory items:', inventory.map(i => ({ id: i.id, series: i.noteSeries, denomination: i.denomination, quantity: i.quantity })));
        return false;
      }

      if (item.quantity < quantity) {
        console.error(`Insufficient quantity in dye-stained inventory. Requested: ${quantity}, Available: ${item.quantity}`);
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity - quantity;

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newQuantity * denomination; // Dye-stained money has real monetary value
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date(),
        isDyeStained: true
      };

      // Update data
      this.dyeStainedCashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveDyeStainedCashInventory(inventory);

      console.log(`Successfully removed ${quantity} dye-stained notes. New quantity: ${newQuantity}, New value: R${item.value}`);

      return true;
    } catch (error) {
      console.error('Error removing dye-stained cash:', error);
      return false;
    }
  }

  /**
   * Remove dye-stained cash from custom series inventory
   */
  removeDyeStainedCashFromCustomSeries(seriesId: string, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      // Validate input parameters
      if (!seriesId || !denomination || quantity <= 0) {
        console.error('Invalid parameters for removing dye-stained cash from custom series');
        return false;
      }

      const inventory = this.getDyeStainedCashInventory();
      const itemId = `dye-stained-cash-${seriesId}-${denomination}`;
      const item = inventory.find(i => i.id === itemId);

      if (!item) {
        console.error('Dye-stained cash inventory item not found for custom series');
        return false;
      }

      if (item.quantity < quantity) {
        console.error('Insufficient quantity in dye-stained custom series inventory');
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity - quantity;

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newQuantity * denomination; // Dye-stained money has real monetary value
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date(),
        isDyeStained: true
      };

      // Update data
      this.dyeStainedCashInventorySubject.next([...inventory]);
      this.updateInventorySummary();
      this.addTransaction(transaction);
      this.saveDyeStainedCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing dye-stained cash from custom series:', error);
      return false;
    }
  }
}
