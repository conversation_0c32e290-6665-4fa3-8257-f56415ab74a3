<div class="dashboard-container">
  <mat-card class="dashboard-card">
    <mat-card-content>
      <div class="welcome-section" *ngIf="authService.currentUser$ | async as userData">
        <h1 class="welcome-title">Welcome {{ userData.name }}</h1>
        <p class="user-code">
          {{ userData.department }}-{{ userData.abnumber }}
        </p>
        <p class="description">{{ userData.description }}</p>
      </div>

      <button *ngIf="role === 'cash-requester'" mat-raised-button class="add-request-btn" (click)="onAddNewRequest()">
        <mat-icon>add</mat-icon>
        Add New Request
      </button>
    </mat-card-content>
  </mat-card>
</div>
