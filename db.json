{
  "users": [
    {
      "id": "1",
      "name": "<PERSON><PERSON>",
      "department": "FFA",
      "abnumber": "AB004",
      "email": "<EMAIL>",
      "password": "<PERSON><PERSON>@1234",
      "description": "Manage your cash requests effectively and securely",
      "role": "Cash-Requester",
      "avatar": "https://www.pexels.com/photo/contemplative-young-ethnic-man-with-sunlight-on-face-looking-at-camera-in-studio-5490276/"
    },
    {
      "id": "2",
      "name": "<PERSON><PERSON><PERSON><PERSON>",
      "department": "ATM",
      "abnumber": "AB005",
      "email": "<EMAIL>",
      "password": "<PERSON><PERSON><PERSON><PERSON>@1234",
      "description": "Cash Management - Issue and Approve Cash Requests",
      "role": "Cash-Issuer",
      "avatar": "https://www.pexels.com/photo/smiling-black-model-with-afro-braids-on-gray-background-7275385/"
    },
    {
      "id": "3",
      "name": "Bennet Nkolele",
      "department": "BCD",
      "abnumber": "AB006",
      "email": "<EMAIL>",
      "password": "Bennet@1234",
      "description": "Manage your cash requests effectively and securely",
      "role": "Cash-Requester",
      "avatar": "https://www.pexels.com/photo/thoughtful-young-man-looking-away-6274712/"
    },
    {
      "id": "4",
      "name": "Maboku Seimela",
      "department": "FFA",
      "abnumber": "AB007",
      "email": "<EMAIL>",
      "password": "Maboku@1234",
      "description": "Manage your cash requests effectively and securely",
      "role": "Cash-Requester",
      "avatar": "https://www.pexels.com/photo/man-in-white-crew-neck-shirt-5082976/"
    },
    {
      "id": "5",
      "name": "Tshepang Sebogodi",
      "department": "ATM",
      "abnumber": "AB008",
      "email": "<EMAIL>",
      "password": "Tshepang@1234",
      "description": "Manage your cash requests effectively and securely",
      "role": "Cash-Requester",
      "avatar": "https://www.pexels.com/photo/young-black-lady-looking-at-camera-in-brown-studio-5615665/"
    },
    {
      "id": "6",
      "name": "Malcom Finn",
      "department": "ATM",
      "abnumber": "AB009",
      "email": "<EMAIL>",
      "password": "Malcom@1234",
      "description": "Manager Dashboard - Super Admin Access",
      "role": "admin",
      "avatar": "https://www.pexels.com/photo/smiling-bearded-ethnic-man-looking-at-camera-5876516/"
    },
    {
      "id": "5b87"
    },
    {
      "id": "a0a3"
    },
    {
      "id": "f060"
    },
    {
      "id": "6792"
    }
  ],
  "requests": [
    {
      "id": "1",
      "cashTotal": 7000,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2025-01-10T10:00:00Z",
      "createdAt": "2024-03-05T08:30:00Z",
      "updatedAt": null
    },
    {
      "id": "2",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 22010,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Issued",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2025-01-15T12:00:00Z",
      "createdAt": "2025-01-03T11:00:00Z",
      "updatedAt": null
    },
    {
      "id": "3",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 2000,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2025-02-20T14:00:00Z",
      "createdAt": "2025-03-18T08:45:00Z",
      "updatedAt": null
    },
    {
      "id": "4",
      "requesterName": "Judas Mohlala",
      "cashTotal": 15000,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2025-04-29T06:30:00Z",
      "createdAt": "2025-04-29T06:30:00Z",
      "updatedAt": null
    },
    {
      "id": "5",
      "requesterName": "Judas Mohlala",
      "cashTotal": 11050,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Approved",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2025-06-12T10:20:00Z",
      "createdAt": "2025-06-12T10:20:00Z",
      "updatedAt": null
    },
    {
      "id": "6",
      "requesterName": "Judas Mohlala",
      "cashTotal": 10010,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2025-07-04T08:10:00Z",
      "createdAt": "2025-07-04T08:10:00Z",
      "updatedAt": null
    },
    {
      "id": "7",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 12200,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Approved",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2025-06-15T13:59:00Z",
      "createdAt": "2025-06-15T13:59:00Z",
      "updatedAt": null
    },
    {
      "id": "8",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 21000,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2025-10-05T05:05:00Z",
      "createdAt": "2025-10-05T05:05:00Z",
      "updatedAt": null
    },
    {
      "id": "9",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 20200,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Issued",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2025-02-28T12:30:00Z",
      "createdAt": "2025-02-28T12:30:00Z",
      "updatedAt": null
    },
    {
      "id": "10",
      "requesterName": "Maboku Seimela",
      "cashTotal": 5520,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Rejected",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-08-01T11:55:00Z",
      "createdAt": "2025-08-01T11:55:00Z",
      "updatedAt": null
    },
    {
      "id": "11",
      "requesterName": "Maboku Seimela",
      "cashTotal": 25000,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-09-21T14:35:00Z",
      "createdAt": "2025-09-21T14:35:00Z",
      "updatedAt": null
    },
    {
      "id": "12",
      "requesterName": "Maboku Seimela",
      "cashTotal": 22020,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Approved",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-10-30T10:50:00Z",
      "createdAt": "2025-10-30T10:50:00Z",
      "updatedAt": null
    },
    {
      "id": "13",
      "requesterName": "Maboku Seimela",
      "cashTotal": 1500,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-12-25T00:00:00Z",
      "createdAt": "2025-12-25T00:00:00Z",
      "updatedAt": null
    },
    {
      "id": "14",
      "requesterName": "Judas Mohlala",
      "cashTotal": 30000,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2024-06-15T13:59:00Z",
      "createdAt": "2024-06-15T13:59:00Z",
      "updatedAt": null
    },
    {
      "id": "15",
      "requesterName": "Judas Mohlala",
      "cashTotal": 12000,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Rejected",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2024-10-05T05:05:00Z",
      "createdAt": "2024-10-05T05:05:00Z",
      "updatedAt": null
    },
    {
      "id": "16",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 3000,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Issued",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2024-11-10T09:15:00Z",
      "createdAt": "2024-11-10T09:15:00Z",
      "updatedAt": null
    },
    {
      "id": "17",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 10000,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Approved",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2024-08-01T11:55:00Z",
      "createdAt": "2024-08-01T11:55:00Z",
      "updatedAt": null
    },
    {
      "id": "18",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 10,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2024-02-14T07:25:00Z",
      "createdAt": "2024-02-14T07:25:00Z",
      "updatedAt": null
    },
    {
      "id": "19",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 30000,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Issued",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2024-05-22T13:40:00Z",
      "createdAt": "2024-05-22T13:40:00Z",
      "updatedAt": null
    },
    {
      "id": "20",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 6000,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2024-08-01T11:54:00Z",
      "createdAt": "2024-08-01T11:54:00Z",
      "updatedAt": null
    },
    {
      "id": "21",
      "requesterName": "Judas Mohlala",
      "cashTotal": 10100,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Approved",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2024-03-14T10:20:00Z",
      "createdAt": "2024-03-14T10:20:00Z",
      "updatedAt": null
    },
    {
      "id": "22",
      "requesterName": "Judas Mohlala",
      "cashTotal": 7100,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Rejected",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2024-09-08T14:50:00Z",
      "createdAt": "2024-09-08T14:50:00Z",
      "updatedAt": null
    },
    {
      "id": "23",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 6000,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2025-01-27T09:35:00Z",
      "createdAt": "2025-01-27T09:35:00Z",
      "updatedAt": null
    },
    {
      "id": "24",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 11000,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2024-04-11T07:10:00Z",
      "createdAt": "2024-04-11T07:10:00Z",
      "updatedAt": null
    },
    {
      "id": "25",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 3050,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Issued",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2024-07-03T12:25:00Z",
      "createdAt": "2024-07-03T12:25:00Z",
      "updatedAt": null
    },
    {
      "id": "26",
      "requesterName": "Maboku Seimela",
      "cashTotal": 20000,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Approved",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-04-29T06:30:00Z",
      "createdAt": "2024-12-10T11:50:00Z",
      "updatedAt": null
    },
    {
      "id": "27",
      "requesterName": "Maboku Seimela",
      "cashTotal": 15000,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-03-01T06:45:00Z",
      "createdAt": "2025-03-01T06:45:00Z",
      "updatedAt": null
    },
    {
      "id": "28",
      "requesterName": "Maboku Seimela",
      "cashTotal": 2200,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-05-10T13:10:00Z",
      "createdAt": "2025-05-10T13:10:00Z",
      "updatedAt": null
    },
    {
      "id": "29",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 25000,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Approved",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2024-07-18T08:00:00Z",
      "createdAt": "2024-07-18T08:00:00Z",
      "updatedAt": null
    },
    {
      "id": "30",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 220,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Pending",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2024-05-02T10:45:00Z",
      "createdAt": "2024-05-02T10:45:00Z",
      "updatedAt": null
    },
    {
      "id": "31",
      "requesterName": "Bennet Nkolele",
      "cashTotal": 2000,
      "issuedTo": "Bennet Nkolele",
      "requestStatus": "Rejected",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 3,
      "expectedReturn": "2025-02-28T12:30:00Z",
      "createdAt": "2025-02-06T14:15:00Z",
      "updatedAt": null
    },
    {
      "id": "32",
      "requesterName": "Judas Mohlala",
      "cashTotal": 5000,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Completed",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2024-11-19T07:35:00Z",
      "createdAt": "2024-11-19T07:35:00Z",
      "updatedAt": null
    },
    {
      "id": "33",
      "requesterName": "Judas Mohlala",
      "cashTotal": 30000,
      "issuedTo": "Judas Mohlala",
      "requestStatus": "Issued",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 1,
      "expectedReturn": "2025-06-08T09:50:00Z",
      "createdAt": "2025-06-08T09:50:00Z",
      "updatedAt": null
    },
    {
      "id": "34",
      "requesterName": "Tshepang Sebogodi",
      "cashTotal": 7000,
      "issuedTo": "Tshepang Sebogodi",
      "requestStatus": "Rejected",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 5,
      "expectedReturn": "2025-12-25T00:00:00Z",
      "createdAt": "2024-09-14T11:00:00Z",
      "updatedAt": null
    },
    {
      "id": "35",
      "requesterName": "Maboku Seimela",
      "cashTotal": 22000,
      "issuedTo": "Maboku Seimela",
      "requestStatus": "Issued",
      "requesterComments": null,
      "issuerComments": null,
      "userId": 4,
      "expectedReturn": "2025-04-06T12:15:00Z",
      "createdAt": "2025-04-06T12:15:00Z",
      "updatedAt": null
    }
  ],
  "requestCashTypes": [
    {
<<<<<<< HEAD
      "id": "1",
=======
      "id": 1,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 1,
      "cashTypeId": 3
    },
    {
<<<<<<< HEAD
      "id": "2",
=======
      "id": 2,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 1,
      "cashTypeId": 7
    },
    {
<<<<<<< HEAD
      "id": "3",
=======
      "id": 3,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 2,
      "cashTypeId": 5
    },
    {
<<<<<<< HEAD
      "id": "4",
=======
      "id": 4,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 2,
      "cashTypeId": 12
    },
    {
<<<<<<< HEAD
      "id": "5",
=======
      "id": 5,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 2,
      "cashTypeId": 21
    },
    {
<<<<<<< HEAD
      "id": "6",
=======
      "id": 6,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 3,
      "cashTypeId": 1
    },
    {
<<<<<<< HEAD
      "id": "7",
=======
      "id": 7,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 3,
      "cashTypeId": 16
    },
    {
<<<<<<< HEAD
      "id": "8",
=======
      "id": 8,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 4,
      "cashTypeId": 8
    },
    {
<<<<<<< HEAD
      "id": "9",
=======
      "id": 9,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 4,
      "cashTypeId": 14
    },
    {
<<<<<<< HEAD
      "id": "10",
=======
      "id": 10,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 5,
      "cashTypeId": 4
    },
    {
<<<<<<< HEAD
      "id": "11",
=======
      "id": 11,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 5,
      "cashTypeId": 11
    },
    {
<<<<<<< HEAD
      "id": "12",
=======
      "id": 12,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 5,
      "cashTypeId": 23
    },
    {
<<<<<<< HEAD
      "id": "13",
=======
      "id": 13,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 6,
      "cashTypeId": 9
    },
    {
<<<<<<< HEAD
      "id": "14",
=======
      "id": 14,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 6,
      "cashTypeId": 27
    },
    {
<<<<<<< HEAD
      "id": "15",
=======
      "id": 15,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 7,
      "cashTypeId": 2
    },
    {
<<<<<<< HEAD
      "id": "16",
=======
      "id": 16,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 7,
      "cashTypeId": 19
    },
    {
<<<<<<< HEAD
      "id": "17",
=======
      "id": 17,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 7,
      "cashTypeId": 25
    },
    {
<<<<<<< HEAD
      "id": "18",
=======
      "id": 18,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 8,
      "cashTypeId": 6
    },
    {
<<<<<<< HEAD
      "id": "19",
=======
      "id": 19,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 8,
      "cashTypeId": 20
    },
    {
<<<<<<< HEAD
      "id": "20",
=======
      "id": 20,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 9,
      "cashTypeId": 10
    },
    {
<<<<<<< HEAD
      "id": "21",
=======
      "id": 21,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 9,
      "cashTypeId": 31
    },
    {
<<<<<<< HEAD
      "id": "22",
=======
      "id": 22,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 10,
      "cashTypeId": 13
    },
    {
<<<<<<< HEAD
      "id": "23",
=======
      "id": 23,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 10,
      "cashTypeId": 28
    },
    {
<<<<<<< HEAD
      "id": "24",
=======
      "id": 24,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 10,
      "cashTypeId": 32
    },
    {
<<<<<<< HEAD
      "id": "25",
=======
      "id": 25,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 11,
      "cashTypeId": 5
    },
    {
<<<<<<< HEAD
      "id": "26",
=======
      "id": 26,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 11,
      "cashTypeId": 18
    },
    {
<<<<<<< HEAD
      "id": "27",
=======
      "id": 27,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 12,
      "cashTypeId": 22
    },
    {
<<<<<<< HEAD
      "id": "28",
=======
      "id": 28,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 12,
      "cashTypeId": 7
    },
    {
<<<<<<< HEAD
      "id": "29",
=======
      "id": 29,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 12,
      "cashTypeId": 15
    },
    {
<<<<<<< HEAD
      "id": "30",
=======
      "id": 30,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 13,
      "cashTypeId": 1
    },
    {
<<<<<<< HEAD
      "id": "31",
=======
      "id": 31,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 13,
      "cashTypeId": 26
    },
    {
<<<<<<< HEAD
      "id": "32",
=======
      "id": 32,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 14,
      "cashTypeId": 9
    },
    {
<<<<<<< HEAD
      "id": "33",
=======
      "id": 33,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 14,
      "cashTypeId": 20
    },
    {
<<<<<<< HEAD
      "id": "34",
=======
      "id": 34,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 15,
      "cashTypeId": 2
    },
    {
<<<<<<< HEAD
      "id": "35",
=======
      "id": 35,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 15,
      "cashTypeId": 14
    },
    {
<<<<<<< HEAD
      "id": "36",
=======
      "id": 36,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 16,
      "cashTypeId": 11
    },
    {
<<<<<<< HEAD
      "id": "37",
=======
      "id": 37,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 16,
      "cashTypeId": 17
    },
    {
<<<<<<< HEAD
      "id": "38",
=======
      "id": 38,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 17,
      "cashTypeId": 19
    },
    {
<<<<<<< HEAD
      "id": "39",
=======
      "id": 39,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 18,
      "cashTypeId": 21
    },
    {
<<<<<<< HEAD
      "id": "40",
=======
      "id": 40,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 19,
      "cashTypeId": 4
    },
    {
<<<<<<< HEAD
      "id": "41",
=======
      "id": 41,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 19,
      "cashTypeId": 15
    },
    {
<<<<<<< HEAD
      "id": "42",
=======
      "id": 42,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 20,
      "cashTypeId": 3
    },
    {
<<<<<<< HEAD
      "id": "43",
=======
      "id": 43,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 20,
      "cashTypeId": 6
    },
    {
<<<<<<< HEAD
      "id": "44",
=======
      "id": 44,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 21,
      "cashTypeId": 9
    },
    {
<<<<<<< HEAD
      "id": "45",
=======
      "id": 45,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 21,
      "cashTypeId": 24
    },
    {
<<<<<<< HEAD
      "id": "46",
=======
      "id": 46,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 22,
      "cashTypeId": 8
    },
    {
<<<<<<< HEAD
      "id": "47",
=======
      "id": 47,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 22,
      "cashTypeId": 12
    },
    {
<<<<<<< HEAD
      "id": "48",
=======
      "id": 48,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 22,
      "cashTypeId": 30
    },
    {
<<<<<<< HEAD
      "id": "49",
=======
      "id": 49,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 23,
      "cashTypeId": 13
    },
    {
<<<<<<< HEAD
      "id": "50",
=======
      "id": 50,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 23,
      "cashTypeId": 16
    },
    {
<<<<<<< HEAD
      "id": "51",
=======
      "id": 51,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 24,
      "cashTypeId": 1
    },
    {
<<<<<<< HEAD
      "id": "52",
=======
      "id": 52,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 24,
      "cashTypeId": 19
    },
    {
<<<<<<< HEAD
      "id": "53",
=======
      "id": 53,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 25,
      "cashTypeId": 7
    },
    {
<<<<<<< HEAD
      "id": "54",
=======
      "id": 54,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 25,
      "cashTypeId": 11
    },
    {
<<<<<<< HEAD
      "id": "55",
=======
      "id": 55,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 25,
      "cashTypeId": 29
    },
    {
<<<<<<< HEAD
      "id": "56",
=======
      "id": 56,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 26,
      "cashTypeId": 5
    },
    {
<<<<<<< HEAD
      "id": "57",
=======
      "id": 57,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 27,
      "cashTypeId": 3
    },
    {
<<<<<<< HEAD
      "id": "58",
=======
      "id": 58,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 27,
      "cashTypeId": 14
    },
    {
<<<<<<< HEAD
      "id": "59",
=======
      "id": 59,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 28,
      "cashTypeId": 2
    },
    {
<<<<<<< HEAD
      "id": "60",
=======
      "id": 60,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 28,
      "cashTypeId": 25
    },
    {
<<<<<<< HEAD
      "id": "61",
=======
      "id": 61,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 29,
      "cashTypeId": 10
    },
    {
<<<<<<< HEAD
      "id": "62",
=======
      "id": 62,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 29,
      "cashTypeId": 18
    },
    {
<<<<<<< HEAD
      "id": "63",
=======
      "id": 63,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 30,
      "cashTypeId": 22
    },
    {
<<<<<<< HEAD
      "id": "64",
=======
      "id": 64,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 30,
      "cashTypeId": 31
    },
    {
<<<<<<< HEAD
      "id": "65",
=======
      "id": 65,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 31,
      "cashTypeId": 1
    },
    {
<<<<<<< HEAD
      "id": "66",
=======
      "id": 66,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 31,
      "cashTypeId": 6
    },
    {
<<<<<<< HEAD
      "id": "67",
=======
      "id": 67,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 32,
      "cashTypeId": 8
    },
    {
<<<<<<< HEAD
      "id": "68",
=======
      "id": 68,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 33,
      "cashTypeId": 4
    },
    {
<<<<<<< HEAD
      "id": "69",
=======
      "id": 69,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 33,
      "cashTypeId": 20
    },
    {
<<<<<<< HEAD
      "id": "70",
=======
      "id": 70,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 34,
      "cashTypeId": 3
    },
    {
<<<<<<< HEAD
      "id": "71",
=======
      "id": 71,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 34,
      "cashTypeId": 12
    },
    {
<<<<<<< HEAD
      "id": "72",
=======
      "id": 72,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 35,
      "cashTypeId": 7
    },
    {
<<<<<<< HEAD
      "id": "73",
=======
      "id": 73,
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "requestId": 35,
      "cashTypeId": 15
    }
  ],
  "cashTypes": [
    {
      "id": "1",
      "name": "Mandela Series",
      "value": 10,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R10 Mandela series note"
    },
    {
      "id": "2",
      "name": "Mandela Series",
      "value": 20,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R20 Mandela series note"
    },
    {
      "id": "3",
      "name": "Mandela Series",
      "value": 50,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R50 Mandela series note"
    },
    {
      "id": "4",
      "name": "Mandela Series",
      "value": 100,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R100 Mandela series note"
    },
    {
      "id": "5",
      "name": "Mandela Series",
      "value": 200,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R200 Mandela series note"
    },
    {
      "id": "6",
      "name": "V6 Series",
      "value": 10,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R10 V6 series note"
    },
    {
      "id": "7",
      "name": "V6 Series",
      "value": 20,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R20 V6 series note"
    },
    {
      "id": "8",
      "name": "V6 Series",
      "value": 50,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R50 V6 series note"
    },
    {
      "id": "9",
      "name": "V6 Series",
      "value": 100,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R100 V6 series note"
    },
    {
      "id": "10",
      "name": "V6 Series",
      "value": 200,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R200 V6 series note"
    },
    {
      "id": "11",
      "name": "Big 5 Series",
      "value": 10,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R10 Big 5 series note"
    },
    {
      "id": "12",
      "name": "Big 5 Series",
      "value": 20,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R20 Big 5 series note"
    },
    {
      "id": "13",
      "name": "Big 5 Series",
      "value": 50,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R50 Big 5 series note"
    },
    {
      "id": "14",
      "name": "Big 5 Series",
      "value": 100,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R100 Big 5 series note"
    },
    {
      "id": "15",
      "name": "Big 5 Series",
      "value": 200,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R200 Big 5 series note"
    },
    {
      "id": "16",
      "name": "Commemorative Series",
      "value": 10,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R10 Commemorative series note"
    },
    {
      "id": "17",
      "name": "Commemorative Series",
      "value": 20,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R20 Commemorative series note"
    },
    {
      "id": "18",
      "name": "Commemorative Series",
      "value": 50,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R50 Commemorative series note"
    },
    {
      "id": "19",
      "name": "Commemorative Series",
      "value": 100,
<<<<<<< HEAD
=======
      "category": "Cash",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "description": "This is a R100 Commemorative series note"
    },
    {
      "id": "20",
      "name": "Commemorative Series",
      "value": 200,
<<<<<<< HEAD
      "description": "This is a R200 Commemorative series note"
    },
    {
      "id": "21",
      "name": "Old Coins",
=======
      "category": "Cash",
      "description": "This is a R200 Commemorative series note"
    },
    {
      "id": 21,
      "name": "Mandela Series",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "value": 0.1,
      "category": "Coins",
      "description": "This is a mandela series coin 10c"
    },
    {
<<<<<<< HEAD
      "id": "22",
      "name": "Old Coins",
=======
      "id": 22,
      "name": "Mandela Series",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "value": 0.2,
      "category": "Coins",
      "description": "This is a mandela series coin 20c"
    },
    {
<<<<<<< HEAD
      "id": "23",
      "name": "Old Coins",
=======
      "id": 23,
      "name": "Mandela Series",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "value": 0.5,
      "category": "Coins",
      "description": "This is a mandela series coin 50c"
    },
    {
<<<<<<< HEAD
      "id": "24",
      "name": "Old Coins",
      "value": 1,
      "description": "This is an old coin R1"
    },
    {
      "id": "25",
      "name": "Old Coins",
      "value": 2,
      "description": "This is an old coin R2"
    },
    {
      "id": "26",
      "name": "Old Coins",
      "value": 5,
      "description": "This is an old coin R5"
    },
    {
      "id": "27",
      "name": "New Coins",
=======
      "id": 24,
      "name": "Mandela Series",
      "value": 1,
      "category": "Coins",
      "description": "This is a mandela series coin R1"
    },
    {
      "id": 25,
      "name": "Mandela Series",
      "value": 2,
      "category": "Coins",
      "description": "This is a mandela series coin R2"
    },
    {
      "id": 26,
      "name": "Mandela Series",
      "value": 5,
      "category": "Coins",
      "description": "This is a mandela series coin R5"
    },
    {
      "id": 27,
      "name": "Big 5 Series",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "value": 0.1,
      "category": "Coins",
      "description": "This is a big 5 10 coinc"
    },
    {
<<<<<<< HEAD
      "id": "28",
      "name": "New Coins",
=======
      "id": 28,
      "name": "Big 5 Series",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "value": 0.2,
      "category": "Coins",
      "description": "This is a big 5 20 coinc"
    },
    {
<<<<<<< HEAD
      "id": "29",
      "name": "New Coins",
=======
      "id": 29,
      "name": "Big 5 Series",
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
      "value": 0.5,
      "category": "Coins",
      "description": "This is a big 5 50 coinc"
    },
    {
<<<<<<< HEAD
      "id": "30",
      "name": "New Coins",
      "value": 1,
      "description": "This is an New coin R1"
    },
    {
      "id": "31",
      "name": "New Coins",
      "value": 2,
      "description": "This is an New coin R2"
    },
    {
      "id": "32",
      "name": "New Coins",
      "value": 5,
      "description": "This is an New coin R5"
=======
      "id": 30,
      "name": "Big 5 Series",
      "value": 1,
      "category": "Coins",
      "description": "This is a big 5 R1 coin"
    },
    {
      "id": 31,
      "name": "Big 5 Series",
      "value": 2,
      "category": "Coins",
      "description": "This is a big 5 R2 coin"
    },
    {
      "id": 32,
      "name": "Big 5 Series",
      "value": 5,
      "category": "Coins",
      "description": "This is a big 5 R5 coin"
    },
    {
      "id": 33,
      "name": "V6 Series",
      "value": 0.1,
      "category": "Coins",
      "description": "This is a V6 series 10 coinc"
    },
    {
      "id": 34,
      "name": "V6 Series",
      "value": 0.2,
      "category": "Coins",
      "description": "This is a V6 series 20 coinc"
    },
    {
      "id": 35,
      "name": "V6 Series",
      "value": 0.5,
      "category": "Coins",
      "description": "This is a V6 series 50 coinc"
    },
    {
      "id": 36,
      "name": "V6 Series",
      "value": 1,
      "category": "Coins",
      "description": "This is a V6 series R1 coin"
    },
    {
      "id": 37,
      "name": "V6 Series",
      "value": 2,
      "category": "Coins",
      "description": "This is a V6 series R2 coin"
    },
    {
      "id": 38,
      "name": "V6 Series",
      "value": 5,
      "category": "Coins",
      "description": "This is a V6 series R5 coin"
    },
    {
      "id": 39,
      "name": "Commemorative Series",
      "value": 0.1,
      "category": "Coins",
      "description": "This is a Commemorative series 10 coinc"
    },
    {
      "id": 40,
      "name": "Commemorative Series",
      "value": 0.2,
      "category": "Coins",
      "description": "This is a Commemorative series 20 coinc"
    },
    {
      "id": 41,
      "name": "Commemorative Series",
      "value": 0.5,
      "category": "Coins",
      "description": "This is a Commemorative series 50 coinc"
    },
    {
      "id": 42,
      "name": "Commemorative Series",
      "value": 1,
      "category": "Coins",
      "description": "This is a Commemorative series R1 coin"
    },
    {
      "id": 43,
      "name": "Commemorative Series",
      "value": 2,
      "category": "Coins",
      "description": "This is a Commemorative series R2 coin"
    },
    {
      "id": 44,
      "name": "Commemorative Series",
      "value": 5,
      "category": "Coins",
      "description": "This is a Commemorative series R5 coin"
    },
    {
      "id": 45,
      "name": "Fake notes",
      "value": 10,
      "category": "Fake notes",
      "description": "This is a R10 Fake note note"
    },
    {
      "id": 46,
      "name": "Fake notes",
      "value": 20,
      "category": "Fake notes",
      "description": "This is a R20 Fake note note"
    },
    {
      "id": 47,
      "name": "Fake notes",
      "value": 50,
      "category": "Fake notes",
      "description": "This is a R50 Fake note note"
    },
    {
      "id": 48,
      "name": "Fake notes",
      "value": 100,
      "category": "Fake notes",
      "description": "This is a R100 Fake note note"
    },
    {
      "id": 49,
      "name": "Fake notes",
      "value": 200,
      "category": "Fake notes",
      "description": "This is a R200 Fake note note"
    },
    {
      "id": 50,
      "name": "Mandela Series",
      "value": 10,
      "category": "Dye-stained",
      "description": "This is a R10 Mandela series note"
    },
    {
      "id": 51,
      "name": "Mandela Series",
      "value": 20,
      "category": "Dye-stained",
      "description": "This is a R20 Mandela series note"
    },
    {
      "id": 52,
      "name": "Mandela Series",
      "value": 50,
      "category": "Dye-stained",
      "description": "This is a R50 Mandela series note"
    },
    {
      "id": 53,
      "name": "Mandela Series",
      "value": 100,
      "category": "Dye-stained",
      "description": "This is a R100 Mandela series note"
    },
    {
      "id": 54,
      "name": "Mandela Series",
      "value": 200,
      "category": "Dye-stained",
      "description": "This is a R200 Mandela series note"
    },
    {
      "id": 55,
      "name": "V6 Series",
      "value": 10,
      "category": "Dye-stained",
      "description": "This is a R10 V6 series note"
    },
    {
      "id": 56,
      "name": "V6 Series",
      "value": 20,
      "category": "Dye-stained",
      "description": "This is a R20 V6 series note"
    },
    {
      "id": 57,
      "name": "V6 Series",
      "value": 50,
      "category": "Dye-stained",
      "description": "This is a R50 V6 series note"
    },
    {
      "id": 58,
      "name": "V6 Series",
      "value": 100,
      "category": "Dye-stained",
      "description": "This is a R100 V6 series note"
    },
    {
      "id": 59,
      "name": "V6 Series",
      "value": 200,
      "category": "Dye-stained",
      "description": "This is a R200 V6 series note"
    },
    {
      "id": 60,
      "name": "Big 5 Series",
      "value": 10,
      "category": "Dye-stained",
      "description": "This is a R10 Big 5 series note"
    },
    {
      "id": 61,
      "name": "Big 5 Series",
      "value": 20,
      "category": "Dye-stained",
      "description": "This is a R20 Big 5 series note"
    },
    {
      "id": 62,
      "name": "Big 5 Series",
      "value": 50,
      "category": "Dye-stained",
      "description": "This is a R50 Big 5 series note"
    },
    {
      "id": 63,
      "name": "Big 5 Series",
      "value": 100,
      "category": "Dye-stained",
      "description": "This is a R100 Big 5 series note"
    },
    {
      "id": 64,
      "name": "Big 5 Series",
      "value": 200,
      "category": "Dye-stained",
      "description": "This is a R200 Big 5 series note"
    },
    {
      "id": 65,
      "name": "Commemorative Series",
      "value": 10,
      "category": "Dye-stained",
      "description": "This is a R10 Commemorative series note"
    },
    {
      "id": 66,
      "name": "Commemorative Series",
      "value": 20,
      "category": "Dye-stained",
      "description": "This is a R20 Commemorative series note"
    },
    {
      "id": 67,
      "name": "Commemorative Series",
      "value": 50,
      "category": "Dye-stained",
      "description": "This is a R50 Commemorative series note"
    },
    {
      "id": 68,
      "name": "Commemorative Series",
      "value": 100,
      "category": "Dye-stained",
      "description": "This is a R100 Commemorative series note"
    },
    {
      "id": 69,
      "name": "Commemorative Series",
      "value": 200,
      "category": "Dye-stained",
      "description": "This is a R200 Commemorative series note"
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
    }
  ],
  "bundles": [
    {
      "id": "1",
      "transactionType": "addition",
      "numberOfBundles": 100,
      "reason": null,
      "cashTypeId": 1
    },
    {
      "id": "2",
      "transactionType": "addition",
      "numberOfBundles": 60,
      "reason": null,
      "cashTypeId": 2
    },
    {
      "id": "3",
      "transactionType": "addition",
      "numberOfBundles": 80,
      "reason": null,
      "cashTypeId": 3
    },
    {
      "id": "4",
      "transactionType": "addition",
      "numberOfBundles": 10,
      "reason": null,
      "cashTypeId": 4
    },
    {
      "id": "5",
      "transactionType": "addition",
      "numberOfBundles": 30,
      "reason": null,
      "cashTypeId": 5
    },
    {
      "id": "6",
      "transactionType": "addition",
      "numberOfBundles": 25,
      "reason": null,
      "cashTypeId": 6
    },
    {
      "id": "7",
      "transactionType": "addition",
      "numberOfBundles": 70,
      "reason": null,
      "cashTypeId": 7
    },
    {
      "id": "8",
      "transactionType": "addition",
      "numberOfBundles": 40,
      "reason": null,
      "cashTypeId": 8
    },
    {
      "id": "9",
      "transactionType": "addition",
      "numberOfBundles": 45,
      "reason": null,
      "cashTypeId": 9
    },
    {
      "id": "10",
      "transactionType": "addition",
      "numberOfBundles": 35,
      "reason": null,
      "cashTypeId": 10
    },
    {
      "id": "11",
      "transactionType": "addition",
      "numberOfBundles": 2,
      "reason": null,
      "cashTypeId": 11
    },
    {
      "id": "12",
      "transactionType": "addition",
      "numberOfBundles": 0,
      "reason": null,
      "cashTypeId": 12
    },
    {
      "id": "13",
      "transactionType": "addition",
      "numberOfBundles": 1,
      "reason": null,
      "cashTypeId": 13
    },
    {
      "id": "14",
      "transactionType": "addition",
      "numberOfBundles": 0,
      "reason": null,
      "cashTypeId": 14
    },
    {
      "id": "15",
      "transactionType": "addition",
      "numberOfBundles": 0,
      "reason": null,
      "cashTypeId": 15
    },
    {
      "id": "16",
      "transactionType": "addition",
      "numberOfBundles": 20,
      "reason": null,
      "cashTypeId": 16
    },
    {
      "id": "17",
      "transactionType": "addition",
      "numberOfBundles": 45,
      "reason": null,
      "cashTypeId": 17
    },
    {
      "id": "18",
      "transactionType": "addition",
      "numberOfBundles": 50,
      "reason": null,
      "cashTypeId": 18
    },
    {
      "id": "19",
      "transactionType": "addition",
      "numberOfBundles": 60,
      "reason": null,
      "cashTypeId": 19
    },
    {
      "id": "20",
      "transactionType": "addition",
      "numberOfBundles": 30,
      "reason": null,
      "cashTypeId": 20
    },
    {
      "id": "21",
      "transactionType": "addition",
      "numberOfBundles": 5,
      "reason": null,
      "cashTypeId": 21
    },
    {
      "id": "22",
      "transactionType": "addition",
      "numberOfBundles": 15,
      "reason": null,
      "cashTypeId": 22
    },
    {
      "id": "23",
      "transactionType": "addition",
      "numberOfBundles": 25,
      "reason": null,
      "cashTypeId": 23
    },
    {
      "id": "24",
      "transactionType": "addition",
      "numberOfBundles": 20,
      "reason": null,
      "cashTypeId": 24
    },
    {
      "id": "25",
      "transactionType": "addition",
      "numberOfBundles": 35,
      "reason": null,
      "cashTypeId": 25
    },
    {
      "id": "26",
      "transactionType": "addition",
      "numberOfBundles": 45,
      "reason": null,
      "cashTypeId": 26
    },
    {
      "id": "27",
      "transactionType": "addition",
      "numberOfBundles": 30,
      "reason": null,
      "cashTypeId": 27
    },
    {
      "id": "28",
      "transactionType": "addition",
      "numberOfBundles": 10,
      "reason": null,
      "cashTypeId": 28
    },
    {
      "id": "29",
      "transactionType": "addition",
      "numberOfBundles": 5,
      "reason": null,
      "cashTypeId": 29
    },
    {
      "id": "30",
      "transactionType": "addition",
      "numberOfBundles": 35,
      "reason": null,
      "cashTypeId": 30
    },
    {
      "id": "31",
      "transactionType": "addition",
      "numberOfBundles": 25,
      "reason": null,
      "cashTypeId": 31
    },
    {
      "id": "32",
      "transactionType": "addition",
      "numberOfBundles": 20,
      "reason": null,
      "cashTypeId": 32
    }
  ],
  "singleNotes": [
    {
      "id": "1",
      "transactionType": "addition",
      "numberOfSingleNotes": 65,
      "reason": null,
      "cashTypeId": 1
    },
    {
      "id": "2",
      "transactionType": "addition",
      "numberOfSingleNotes": 8,
      "reason": null,
      "cashTypeId": 2
    },
    {
      "id": "3",
      "transactionType": "addition",
      "numberOfSingleNotes": 78,
      "reason": null,
      "cashTypeId": 3
    },
    {
      "id": "4",
      "transactionType": "addition",
      "numberOfSingleNotes": 54,
      "reason": null,
      "cashTypeId": 4
    },
    {
      "id": "5",
      "transactionType": "addition",
      "numberOfSingleNotes": 67,
      "reason": null,
      "cashTypeId": 5
    },
    {
      "id": "6",
      "transactionType": "addition",
      "numberOfSingleNotes": 27,
      "reason": null,
      "cashTypeId": 6
    },
    {
      "id": "7",
      "transactionType": "addition",
      "numberOfSingleNotes": 32,
      "reason": null,
      "cashTypeId": 7
    },
    {
      "id": "8",
      "transactionType": "addition",
      "numberOfSingleNotes": 85,
      "reason": null,
      "cashTypeId": 8
    },
    {
      "id": "9",
      "transactionType": "addition",
      "numberOfSingleNotes": 22,
      "reason": null,
      "cashTypeId": 9
    },
    {
      "id": "10",
      "transactionType": "addition",
      "numberOfSingleNotes": 86,
      "reason": null,
      "cashTypeId": 10
    },
    {
      "id": "11",
      "transactionType": "addition",
      "numberOfSingleNotes": 0,
      "reason": null,
      "cashTypeId": 11
    },
    {
      "id": "12",
      "transactionType": "addition",
      "numberOfSingleNotes": 0,
      "reason": null,
      "cashTypeId": 12
    },
    {
      "id": "13",
      "transactionType": "addition",
      "numberOfSingleNotes": 0,
      "reason": null,
      "cashTypeId": 13
    },
    {
      "id": "14",
      "transactionType": "addition",
      "numberOfSingleNotes": 0,
      "reason": null,
      "cashTypeId": 14
    },
    {
      "id": "15",
      "transactionType": "addition",
      "numberOfSingleNotes": 0,
      "reason": null,
      "cashTypeId": 15
    },
    {
      "id": "16",
      "transactionType": "addition",
      "numberOfSingleNotes": 36,
      "reason": null,
      "cashTypeId": 16
    },
    {
      "id": "17",
      "transactionType": "addition",
      "numberOfSingleNotes": 46,
      "reason": null,
      "cashTypeId": 17
    },
    {
      "id": "18",
      "transactionType": "addition",
      "numberOfSingleNotes": 65,
      "reason": null,
      "cashTypeId": 18
    },
    {
      "id": "19",
      "transactionType": "addition",
      "numberOfSingleNotes": 43,
      "reason": null,
      "cashTypeId": 19
    },
    {
      "id": "20",
      "transactionType": "addition",
      "numberOfSingleNotes": 33,
      "reason": null,
      "cashTypeId": 20
    },
    {
      "id": "21",
      "transactionType": "addition",
      "numberOfSingleNotes": 13,
      "reason": null,
      "cashTypeId": 21
    },
    {
      "id": "22",
      "transactionType": "addition",
      "numberOfSingleNotes": 22,
      "reason": null,
      "cashTypeId": 22
    },
    {
      "id": "23",
      "transactionType": "addition",
      "numberOfSingleNotes": 12,
      "reason": null,
      "cashTypeId": 23
    },
    {
      "id": "24",
      "transactionType": "addition",
      "numberOfSingleNotes": 13,
      "reason": null,
      "cashTypeId": 24
    },
    {
      "id": "25",
      "transactionType": "addition",
      "numberOfSingleNotes": 6,
      "reason": null,
      "cashTypeId": 25
    },
    {
      "id": "26",
      "transactionType": "addition",
      "numberOfSingleNotes": 8,
      "reason": null,
      "cashTypeId": 26
    },
    {
      "id": "27",
      "transactionType": "addition",
      "numberOfSingleNotes": 4,
      "reason": null,
      "cashTypeId": 27
    },
    {
      "id": "28",
      "transactionType": "addition",
      "numberOfSingleNotes": 11,
      "reason": null,
      "cashTypeId": 28
    },
    {
      "id": "29",
      "transactionType": "addition",
      "numberOfSingleNotes": 13,
      "reason": null,
      "cashTypeId": 29
    },
    {
      "id": "30",
      "transactionType": "addition",
      "numberOfSingleNotes": 15,
      "reason": null,
      "cashTypeId": 30
    },
    {
      "id": "31",
      "transactionType": "addition",
      "numberOfSingleNotes": 8,
      "reason": null,
      "cashTypeId": 31
    },
    {
      "id": "32",
      "transactionType": "addition",
      "numberOfSingleNotes": 19,
      "reason": null,
      "cashTypeId": 32
    }
  ],
  "departments": [
    {
      "id": "1",
      "name": "FFA"
    },
    {
      "id": "2",
      "name": "BCD"
    },
    {
      "id": "3",
      "name": "ATM"
    }
  ],
  "auditLog": [
    {
      "id": "1",
      "userId": 1,
      "action": "created_request",
      "resourceType": "cashRequest",
      "resourceId": "0001",
      "details": "Created new cash request for R612.00",
      "timestamp": "2024-03-05T08:30:00Z"
    }
  ],
  "query": [],
  "bundleQuery": [],
  "singleNoteQuery": []
}
