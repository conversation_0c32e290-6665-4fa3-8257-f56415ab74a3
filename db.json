{"users": [{"id": "1", "name": "<PERSON><PERSON>", "department": "FFA", "abnumber": "AB004", "email": "<EMAIL>", "password": "<PERSON><PERSON>@1234", "description": "Manage your cash requests effectively and securely", "role": "Cash-Requester", "avatar": "https://www.pexels.com/photo/contemplative-young-ethnic-man-with-sunlight-on-face-looking-at-camera-in-studio-5490276/"}, {"id": "2", "name": "<PERSON><PERSON><PERSON><PERSON>", "department": "ATM", "abnumber": "AB005", "email": "<EMAIL>", "password": "<PERSON><PERSON><PERSON><PERSON>@1234", "description": "Cash Management - Issue and Approve Cash Requests", "role": "Cash-Issuer", "avatar": "https://www.pexels.com/photo/smiling-black-model-with-afro-braids-on-gray-background-7275385/"}, {"id": "3", "name": "<PERSON><PERSON>", "department": "BCD", "abnumber": "AB006", "email": "<EMAIL>", "password": "Bennet@1234", "description": "Manage your cash requests effectively and securely", "role": "Cash-Requester", "avatar": "https://www.pexels.com/photo/thoughtful-young-man-looking-away-6274712/"}, {"id": "4", "name": "Ma<PERSON><PERSON>", "department": "FFA", "abnumber": "AB007", "email": "<EMAIL>", "password": "Maboku@1234", "description": "Manage your cash requests effectively and securely", "role": "Cash-Requester", "avatar": "https://www.pexels.com/photo/man-in-white-crew-neck-shirt-5082976/"}, {"id": "5", "name": "Tshepang Sebogodi", "department": "ATM", "abnumber": "AB008", "email": "<EMAIL>", "password": "Tshepang@1234", "description": "Manage your cash requests effectively and securely", "role": "Cash-Requester", "avatar": "https://www.pexels.com/photo/young-black-lady-looking-at-camera-in-brown-studio-5615665/"}, {"id": "6", "name": "Malcom Finn", "department": "ATM", "abnumber": "AB009", "email": "<EMAIL>", "password": "Malcom@1234", "description": "Manager Dashboard - Super Admin Access", "role": "admin", "avatar": "https://www.pexels.com/photo/smiling-bearded-ethnic-man-looking-at-camera-5876516/"}, {"id": "5b87"}, {"id": "a0a3"}, {"id": "f060"}, {"id": "6792"}], "requests": [{"id": "1", "cashTotal": 7000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2025-01-10T10:00:00Z", "createdAt": "2024-03-05T08:30:00Z", "updatedAt": null}, {"id": "2", "requesterName": "<PERSON><PERSON>", "cashTotal": 22010, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Issued", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2025-01-15T12:00:00Z", "createdAt": "2025-01-03T11:00:00Z", "updatedAt": null}, {"id": "3", "requesterName": "<PERSON><PERSON>", "cashTotal": 2000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2025-02-20T14:00:00Z", "createdAt": "2025-03-18T08:45:00Z", "updatedAt": null}, {"id": "4", "requesterName": "<PERSON><PERSON>", "cashTotal": 15000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2025-04-29T06:30:00Z", "createdAt": "2025-04-29T06:30:00Z", "updatedAt": null}, {"id": "5", "requesterName": "<PERSON><PERSON>", "cashTotal": 11050, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Approved", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2025-06-12T10:20:00Z", "createdAt": "2025-06-12T10:20:00Z", "updatedAt": null}, {"id": "6", "requesterName": "<PERSON><PERSON>", "cashTotal": 10010, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2025-07-04T08:10:00Z", "createdAt": "2025-07-04T08:10:00Z", "updatedAt": null}, {"id": "7", "requesterName": "Tshepang Sebogodi", "cashTotal": 12200, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Approved", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2025-06-15T13:59:00Z", "createdAt": "2025-06-15T13:59:00Z", "updatedAt": null}, {"id": "8", "requesterName": "Tshepang Sebogodi", "cashTotal": 21000, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2025-10-05T05:05:00Z", "createdAt": "2025-10-05T05:05:00Z", "updatedAt": null}, {"id": "9", "requesterName": "Tshepang Sebogodi", "cashTotal": 20200, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Issued", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2025-02-28T12:30:00Z", "createdAt": "2025-02-28T12:30:00Z", "updatedAt": null}, {"id": "10", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 5520, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Rejected", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-08-01T11:55:00Z", "createdAt": "2025-08-01T11:55:00Z", "updatedAt": null}, {"id": "11", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 25000, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-09-21T14:35:00Z", "createdAt": "2025-09-21T14:35:00Z", "updatedAt": null}, {"id": "12", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 22020, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Approved", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-10-30T10:50:00Z", "createdAt": "2025-10-30T10:50:00Z", "updatedAt": null}, {"id": "13", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 1500, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-12-25T00:00:00Z", "createdAt": "2025-12-25T00:00:00Z", "updatedAt": null}, {"id": "14", "requesterName": "<PERSON><PERSON>", "cashTotal": 30000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2024-06-15T13:59:00Z", "createdAt": "2024-06-15T13:59:00Z", "updatedAt": null}, {"id": "15", "requesterName": "<PERSON><PERSON>", "cashTotal": 12000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Rejected", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2024-10-05T05:05:00Z", "createdAt": "2024-10-05T05:05:00Z", "updatedAt": null}, {"id": "16", "requesterName": "Tshepang Sebogodi", "cashTotal": 3000, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Issued", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2024-11-10T09:15:00Z", "createdAt": "2024-11-10T09:15:00Z", "updatedAt": null}, {"id": "17", "requesterName": "Tshepang Sebogodi", "cashTotal": 10000, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Approved", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2024-08-01T11:55:00Z", "createdAt": "2024-08-01T11:55:00Z", "updatedAt": null}, {"id": "18", "requesterName": "<PERSON><PERSON>", "cashTotal": 10, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2024-02-14T07:25:00Z", "createdAt": "2024-02-14T07:25:00Z", "updatedAt": null}, {"id": "19", "requesterName": "<PERSON><PERSON>", "cashTotal": 30000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Issued", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2024-05-22T13:40:00Z", "createdAt": "2024-05-22T13:40:00Z", "updatedAt": null}, {"id": "20", "requesterName": "<PERSON><PERSON>", "cashTotal": 6000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2024-08-01T11:54:00Z", "createdAt": "2024-08-01T11:54:00Z", "updatedAt": null}, {"id": "21", "requesterName": "<PERSON><PERSON>", "cashTotal": 10100, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Approved", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2024-03-14T10:20:00Z", "createdAt": "2024-03-14T10:20:00Z", "updatedAt": null}, {"id": "22", "requesterName": "<PERSON><PERSON>", "cashTotal": 7100, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Rejected", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2024-09-08T14:50:00Z", "createdAt": "2024-09-08T14:50:00Z", "updatedAt": null}, {"id": "23", "requesterName": "Tshepang Sebogodi", "cashTotal": 6000, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2025-01-27T09:35:00Z", "createdAt": "2025-01-27T09:35:00Z", "updatedAt": null}, {"id": "24", "requesterName": "Tshepang Sebogodi", "cashTotal": 11000, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2024-04-11T07:10:00Z", "createdAt": "2024-04-11T07:10:00Z", "updatedAt": null}, {"id": "25", "requesterName": "Tshepang Sebogodi", "cashTotal": 3050, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Issued", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2024-07-03T12:25:00Z", "createdAt": "2024-07-03T12:25:00Z", "updatedAt": null}, {"id": "26", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 20000, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Approved", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-04-29T06:30:00Z", "createdAt": "2024-12-10T11:50:00Z", "updatedAt": null}, {"id": "27", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 15000, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-03-01T06:45:00Z", "createdAt": "2025-03-01T06:45:00Z", "updatedAt": null}, {"id": "28", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 2200, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-05-10T13:10:00Z", "createdAt": "2025-05-10T13:10:00Z", "updatedAt": null}, {"id": "29", "requesterName": "<PERSON><PERSON>", "cashTotal": 25000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Approved", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2024-07-18T08:00:00Z", "createdAt": "2024-07-18T08:00:00Z", "updatedAt": null}, {"id": "30", "requesterName": "<PERSON><PERSON>", "cashTotal": 220, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Pending", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2024-05-02T10:45:00Z", "createdAt": "2024-05-02T10:45:00Z", "updatedAt": null}, {"id": "31", "requesterName": "<PERSON><PERSON>", "cashTotal": 2000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Rejected", "requesterComments": null, "issuerComments": null, "userId": 3, "expectedReturn": "2025-02-28T12:30:00Z", "createdAt": "2025-02-06T14:15:00Z", "updatedAt": null}, {"id": "32", "requesterName": "<PERSON><PERSON>", "cashTotal": 5000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Completed", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2024-11-19T07:35:00Z", "createdAt": "2024-11-19T07:35:00Z", "updatedAt": null}, {"id": "33", "requesterName": "<PERSON><PERSON>", "cashTotal": 30000, "issuedTo": "<PERSON><PERSON>", "requestStatus": "Issued", "requesterComments": null, "issuerComments": null, "userId": 1, "expectedReturn": "2025-06-08T09:50:00Z", "createdAt": "2025-06-08T09:50:00Z", "updatedAt": null}, {"id": "34", "requesterName": "Tshepang Sebogodi", "cashTotal": 7000, "issuedTo": "Tshepang Sebogodi", "requestStatus": "Rejected", "requesterComments": null, "issuerComments": null, "userId": 5, "expectedReturn": "2025-12-25T00:00:00Z", "createdAt": "2024-09-14T11:00:00Z", "updatedAt": null}, {"id": "35", "requesterName": "Ma<PERSON><PERSON>", "cashTotal": 22000, "issuedTo": "Ma<PERSON><PERSON>", "requestStatus": "Issued", "requesterComments": null, "issuerComments": null, "userId": 4, "expectedReturn": "2025-04-06T12:15:00Z", "createdAt": "2025-04-06T12:15:00Z", "updatedAt": null}], "requestCashTypes": [{"id": "1", "requestId": 1, "cashTypeId": 3}, {"id": "2", "requestId": 1, "cashTypeId": 7}, {"id": "3", "requestId": 2, "cashTypeId": 5}, {"id": "4", "requestId": 2, "cashTypeId": 12}, {"id": "5", "requestId": 2, "cashTypeId": 21}, {"id": "6", "requestId": 3, "cashTypeId": 1}, {"id": "7", "requestId": 3, "cashTypeId": 16}, {"id": "8", "requestId": 4, "cashTypeId": 8}, {"id": "9", "requestId": 4, "cashTypeId": 14}, {"id": "10", "requestId": 5, "cashTypeId": 4}, {"id": "11", "requestId": 5, "cashTypeId": 11}, {"id": "12", "requestId": 5, "cashTypeId": 23}, {"id": "13", "requestId": 6, "cashTypeId": 9}, {"id": "14", "requestId": 6, "cashTypeId": 27}, {"id": "15", "requestId": 7, "cashTypeId": 2}, {"id": "16", "requestId": 7, "cashTypeId": 19}, {"id": "17", "requestId": 7, "cashTypeId": 25}, {"id": "18", "requestId": 8, "cashTypeId": 6}, {"id": "19", "requestId": 8, "cashTypeId": 20}, {"id": "20", "requestId": 9, "cashTypeId": 10}, {"id": "21", "requestId": 9, "cashTypeId": 31}, {"id": "22", "requestId": 10, "cashTypeId": 13}, {"id": "23", "requestId": 10, "cashTypeId": 28}, {"id": "24", "requestId": 10, "cashTypeId": 32}, {"id": "25", "requestId": 11, "cashTypeId": 5}, {"id": "26", "requestId": 11, "cashTypeId": 18}, {"id": "27", "requestId": 12, "cashTypeId": 22}, {"id": "28", "requestId": 12, "cashTypeId": 7}, {"id": "29", "requestId": 12, "cashTypeId": 15}, {"id": "30", "requestId": 13, "cashTypeId": 1}, {"id": "31", "requestId": 13, "cashTypeId": 26}, {"id": "32", "requestId": 14, "cashTypeId": 9}, {"id": "33", "requestId": 14, "cashTypeId": 20}, {"id": "34", "requestId": 15, "cashTypeId": 2}, {"id": "35", "requestId": 15, "cashTypeId": 14}, {"id": "36", "requestId": 16, "cashTypeId": 11}, {"id": "37", "requestId": 16, "cashTypeId": 17}, {"id": "38", "requestId": 17, "cashTypeId": 19}, {"id": "39", "requestId": 18, "cashTypeId": 21}, {"id": "40", "requestId": 19, "cashTypeId": 4}, {"id": "41", "requestId": 19, "cashTypeId": 15}, {"id": "42", "requestId": 20, "cashTypeId": 3}, {"id": "43", "requestId": 20, "cashTypeId": 6}, {"id": "44", "requestId": 21, "cashTypeId": 9}, {"id": "45", "requestId": 21, "cashTypeId": 24}, {"id": "46", "requestId": 22, "cashTypeId": 8}, {"id": "47", "requestId": 22, "cashTypeId": 12}, {"id": "48", "requestId": 22, "cashTypeId": 30}, {"id": "49", "requestId": 23, "cashTypeId": 13}, {"id": "50", "requestId": 23, "cashTypeId": 16}, {"id": "51", "requestId": 24, "cashTypeId": 1}, {"id": "52", "requestId": 24, "cashTypeId": 19}, {"id": "53", "requestId": 25, "cashTypeId": 7}, {"id": "54", "requestId": 25, "cashTypeId": 11}, {"id": "55", "requestId": 25, "cashTypeId": 29}, {"id": "56", "requestId": 26, "cashTypeId": 5}, {"id": "57", "requestId": 27, "cashTypeId": 3}, {"id": "58", "requestId": 27, "cashTypeId": 14}, {"id": "59", "requestId": 28, "cashTypeId": 2}, {"id": "60", "requestId": 28, "cashTypeId": 25}, {"id": "61", "requestId": 29, "cashTypeId": 10}, {"id": "62", "requestId": 29, "cashTypeId": 18}, {"id": "63", "requestId": 30, "cashTypeId": 22}, {"id": "64", "requestId": 30, "cashTypeId": 31}, {"id": "65", "requestId": 31, "cashTypeId": 1}, {"id": "66", "requestId": 31, "cashTypeId": 6}, {"id": "67", "requestId": 32, "cashTypeId": 8}, {"id": "68", "requestId": 33, "cashTypeId": 4}, {"id": "69", "requestId": 33, "cashTypeId": 20}, {"id": "70", "requestId": 34, "cashTypeId": 3}, {"id": "71", "requestId": 34, "cashTypeId": 12}, {"id": "72", "requestId": 35, "cashTypeId": 7}, {"id": "73", "requestId": 35, "cashTypeId": 15}], "cashTypes": [{"id": "1", "name": "Mandela Series", "value": 10, "description": "This is a R10 Mandela series note"}, {"id": "2", "name": "Mandela Series", "value": 20, "description": "This is a R20 Mandela series note"}, {"id": "3", "name": "Mandela Series", "value": 50, "description": "This is a R50 Mandela series note"}, {"id": "4", "name": "Mandela Series", "value": 100, "description": "This is a R100 Mandela series note"}, {"id": "5", "name": "Mandela Series", "value": 200, "description": "This is a R200 Mandela series note"}, {"id": "6", "name": "V6 Series", "value": 10, "description": "This is a R10 V6 series note"}, {"id": "7", "name": "V6 Series", "value": 20, "description": "This is a R20 V6 series note"}, {"id": "8", "name": "V6 Series", "value": 50, "description": "This is a R50 V6 series note"}, {"id": "9", "name": "V6 Series", "value": 100, "description": "This is a R100 V6 series note"}, {"id": "10", "name": "V6 Series", "value": 200, "description": "This is a R200 V6 series note"}, {"id": "11", "name": "Big 5 Series", "value": 10, "description": "This is a R10 Big 5 series note"}, {"id": "12", "name": "Big 5 Series", "value": 20, "description": "This is a R20 Big 5 series note"}, {"id": "13", "name": "Big 5 Series", "value": 50, "description": "This is a R50 Big 5 series note"}, {"id": "14", "name": "Big 5 Series", "value": 100, "description": "This is a R100 Big 5 series note"}, {"id": "15", "name": "Big 5 Series", "value": 200, "description": "This is a R200 Big 5 series note"}, {"id": "16", "name": "Commemorative Series", "value": 10, "description": "This is a R10 Commemorative series note"}, {"id": "17", "name": "Commemorative Series", "value": 20, "description": "This is a R20 Commemorative series note"}, {"id": "18", "name": "Commemorative Series", "value": 50, "description": "This is a R50 Commemorative series note"}, {"id": "19", "name": "Commemorative Series", "value": 100, "description": "This is a R100 Commemorative series note"}, {"id": "20", "name": "Commemorative Series", "value": 200, "description": "This is a R200 Commemorative series note"}, {"id": "21", "name": "Old Coins", "value": 0.1, "description": "This is an old coin 10c"}, {"id": "22", "name": "Old Coins", "value": 0.2, "description": "This is an old coin 20c"}, {"id": "23", "name": "Old Coins", "value": 0.5, "description": "This is an old coin 50c"}, {"id": "24", "name": "Old Coins", "value": 1, "description": "This is an old coin R1"}, {"id": "25", "name": "Old Coins", "value": 2, "description": "This is an old coin R2"}, {"id": "26", "name": "Old Coins", "value": 5, "description": "This is an old coin R5"}, {"id": "27", "name": "New Coins", "value": 0.1, "description": "This is an New coin 10c"}, {"id": "28", "name": "New Coins", "value": 0.2, "description": "This is an New coin 20c"}, {"id": "29", "name": "New Coins", "value": 0.5, "description": "This is an New coin 50c"}, {"id": "30", "name": "New Coins", "value": 1, "description": "This is an New coin R1"}, {"id": "31", "name": "New Coins", "value": 2, "description": "This is an New coin R2"}, {"id": "32", "name": "New Coins", "value": 5, "description": "This is an New coin R5"}], "bundles": [{"id": "1", "transactionType": "addition", "numberOfBundles": 100, "reason": null, "cashTypeId": 1}, {"id": "2", "transactionType": "addition", "numberOfBundles": 60, "reason": null, "cashTypeId": 2}, {"id": "3", "transactionType": "addition", "numberOfBundles": 80, "reason": null, "cashTypeId": 3}, {"id": "4", "transactionType": "addition", "numberOfBundles": 10, "reason": null, "cashTypeId": 4}, {"id": "5", "transactionType": "addition", "numberOfBundles": 30, "reason": null, "cashTypeId": 5}, {"id": "6", "transactionType": "addition", "numberOfBundles": 25, "reason": null, "cashTypeId": 6}, {"id": "7", "transactionType": "addition", "numberOfBundles": 70, "reason": null, "cashTypeId": 7}, {"id": "8", "transactionType": "addition", "numberOfBundles": 40, "reason": null, "cashTypeId": 8}, {"id": "9", "transactionType": "addition", "numberOfBundles": 45, "reason": null, "cashTypeId": 9}, {"id": "10", "transactionType": "addition", "numberOfBundles": 35, "reason": null, "cashTypeId": 10}, {"id": "11", "transactionType": "addition", "numberOfBundles": 2, "reason": null, "cashTypeId": 11}, {"id": "12", "transactionType": "addition", "numberOfBundles": 0, "reason": null, "cashTypeId": 12}, {"id": "13", "transactionType": "addition", "numberOfBundles": 1, "reason": null, "cashTypeId": 13}, {"id": "14", "transactionType": "addition", "numberOfBundles": 0, "reason": null, "cashTypeId": 14}, {"id": "15", "transactionType": "addition", "numberOfBundles": 0, "reason": null, "cashTypeId": 15}, {"id": "16", "transactionType": "addition", "numberOfBundles": 20, "reason": null, "cashTypeId": 16}, {"id": "17", "transactionType": "addition", "numberOfBundles": 45, "reason": null, "cashTypeId": 17}, {"id": "18", "transactionType": "addition", "numberOfBundles": 50, "reason": null, "cashTypeId": 18}, {"id": "19", "transactionType": "addition", "numberOfBundles": 60, "reason": null, "cashTypeId": 19}, {"id": "20", "transactionType": "addition", "numberOfBundles": 30, "reason": null, "cashTypeId": 20}, {"id": "21", "transactionType": "addition", "numberOfBundles": 5, "reason": null, "cashTypeId": 21}, {"id": "22", "transactionType": "addition", "numberOfBundles": 15, "reason": null, "cashTypeId": 22}, {"id": "23", "transactionType": "addition", "numberOfBundles": 25, "reason": null, "cashTypeId": 23}, {"id": "24", "transactionType": "addition", "numberOfBundles": 20, "reason": null, "cashTypeId": 24}, {"id": "25", "transactionType": "addition", "numberOfBundles": 35, "reason": null, "cashTypeId": 25}, {"id": "26", "transactionType": "addition", "numberOfBundles": 45, "reason": null, "cashTypeId": 26}, {"id": "27", "transactionType": "addition", "numberOfBundles": 30, "reason": null, "cashTypeId": 27}, {"id": "28", "transactionType": "addition", "numberOfBundles": 10, "reason": null, "cashTypeId": 28}, {"id": "29", "transactionType": "addition", "numberOfBundles": 5, "reason": null, "cashTypeId": 29}, {"id": "30", "transactionType": "addition", "numberOfBundles": 35, "reason": null, "cashTypeId": 30}, {"id": "31", "transactionType": "addition", "numberOfBundles": 25, "reason": null, "cashTypeId": 31}, {"id": "32", "transactionType": "addition", "numberOfBundles": 20, "reason": null, "cashTypeId": 32}], "singleNotes": [{"id": "1", "transactionType": "addition", "numberOfSingleNotes": 65, "reason": null, "cashTypeId": 1}, {"id": "2", "transactionType": "addition", "numberOfSingleNotes": 8, "reason": null, "cashTypeId": 2}, {"id": "3", "transactionType": "addition", "numberOfSingleNotes": 78, "reason": null, "cashTypeId": 3}, {"id": "4", "transactionType": "addition", "numberOfSingleNotes": 54, "reason": null, "cashTypeId": 4}, {"id": "5", "transactionType": "addition", "numberOfSingleNotes": 67, "reason": null, "cashTypeId": 5}, {"id": "6", "transactionType": "addition", "numberOfSingleNotes": 27, "reason": null, "cashTypeId": 6}, {"id": "7", "transactionType": "addition", "numberOfSingleNotes": 32, "reason": null, "cashTypeId": 7}, {"id": "8", "transactionType": "addition", "numberOfSingleNotes": 85, "reason": null, "cashTypeId": 8}, {"id": "9", "transactionType": "addition", "numberOfSingleNotes": 22, "reason": null, "cashTypeId": 9}, {"id": "10", "transactionType": "addition", "numberOfSingleNotes": 86, "reason": null, "cashTypeId": 10}, {"id": "11", "transactionType": "addition", "numberOfSingleNotes": 0, "reason": null, "cashTypeId": 11}, {"id": "12", "transactionType": "addition", "numberOfSingleNotes": 0, "reason": null, "cashTypeId": 12}, {"id": "13", "transactionType": "addition", "numberOfSingleNotes": 0, "reason": null, "cashTypeId": 13}, {"id": "14", "transactionType": "addition", "numberOfSingleNotes": 0, "reason": null, "cashTypeId": 14}, {"id": "15", "transactionType": "addition", "numberOfSingleNotes": 0, "reason": null, "cashTypeId": 15}, {"id": "16", "transactionType": "addition", "numberOfSingleNotes": 36, "reason": null, "cashTypeId": 16}, {"id": "17", "transactionType": "addition", "numberOfSingleNotes": 46, "reason": null, "cashTypeId": 17}, {"id": "18", "transactionType": "addition", "numberOfSingleNotes": 65, "reason": null, "cashTypeId": 18}, {"id": "19", "transactionType": "addition", "numberOfSingleNotes": 43, "reason": null, "cashTypeId": 19}, {"id": "20", "transactionType": "addition", "numberOfSingleNotes": 33, "reason": null, "cashTypeId": 20}, {"id": "21", "transactionType": "addition", "numberOfSingleNotes": 13, "reason": null, "cashTypeId": 21}, {"id": "22", "transactionType": "addition", "numberOfSingleNotes": 22, "reason": null, "cashTypeId": 22}, {"id": "23", "transactionType": "addition", "numberOfSingleNotes": 12, "reason": null, "cashTypeId": 23}, {"id": "24", "transactionType": "addition", "numberOfSingleNotes": 13, "reason": null, "cashTypeId": 24}, {"id": "25", "transactionType": "addition", "numberOfSingleNotes": 6, "reason": null, "cashTypeId": 25}, {"id": "26", "transactionType": "addition", "numberOfSingleNotes": 8, "reason": null, "cashTypeId": 26}, {"id": "27", "transactionType": "addition", "numberOfSingleNotes": 4, "reason": null, "cashTypeId": 27}, {"id": "28", "transactionType": "addition", "numberOfSingleNotes": 11, "reason": null, "cashTypeId": 28}, {"id": "29", "transactionType": "addition", "numberOfSingleNotes": 13, "reason": null, "cashTypeId": 29}, {"id": "30", "transactionType": "addition", "numberOfSingleNotes": 15, "reason": null, "cashTypeId": 30}, {"id": "31", "transactionType": "addition", "numberOfSingleNotes": 8, "reason": null, "cashTypeId": 31}, {"id": "32", "transactionType": "addition", "numberOfSingleNotes": 19, "reason": null, "cashTypeId": 32}], "departments": [{"id": "1", "name": "FFA"}, {"id": "2", "name": "BCD"}, {"id": "3", "name": "ATM"}], "auditLog": [{"id": "1", "userId": 1, "action": "created_request", "resourceType": "cashRequest", "resourceId": "0001", "details": "Created new cash request for R612.00", "timestamp": "2024-03-05T08:30:00Z"}], "query": [], "bundleQuery": [], "singleNoteQuery": []}