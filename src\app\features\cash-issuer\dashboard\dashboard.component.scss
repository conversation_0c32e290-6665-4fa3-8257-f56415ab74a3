// ===== RESPONSIVE BREAKPOINTS =====
@mixin mobile-small {
  @media (max-width: 479px) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: 639px) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

// ===== CASH ISSUER DASHBOARD CONTAINER =====
.cash-issuer-dashboard-container {
  min-height: 100vh;
  background: white;
  padding: 2rem;
  position: relative;

  @include tablet {
    padding: 1.5rem;
  }

  @include mobile {
    padding: 1rem;
  }


}

// ===== DASHBOARD HEADER =====
.dashboard-header {
  position: relative;
  z-index: 1;
  margin-bottom: 3rem;
  text-align: center;

  @include tablet {
    margin-bottom: 2rem;
  }

  @include mobile {
    margin-bottom: 1.5rem;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    letter-spacing: -0.025em;

    @include tablet {
      font-size: 2rem;
    }

    @include mobile {
      font-size: 1.75rem;
    }

    @include mobile-small {
      font-size: 1.5rem;
    }
  }

  p {
    font-size: 1.125rem;
    color: #6b7280;
    margin: 0.5rem 0 0 0;

    @include mobile {
      font-size: 1rem;
    }
  }
}

// ===== DASHBOARD CONTENT =====
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;

  @include tablet {
    gap: 2rem;
  }

  @include mobile {
    gap: 1.5rem;
  }
}

// ===== UNIFIED CONTENT CARD =====
.unified-content-card {
  background: white;
  border-radius: 16px;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;

  @include mobile {
    border-radius: 12px;
  }

  // Override the landing board component's card styling to remove duplicate background
  ::ng-deep app-landingboard {
    .dashboard-card {
      background: transparent;
      box-shadow: none;
      border-radius: 0;
      margin-top: 0;
    }
  }
}

// ===== QUICK ACTIONS SECTION =====
.quick-actions-section {
  padding: 2rem;
  border-top: 1px solid #e5e7eb;

  @include tablet {
    padding: 1.5rem;
  }

  @include mobile {
    padding: 1rem;
  }

  .section-header {
    margin-bottom: 2rem;

    @include mobile {
      margin-bottom: 1.5rem;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      mat-icon {
        color: #10b981;
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }

      h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;

        @include mobile {
          font-size: 1.25rem;
        }
      }
    }
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;

    @include tablet {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }
}

// ===== ACTION CARDS =====
.action-card {
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1);

  @include mobile {
    border-radius: 8px;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow:
      0 12px 24px rgba(0, 0, 0, 0.1),
      0 4px 8px rgba(0, 0, 0, 0.06);

    .card-background {
      opacity: 1;
      transform: scale(1.05);
    }

    .card-arrow {
      transform: translateX(4px);
      background: rgba(0, 0, 0, 0.1);

      mat-icon {
        color: #374151;
      }
    }
  }

  &:active {
    transform: translateY(-2px);
  }

  .card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 0;
  }

  &.primary .card-background {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  }

  &.secondary .card-background {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  }

  ::ng-deep .mat-mdc-card-content {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    z-index: 1;

    @include tablet {
      padding: 1.5rem;
      gap: 1rem;
    }

    @include mobile {
      padding: 1rem;
      gap: 0.75rem;
    }
  }

  .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    @include mobile {
      width: 50px;
      height: 50px;
      border-radius: 10px;
    }

    mat-icon {
      font-size: 1.75rem;
      width: 1.75rem;
      height: 1.75rem;

      @include mobile {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }
  }

  &.primary .card-icon {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid rgba(16, 185, 129, 0.2);

    mat-icon {
      color: #10b981;
    }
  }

  &.secondary .card-icon {
    background: rgba(245, 158, 11, 0.1);
    border: 2px solid rgba(245, 158, 11, 0.2);

    mat-icon {
      color: #f59e0b;
    }
  }

  .card-content {
    flex: 1;

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 0.5rem 0;
      line-height: 1.2;

      @include mobile {
        font-size: 1.125rem;
      }
    }

    p {
      font-size: 0.875rem;
      color: #6b7280;
      margin: 0;
      line-height: 1.4;

      @include mobile {
        font-size: 0.8125rem;
      }
    }
  }

  .card-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    @include mobile {
      width: 28px;
      height: 28px;
    }

    mat-icon {
      font-size: 1.125rem;
      width: 1.125rem;
      height: 1.125rem;
      color: #9ca3af;
      transition: color 0.3s ease;

      @include mobile {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }

  &:hover .card-arrow {
    background: rgba(0, 0, 0, 0.1);

    mat-icon {
      color: #374151;
    }
  }
}

// ===== RESPONSIVE ADJUSTMENTS =====
@include tablet {
  .action-card {
    ::ng-deep .mat-mdc-card-content {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .card-content {
      align-items: center;
      text-align: center;
    }

    .card-arrow {
      align-self: center;
    }
  }
}

@include mobile {
  .cash-issuer-dashboard-container {
    min-height: auto;
  }

  .action-card {
    ::ng-deep .mat-mdc-card-content {
      flex-direction: row;
      text-align: left;
    }

    .card-content {
      text-align: left;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  .action-buttons {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}
