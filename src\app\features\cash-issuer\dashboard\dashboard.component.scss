.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
  text-align: center;

  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #4f5455;
    margin-bottom: 10px;
  }

  p {
    font-size: 1.2rem;
    color: #808080;
  }
}

.dashboard-content {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr;
}

.quick-actions-card {
  margin-bottom: 20px;
  box-shadow: none;
  border: 1px solid #e0e0e0;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;

  button {
    min-width: 150px;
    height: 48px;
    font-size: 1rem;

    mat-icon {
      margin-right: 8px;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  .action-buttons {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}
