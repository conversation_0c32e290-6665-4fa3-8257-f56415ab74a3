import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { RemoveCashModalComponent } from './remove-cash-modal.component';
import { NoteSeries, NoteDenomination } from '../../../../shared/models/inventory.model';

describe('RemoveCashModalComponent', () => {
  let component: RemoveCashModalComponent;
  let fixture: ComponentFixture<RemoveCashModalComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<RemoveCashModalComponent>>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockSnackBar = jasmine.createSpyObj('MatSnackBar', ['open']);

    await TestBed.configureTestingModule({
      imports: [
        RemoveCashModalComponent,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: MatSnackBar, useValue: mockSnackBar }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(RemoveCashModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.selectedSeries).toBeNull();
    expect(component.selectedDenomination).toBeNull();
    expect(component.batches).toBe(0);
    expect(component.singles).toBe(0);
    expect(component.totalQuantity).toBe(0);
  });

  it('should pre-populate data when provided', () => {
    const testData = {
      series: NoteSeries.Y6,
      denomination: NoteDenomination.R100,
      currentQuantity: 500
    };

    component = new RemoveCashModalComponent(
      mockDialogRef,
      testData,
      mockSnackBar,
      jasmine.createSpyObj('Renderer2', ['setStyle', 'addClass', 'removeClass']),
      jasmine.createSpyObj('ElementRef', ['nativeElement'])
    );

    expect(component.selectedSeries).toBe(NoteSeries.Y6);
    expect(component.selectedDenomination).toBe(NoteDenomination.R100);
    expect(component.currentQuantity).toBe(500);
  });

  it('should calculate values correctly', () => {
    component.selectedDenomination = NoteDenomination.R100;
    component.currentQuantity = 500;
    component.batches = 2;
    component.singles = 50;
    
    component['calculateValues']();

    expect(component.totalQuantity).toBe(250); // 2 * 100 + 50
    expect(component.remainingQuantity).toBe(250); // 500 - 250
    expect(component.totalValue).toBe(25000); // 250 * 100
  });

  it('should validate form correctly', () => {
    // Invalid form - no series/denomination selected
    expect(component.isFormValid()).toBeFalse();

    // Valid form
    component.selectedSeries = NoteSeries.Y6;
    component.selectedDenomination = NoteDenomination.R100;
    component.currentQuantity = 500;
    component.batches = 1;
    component.totalQuantity = 100;

    expect(component.isFormValid()).toBeTrue();

    // Invalid - trying to remove more than available
    component.totalQuantity = 600;
    expect(component.isFormValid()).toBeFalse();
  });

  it('should handle cancel correctly', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalledWith({ success: false });
  });

  it('should format currency correctly', () => {
    const formatted = component.formatCurrency(1234.56);
    expect(formatted).toContain('1');
    expect(formatted).toContain('234');
    expect(formatted).toContain('56');
  });

  it('should adjust batches correctly', () => {
    component.currentQuantity = 500;
    component.batches = 2;

    // Increase batches
    component.adjustBatches(1);
    expect(component.batches).toBe(3);

    // Decrease batches
    component.adjustBatches(-1);
    expect(component.batches).toBe(2);

    // Cannot go below 0
    component.batches = 0;
    component.adjustBatches(-1);
    expect(component.batches).toBe(0);
  });

  it('should adjust singles correctly', () => {
    component.currentQuantity = 250;
    component.batches = 2; // 200 notes
    component.singles = 25;

    // Increase singles
    component.adjustSingles(1);
    expect(component.singles).toBe(26);

    // Cannot exceed available singles
    component.singles = 50; // max available
    component.adjustSingles(1);
    expect(component.singles).toBe(50);

    // Decrease singles
    component.adjustSingles(-1);
    expect(component.singles).toBe(49);
  });
});
