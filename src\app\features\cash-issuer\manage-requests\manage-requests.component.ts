import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ManageRequestsService } from './manage-requests.service';
import { RequestStatsComponent } from '../../../shared/components/request-stats/request-stats.component';
import { LandingboardComponent } from '../../../shared/components/dashboard/landing-board.component';
import { QuickActionsComponent } from '../../../layout/quick-actions/quick-actions.component';

@Component({
  selector: 'app-manage-requests',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule,RequestStatsComponent,LandingboardComponent,QuickActionsComponent],
  templateUrl: './manage-requests.component.html',
  styleUrls: ['./manage-requests.component.scss']
})
export class ManageRequestsComponent implements OnInit {

  constructor(private manageRequestsService: ManageRequestsService) { }

  ngOnInit(): void {
    // Initialize component
  }
}
