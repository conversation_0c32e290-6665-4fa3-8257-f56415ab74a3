// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;

// Note: Color palettes are generated from primary: #95052a, secondary: #ffffff, tertiary: #dc0037
$_palettes: (
  primary: (
    0: #000000,
    10: #40000c,
    20: #68001a,
    25: #7c0021,
    30: #910128,
    35: #a31532,
    40: #b4243d,
    50: #d63f54,
    60: #f9586b,
    70: #ff8890,
    80: #ffb3b6,
    90: #ffdada,
    95: #ffedec,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #1a1c1c,
    20: #2f3131,
    25: #3a3c3c,
    30: #454747,
    35: #515353,
    40: #5d5f5f,
    50: #767777,
    60: #909191,
    70: #aaabab,
    80: #c6c6c7,
    90: #e2e2e2,
    95: #f0f1f1,
    98: #f9f9f9,
    99: #fcfcfc,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #400009,
    20: #680015,
    25: #7d001b,
    30: #920021,
    35: #a80028,
    40: #bf002e,
    50: #ea173f,
    60: #ff5260,
    70: #ff888b,
    80: #ffb3b3,
    90: #ffdad9,
    95: #ffedec,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #261819,
    20: #3c2c2d,
    25: #483738,
    30: #544243,
    35: #604e4e,
    40: #6d5a5a,
    50: #877272,
    60: #a28b8c,
    70: #bda6a6,
    80: #d9c1c1,
    90: #f7dcdd,
    95: #ffedec,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
    4: #170b0b,
    6: #1d1010,
    12: #2a1c1c,
    17: #352627,
    22: #413131,
    24: #463536,
    87: #eed4d4,
    92: #fce2e2,
    94: #ffe9e9,
    96: #fff0f0,
  ),
  neutral-variant: (
    0: #000000,
    10: #2a1617,
    20: #412b2b,
    25: #4d3536,
    30: #594041,
    35: #664c4d,
    40: #735858,
    50: #8d7071,
    60: #a8898a,
    70: #c4a3a4,
    80: #e1bebf,
    90: #fedada,
    95: #ffedec,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);