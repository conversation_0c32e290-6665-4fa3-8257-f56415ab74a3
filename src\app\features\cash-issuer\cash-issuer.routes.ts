import { Routes } from '@angular/router';
import { ManageRequestsComponent } from './manage-requests/manage-requests.component';
import { InventoryManagementComponent } from './inventory-management/inventory-management.component';
import { CashIssuerDashboardComponent } from './dashboard/dashboard.component';

export const cash_issuerRoutes: Routes = [
  { path: 'dashboard', component: CashIssuerDashboardComponent },
  { path: 'manage-requests', component: ManageRequestsComponent },
  { path: 'inventory-management', component: InventoryManagementComponent },
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' }
];
