import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DashboardService } from './dashboard.service';
import { LandingboardComponent } from "../../../shared/components/dashboard/landing-board.component";
import { RequestStatsComponent } from '../../../shared/components/request-stats/request-stats.component';
import { QuickActionsComponent } from '../../../layout/quick-actions/quick-actions.component';


@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, LandingboardComponent, RequestStatsComponent, QuickActionsComponent],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {

  constructor(private dashboardService: DashboardService) { }

  ngOnInit(): void {
    // Initialize component
  }
}
