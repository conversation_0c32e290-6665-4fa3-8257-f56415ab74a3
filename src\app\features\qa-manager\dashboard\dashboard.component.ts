import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { DashboardService } from './dashboard.service';
import { LandingboardComponent } from "../../../shared/components/dashboard/landing-board.component";
import { RequestStatsComponent } from '../../../shared/components/request-stats/request-stats.component';
import { AuditReportModalComponent } from '../components/audit-report-modal/audit-report-modal.component';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    LandingboardComponent,
    RequestStatsComponent
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {

  constructor(
    private dashboardService: DashboardService,
    private router: Router,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    // Initialize component
  }

  /**
   * Navigate to inventory overview page
   */
  onManageInventory(): void {
    this.router.navigate(['/qa-manager/inventory-overview']);
  }

  /**
   * Open audit report modal
   */
  onGenerateAuditReport(): void {
    const dialogRef = this.dialog.open(AuditReportModalComponent, {
      width: '95vw',
      maxWidth: '1400px',
      maxHeight: '90vh',
      panelClass: 'audit-report-dialog',
      disableClose: false,
      hasBackdrop: true,
      backdropClass: 'audit-report-backdrop'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Audit report generated:', result);
      }
    });
  }

  /**
   * Handle view system logs action (placeholder for future implementation)
   */
  onViewSystemLogs(): void {
    // Placeholder for future system logs functionality
    console.log('View System Logs - Coming Soon');
  }
}
