import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

// QA Manager Components
import { AddCashModalService } from '../components/add-cash-modal/add-cash-modal.service';
import type { AddCashResult, AddCashDialogData } from '../components/add-cash-modal/add-cash-modal.service';
import { RemoveCashModalService } from '../components/remove-cash-modal';
import type { RemoveCashResult, RemoveCashDialogData } from '../components/remove-cash-modal';
import { AddCoinModalService } from '../components/add-coin-modal';
import type { AddCoinResult, AddCoinDialogData } from '../components/add-coin-modal';
import { RemoveCoinModalService } from '../components/remove-coin-modal/remove-coin-modal.service';
import type { RemoveCoinResult, RemoveCoinDialogData } from '../components/remove-coin-modal/remove-coin-modal.component';
import { AuditReportModalService } from '../components/audit-report-modal/audit-report-modal.service';
import { NoteSeriesManagementModalService } from '../components/note-series-management-modal';

// Inventory Models
import {
  CashInventory,
  CoinSeries,
  CoinDenomination,
  CoinInventory,
  NoteSeries,
  NoteDenomination,
  NOTE_SERIES_LABELS,
  DENOMINATION_LABELS,
  COIN_SERIES_LABELS,
  COIN_DENOMINATION_LABELS,
  COIN_BATCH_CONFIG,
  COIN_BATCH_VALUES
} from '../../../shared/models/inventory.model';
import { InventoryService } from '../services/inventory.service';

// Shared Components
import { DetailedInventoryComponent } from '../../../shared/components/detailed-inventory';

// Inventory Mode Enum
export enum InventoryMode {
  NORMAL = 'normal',
  DYE_STAINED = 'dye_stained',
  COINS = 'coins'
}

@Component({
  selector: 'app-inventory-overview',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTabsModule,
    MatSnackBarModule,
    MatTooltipModule,
    DetailedInventoryComponent
  ],
  providers: [
    AuditReportModalService
  ],
  templateUrl: './inventory-overview.component.html',
  styleUrls: ['./inventory-overview.component.scss']
})
export class InventoryOverviewComponent implements OnInit {
  // Inventory data loaded from service
  inventorySummary: any = {
    totalValue: 0,
    totalNotes: 0,
    lowStockAlerts: [],
    outOfStockAlerts: []
  };

  // Separate inventory totals for each category
  cashInventoryTotal: number = 0;
  dyeStainedInventoryTotal: number = 0;
  coinInventoryTotal: number = 0;

  // Inventory data loaded from service
  inventoryData: any[] = [];

  // Make labels available to template
  NOTE_SERIES_LABELS = NOTE_SERIES_LABELS;

  // Make InventoryMode enum available in template
  readonly InventoryMode = InventoryMode;

  // Series data loaded from service
  seriesData: any[] = [];

  // Note series count for summary card
  noteSeriesCount: number = 0;

  // Fake notes series IDs for the detailed inventory component
  fakeNotesSeriesIds: string[] = [NoteSeries.FAKE_NOTES];

  // Inventory mode state
  currentInventoryMode: InventoryMode = InventoryMode.NORMAL;

  // Backward compatibility getter
  get isDyeStainedMode(): boolean {
    return this.currentInventoryMode === InventoryMode.DYE_STAINED;
  }

  // Coins mode getter
  get isCoinsMode(): boolean {
    return this.currentInventoryMode === InventoryMode.COINS;
  }

  // Default series data (will be replaced by actual data) - UNUSED
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private defaultSeriesData: any[] = [
    {
      id: 'mandela',
      name: 'Mandela Series',
      totalBatches: 122,
      totalSingles: 77,
      totalValue: 854040.00,
      denominations: [
        {
          value: 10,
          batches: 20,
          singles: 87,
          totalValue: 20870.00,
          stockLevel: 100
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 30,
          singles: 79,
          totalValue: 153950.00,
          stockLevel: 100
        },
        {
          value: 100,
          batches: 32,
          singles: 61,
          totalValue: 65220.00,
          stockLevel: 100
        },
        {
          value: 200,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        }
      ]
    },
    {
      id: 'big5',
      name: 'Big 5 Series',
      totalBatches: 95,
      totalSingles: 43,
      totalValue: 720500.00,
      denominations: [
        {
          value: 10,
          batches: 5,
          singles: 12,
          totalValue: 512.00,
          stockLevel: 25
        },
        {
          value: 20,
          batches: 25,
          singles: 43,
          totalValue: 50860.00,
          stockLevel: 85
        },
        {
          value: 50,
          batches: 8,
          singles: 15,
          totalValue: 4015.00,
          stockLevel: 35
        },
        {
          value: 100,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 200,
          batches: 18,
          singles: 22,
          totalValue: 362200.00,
          stockLevel: 45
        }
      ]
    },
    {
      id: 'commemorative',
      name: 'Commemorative Series',
      totalBatches: 45,
      totalSingles: 23,
      totalValue: 125000.00,
      denominations: [
        {
          value: 10,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 20,
          batches: 3,
          singles: 8,
          totalValue: 608.00,
          stockLevel: 15
        },
        {
          value: 50,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 100,
          batches: 12,
          singles: 8,
          totalValue: 120800.00,
          stockLevel: 75
        },
        {
          value: 200,
          batches: 2,
          singles: 5,
          totalValue: 4005.00,
          stockLevel: 8
        }
      ]
    },
    {
      id: 'v6',
      name: 'V6 Series',
      totalBatches: 67,
      totalSingles: 34,
      totalValue: 890000.00,
      denominations: [
        {
          value: 10,
          batches: 15,
          singles: 45,
          totalValue: 1545.00,
          stockLevel: 65
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 12,
          singles: 33,
          totalValue: 6033.00,
          stockLevel: 55
        },
        {
          value: 100,
          batches: 22,
          singles: 22,
          totalValue: 222200.00,
          stockLevel: 90
        },
        {
          value: 200,
          batches: 15,
          singles: 12,
          totalValue: 302400.00,
          stockLevel: 60
        }
      ]
    }
  ];

  DENOMINATION_LABELS: { [key: number]: string } = {
    10: 'R10',
    20: 'R20',
    50: 'R50',
    100: 'R100',
    200: 'R200'
  };

  // Coin inventory data loaded from service
  coinInventory: CoinInventory[] = [];

  // Grouped coin inventory by denomination (aggregated across all series)
  groupedCoinInventory: CoinInventory[] = [];

  // Coin configuration
  coinDenominations = Object.values(CoinDenomination);
  coinLabels = COIN_DENOMINATION_LABELS;
  coinBatchConfig = COIN_BATCH_CONFIG;
  coinBatchValues = COIN_BATCH_VALUES;

  // Mock user service for demo
  userService = {
    hasManagerPrivileges: () => true
  };

  constructor(
    private addCashModalService: AddCashModalService,
    private removeCashModalService: RemoveCashModalService,
    private addCoinModalService: AddCoinModalService,
    private removeCoinModalService: RemoveCoinModalService,
    private snackBar: MatSnackBar,
    private inventoryService: InventoryService,
    private auditReportModalService: AuditReportModalService,
    private noteSeriesManagementModalService: NoteSeriesManagementModalService,
    private cdr: ChangeDetectorRef
  ) {
    // Make inventory service accessible for debugging
    (window as any).inventoryService = this.inventoryService;
  }

  ngOnInit(): void {
    // Subscribe to inventory changes
    this.inventoryService.cashInventory$.subscribe(cashInventory => {
      console.log('Cash inventory updated via subscription:', cashInventory.length, 'items');
      if (this.currentInventoryMode === InventoryMode.NORMAL) {
        this.updateInventoryData(cashInventory);
      } else {
        // Update combined summary even if not in normal mode
        this.updateCombinedSummaryData();
      }
      this.cdr.detectChanges();
    });

    // Subscribe to dye-stained cash inventory changes
    this.inventoryService.dyeStainedCashInventory$.subscribe(dyeStainedInventory => {
      console.log('Dye-stained inventory updated via subscription:', dyeStainedInventory.length, 'items');
      if (this.currentInventoryMode === InventoryMode.DYE_STAINED) {
        this.updateDyeStainedInventoryData(dyeStainedInventory);
      } else {
        // Update combined summary even if not in dye-stained mode
        this.updateCombinedSummaryData();
      }
      this.cdr.detectChanges();
    });

    // Subscribe to coin inventory changes
    this.inventoryService.coinInventory$.subscribe(coinInventory => {
      console.log('Coin inventory updated via subscription:', coinInventory.length, 'items');
      if (this.currentInventoryMode === InventoryMode.COINS) {
        this.loadCoinsInventoryData();
      } else {
        // Update combined summary even if not in coins mode
        this.updateCombinedSummaryData();
      }
      this.cdr.detectChanges();
    });

    this.inventoryService.coinInventory$.subscribe(coinInventory => {
      console.log('Coin inventory updated via subscription:', coinInventory.length, 'items');
      this.coinInventory = coinInventory;
      this.groupedCoinInventory = this.groupCoinsByDenomination(coinInventory);
      console.log('Grouped coin inventory:', this.groupedCoinInventory.length, 'denominations');
      this.cdr.detectChanges();
    });

    // Subscribe to custom note series changes
    this.inventoryService.customNoteSeries$.subscribe(() => {
      console.log('Custom note series updated via subscription');
      this.updateNoteSeriesCount();
      // Refresh series data when custom series change based on current mode
      this.refreshSeriesDataForCurrentMode();
      this.cdr.detectChanges();
    });

    // Subscribe to inventory changes to update combined summary
    // The overview should always show combined totals regardless of current mode
    this.inventoryService.inventorySummary$.subscribe(_summary => {
      console.log('Inventory summary updated via subscription, updating combined summary');
      this.updateCombinedSummaryData();
      this.cdr.detectChanges();
    });
  }

  private loadInventoryData(): void {
    // This method is no longer needed as subscriptions handle data loading
    // Keeping for backward compatibility but subscriptions will override
    console.log('Initial data loading handled by subscriptions');
  }

  private updateInventoryData(cashInventory: CashInventory[]): void {
    console.log('Updating inventory data with', cashInventory.length, 'cash items');

    // Transform cash inventory data for the template
    this.inventoryData = this.transformCashInventoryData(cashInventory);
    this.seriesData = this.transformToSeriesData(cashInventory);

    console.log('Transformed to', this.seriesData.length, 'series with denominations');

    // Update combined summary data across all inventory types
    this.updateCombinedSummaryData();

    // Update note series count
    this.updateNoteSeriesCount();
  }

  private updateDyeStainedInventoryData(dyeStainedInventory: any[]): void {
    console.log('Updating dye-stained inventory data with', dyeStainedInventory.length, 'items');

    // Transform dye-stained inventory data for the template (reuse existing methods since structure is the same)
    this.inventoryData = this.transformCashInventoryData(dyeStainedInventory);
    this.seriesData = this.transformToSeriesData(dyeStainedInventory);

    console.log('Transformed to', this.seriesData.length, 'dye-stained series with denominations');

    // Update combined summary data across all inventory types
    this.updateCombinedSummaryData();

    // Update note series count
    this.updateNoteSeriesCount();
  }

  private refreshSeriesDataForCurrentMode(): void {
    if (this.isDyeStainedMode) {
      const dyeStainedInventory = this.inventoryService.getDyeStainedCashInventory();
      this.seriesData = this.transformToSeriesData(dyeStainedInventory as any);
    } else {
      const cashInventory = this.inventoryService.getCashInventory();
      this.seriesData = this.transformToSeriesData(cashInventory);
    }
  }

  /**
   * Refresh data based on current inventory mode
   */
  private refreshDataForCurrentMode(): void {
    switch (this.currentInventoryMode) {
      case InventoryMode.NORMAL:
        const cashInventory = this.inventoryService.getCashInventory();
        this.updateInventoryData(cashInventory);
        break;
      case InventoryMode.DYE_STAINED:
        const dyeStainedInventory = this.inventoryService.getDyeStainedCashInventory();
        this.updateDyeStainedInventoryData(dyeStainedInventory);
        break;
      case InventoryMode.COINS:
        this.loadCoinsInventoryData();
        break;
    }
  }

  /**
   * Load and display coins inventory data organized by series
   */
  private loadCoinsInventoryData(): void {
    const coinInventory = this.inventoryService.getCoinInventory();

    // Group coins by series
    const coinSeriesData = this.transformCoinInventoryToSeriesData(coinInventory);

    // Update series data with coin series
    this.seriesData = coinSeriesData;

    // Update combined summary data across all inventory types
    this.updateCombinedSummaryData();

    // Update note series count (for display purposes)
    this.updateNoteSeriesCount();
  }

  /**
   * Transform coin inventory data to series format for display
   */
  private transformCoinInventoryToSeriesData(coinInventory: CoinInventory[]): any[] {
    const seriesMap = new Map<CoinSeries, any>();

    // Initialize series data
    Object.values(CoinSeries).forEach(series => {
      seriesMap.set(series, {
        id: `coin-${series}`,
        name: `Coin ${COIN_SERIES_LABELS[series]}`,
        totalNotes: 0,
        totalValue: 0,
        totalBatches: 0,
        totalSingles: 0,
        denominations: []
      });
    });

    // Group coins by series and denomination
    coinInventory.forEach(coin => {
      const seriesData = seriesMap.get(coin.series);
      if (seriesData) {
        seriesData.totalNotes += coin.quantity;
        seriesData.totalValue += coin.value;
        seriesData.totalBatches += coin.batches;
        seriesData.totalSingles += (coin.quantity - (coin.batches * COIN_BATCH_CONFIG[coin.denomination]));

        seriesData.denominations.push({
          value: coin.denomination, // Use 'value' to match DenominationData interface
          denomination: coin.denomination,
          quantity: coin.quantity,
          batches: coin.batches,
          singles: coin.quantity - (coin.batches * COIN_BATCH_CONFIG[coin.denomination]),
          totalValue: coin.value,
          stockLevel: this.getCoinStockLevelPercentage(coin),
          label: COIN_DENOMINATION_LABELS[coin.denomination]
        });
      }
    });

    // Sort denominations within each series (highest to lowest)
    seriesMap.forEach(seriesData => {
      seriesData.denominations.sort((a: any, b: any) => b.denomination - a.denomination);
    });

    return Array.from(seriesMap.values());
  }

  /**
   * Update combined summary data across all inventory types (Notes + Dye-Stained + Coins)
   */
  private updateCombinedSummaryData(): void {
    // Get all inventory data
    const cashInventory = this.inventoryService.getCashInventory();
    const dyeStainedInventory = this.inventoryService.getDyeStainedCashInventory();
    const coinInventory = this.inventoryService.getCoinInventory();

    let totalValue = 0;
    let totalNotes = 0;
    const lowStockAlerts: any[] = [];
    const outOfStockAlerts: any[] = [];

    // Calculate separate totals for each category
    this.cashInventoryTotal = this.calculateCashInventoryTotal(cashInventory);
    this.dyeStainedInventoryTotal = this.calculateDyeStainedInventoryTotal(dyeStainedInventory);
    this.coinInventoryTotal = this.calculateCoinInventoryTotal(coinInventory);

    // Process normal cash inventory
    cashInventory.forEach(item => {
      // Exclude fake notes from total value calculations
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        totalValue += item.value;
      }
      totalNotes += item.quantity;

      // Check for stock alerts
      if (item.quantity === 0) {
        outOfStockAlerts.push({
          series: item.noteSeries,
          denomination: item.denomination,
          quantity: item.quantity,
          label: `R${item.denomination}`,
          type: 'cash'
        });
      } else {
        const lowStockThreshold = this.getLowStockThreshold(item.denomination);
        if (item.quantity > 0 && item.quantity < lowStockThreshold) {
          lowStockAlerts.push({
            series: item.noteSeries,
            denomination: item.denomination,
            quantity: item.quantity,
            label: `R${item.denomination}`,
            type: 'cash'
          });
        }
      }
    });

    // Process dye-stained cash inventory
    dyeStainedInventory.forEach(item => {
      // Dye-stained money has real monetary value
      totalValue += item.value;
      totalNotes += item.quantity;

      // Check for stock alerts
      if (item.quantity === 0) {
        outOfStockAlerts.push({
          series: item.noteSeries,
          denomination: item.denomination,
          quantity: item.quantity,
          label: `R${item.denomination} (Dye-Stained)`,
          type: 'dye-stained'
        });
      } else {
        const lowStockThreshold = this.getLowStockThreshold(item.denomination);
        if (item.quantity > 0 && item.quantity < lowStockThreshold) {
          lowStockAlerts.push({
            series: item.noteSeries,
            denomination: item.denomination,
            quantity: item.quantity,
            label: `R${item.denomination} (Dye-Stained)`,
            type: 'dye-stained'
          });
        }
      }
    });

    // Process coin inventory
    coinInventory.forEach(coin => {
      totalValue += coin.value;
      totalNotes += coin.quantity; // Using totalNotes for consistency with UI

      const stockLevel = this.getCoinStockLevel(coin);
      if (stockLevel === 'out-of-stock') {
        outOfStockAlerts.push({
          series: coin.series,
          denomination: coin.denomination,
          quantity: coin.quantity,
          label: COIN_DENOMINATION_LABELS[coin.denomination],
          type: 'coin'
        });
      } else if (stockLevel === 'low') {
        lowStockAlerts.push({
          series: coin.series,
          denomination: coin.denomination,
          quantity: coin.quantity,
          label: COIN_DENOMINATION_LABELS[coin.denomination],
          type: 'coin'
        });
      }
    });

    // Calculate the total value as sum of all category totals for consistency
    const combinedTotalValue = this.cashInventoryTotal + this.dyeStainedInventoryTotal + this.coinInventoryTotal;

    // Update the inventory summary with combined data
    this.inventorySummary = {
      totalValue: combinedTotalValue,
      totalNotes,
      lowStockAlerts,
      outOfStockAlerts
    };

    console.log('Combined inventory summary updated:', {
      totalValue: combinedTotalValue,
      originalTotalValue: totalValue,
      totalNotes,
      cashTotal: this.cashInventoryTotal,
      dyeStainedTotal: this.dyeStainedInventoryTotal,
      coinTotal: this.coinInventoryTotal,
      lowStockCount: lowStockAlerts.length,
      outOfStockCount: outOfStockAlerts.length
    });
  }

  /**
   * Calculate total value for cash inventory (excluding fake notes)
   */
  private calculateCashInventoryTotal(cashInventory: CashInventory[]): number {
    return cashInventory.reduce((total, item) => {
      // Exclude fake notes from total value calculations
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        return total + item.value;
      }
      return total;
    }, 0);
  }

  /**
   * Calculate total value for dye-stained inventory
   */
  private calculateDyeStainedInventoryTotal(dyeStainedInventory: any[]): number {
    return dyeStainedInventory.reduce((total, item) => {
      // Dye-stained money has real monetary value
      return total + item.value;
    }, 0);
  }

  /**
   * Calculate total value for coin inventory
   */
  private calculateCoinInventoryTotal(coinInventory: CoinInventory[]): number {
    return coinInventory.reduce((total, coin) => {
      return total + coin.value;
    }, 0);
  }

  /**
   * Groups coin inventory by denomination, aggregating quantities and values across all series
   */
  private groupCoinsByDenomination(coinInventory: CoinInventory[]): CoinInventory[] {
    const denominationMap = new Map<CoinDenomination, CoinInventory>();

    coinInventory.forEach(coin => {
      const existing = denominationMap.get(coin.denomination);

      if (existing) {
        // Aggregate quantities and values
        existing.quantity += coin.quantity;
        existing.batches += coin.batches;
        existing.value += coin.value;
        // Update timestamp to the most recent
        if (coin.lastUpdated > existing.lastUpdated) {
          existing.lastUpdated = coin.lastUpdated;
          existing.updatedBy = coin.updatedBy;
        }
      } else {
        // Create new aggregated entry
        denominationMap.set(coin.denomination, {
          id: `coin-aggregated-${coin.denomination}`,
          series: coin.series, // Use the first series found (for display purposes)
          denomination: coin.denomination,
          quantity: coin.quantity,
          batches: coin.batches,
          value: coin.value,
          lastUpdated: coin.lastUpdated,
          updatedBy: coin.updatedBy
        });
      }
    });

    // Convert map to array and sort by denomination value (descending)
    return Array.from(denominationMap.values()).sort((a, b) => b.denomination - a.denomination);
  }

  private transformCashInventoryData(cashInventory: CashInventory[]): any[] {
    // Group by series and create the structure expected by the template
    const seriesGroups: { [key: string]: any } = {};
    const allSeries = this.inventoryService.getAllNoteSeries();

    cashInventory.forEach(item => {
      // Skip fake notes in dye-stained mode since they cannot be dye-stained
      if (this.isDyeStainedMode && item.noteSeries === NoteSeries.FAKE_NOTES) {
        return;
      }

      // For custom series, extract the series ID from the item ID
      let seriesKey: string = item.noteSeries;
      let seriesName = NOTE_SERIES_LABELS[item.noteSeries];

      // Check if this is a custom series
      if (!seriesName) {
        // First check if noteSeries itself is a custom series ID
        const customSeries = allSeries.find(s => s.id === item.noteSeries);
        if (customSeries) {
          seriesName = customSeries.name;
        } else if (item.id.startsWith('cash-custom_')) {
          // Fallback: extract from item ID format: cash-{seriesId}-{denomination}
          const parts = item.id.split('-');
          if (parts.length >= 3) {
            seriesKey = parts[1]; // Extract custom series ID
            const customSeriesById = allSeries.find(s => s.id === seriesKey);
            seriesName = customSeriesById ? customSeriesById.name : seriesKey;
          }
        }
      }

      if (!seriesGroups[seriesKey]) {
        const baseName = seriesName || seriesKey;
        const displayName = this.isDyeStainedMode && seriesKey !== NoteSeries.FAKE_NOTES
          ? `Dye Stained ${baseName}`
          : baseName;

        seriesGroups[seriesKey] = {
          id: seriesKey,
          name: displayName,
          denominations: {}
        };
      }

      seriesGroups[seriesKey].denominations[item.denomination] = {
        value: item.denomination,
        quantity: item.quantity,
        totalValue: item.value
      };
    });

    return Object.values(seriesGroups);
  }

  private transformToSeriesData(cashInventory: CashInventory[]): any[] {
    // Group by series and create detailed series data
    const seriesGroups: { [key: string]: any } = {};
    const allSeries = this.inventoryService.getAllNoteSeries();

    // Initialize all predefined series first to ensure they appear even with zero inventory
    // In dye-stained mode, exclude fake notes since they cannot be dye-stained
    Object.values(NoteSeries).forEach(series => {
      // Skip fake notes in dye-stained mode
      if (this.isDyeStainedMode && series === NoteSeries.FAKE_NOTES) {
        return;
      }

      const baseName = NOTE_SERIES_LABELS[series];
      const displayName = this.isDyeStainedMode && series !== NoteSeries.FAKE_NOTES
        ? `Dye Stained ${baseName}`
        : baseName;

      seriesGroups[series] = {
        id: series,
        name: displayName,
        totalBatches: 0,
        totalSingles: 0,
        totalValue: 0,
        totalFakeValue: 0,
        denominations: new Map() // Use Map to track denominations by value
      };
    });

    // Initialize custom series
    allSeries.filter(s => !s.isPredefined).forEach(customSeries => {
      const displayName = this.isDyeStainedMode
        ? `Dye Stained ${customSeries.name}`
        : customSeries.name;

      seriesGroups[customSeries.id] = {
        id: customSeries.id,
        name: displayName,
        totalBatches: 0,
        totalSingles: 0,
        totalValue: 0,
        denominations: new Map() // Use Map to track denominations by value
      };
    });

    // Process cash inventory items
    cashInventory.forEach(item => {
      // Skip fake notes in dye-stained mode since they cannot be dye-stained
      if (this.isDyeStainedMode && item.noteSeries === NoteSeries.FAKE_NOTES) {
        return;
      }

      // For custom series, extract the series ID from the item ID
      let seriesKey: string = item.noteSeries;
      let seriesName = NOTE_SERIES_LABELS[item.noteSeries];

      // Check if this is a custom series
      if (!seriesName) {
        // First check if noteSeries itself is a custom series ID
        const customSeries = allSeries.find(s => s.id === item.noteSeries);
        if (customSeries) {
          seriesName = customSeries.name;
        } else if (item.id.startsWith('cash-custom_')) {
          // Fallback: extract from item ID format: cash-{seriesId}-{denomination}
          const parts = item.id.split('-');
          if (parts.length >= 3) {
            seriesKey = parts[1]; // Extract custom series ID
            const customSeriesById = allSeries.find(s => s.id === seriesKey);
            seriesName = customSeriesById ? customSeriesById.name : seriesKey;
          }
        }
      }

      // Ensure the series group exists (should already exist from initialization above)
      if (!seriesGroups[seriesKey]) {
        const baseName = seriesName || seriesKey;
        const displayName = this.isDyeStainedMode && seriesKey !== NoteSeries.FAKE_NOTES
          ? `Dye Stained ${baseName}`
          : baseName;

        seriesGroups[seriesKey] = {
          id: seriesKey,
          name: displayName,
          totalBatches: 0,
          totalSingles: 0,
          totalValue: 0,
          totalFakeValue: 0,
          denominations: new Map()
        };
      }

      const batches = Math.floor(item.quantity / 100);
      const singles = item.quantity % 100;
      const stockLevel = this.calculateStockLevel(item.quantity, item.denomination);

      seriesGroups[seriesKey].totalBatches += batches;
      seriesGroups[seriesKey].totalSingles += singles;
      // Exclude fake notes from total value calculations
      if (item.noteSeries !== NoteSeries.FAKE_NOTES) {
        seriesGroups[seriesKey].totalValue += item.value;
      }

      // For fake notes, calculate face value for display purposes
      const faceValue = item.quantity * item.denomination;
      if (item.noteSeries === NoteSeries.FAKE_NOTES) {
        seriesGroups[seriesKey].totalFakeValue = (seriesGroups[seriesKey].totalFakeValue || 0) + faceValue;
      }

      // Store denomination data in the Map
      seriesGroups[seriesKey].denominations.set(item.denomination, {
        value: item.denomination,
        batches,
        singles,
        totalValue: item.noteSeries === NoteSeries.FAKE_NOTES ? 0 : item.value,
        fakeValue: item.noteSeries === NoteSeries.FAKE_NOTES ? faceValue : 0,
        stockLevel
      });
    });

    // Convert Maps to arrays and ensure all denominations are included
    const allDenominations = Object.values(NoteDenomination).filter(d => typeof d === 'number') as NoteDenomination[];

    Object.keys(seriesGroups).forEach(seriesKey => {
      const series = seriesGroups[seriesKey];

      // Add missing denominations with zero values
      allDenominations.forEach(denomination => {
        if (!series.denominations.has(denomination)) {
          series.denominations.set(denomination, {
            value: denomination,
            batches: 0,
            singles: 0,
            totalValue: 0,
            fakeValue: 0,
            stockLevel: 0
          });
        }
      });

      // Convert Map to array and sort by denomination value
      series.denominations = Array.from(series.denominations.values())
        .sort((a: any, b: any) => a.value - b.value);
    });

    const result = Object.values(seriesGroups);
    console.log('Transformed series data:', result.length, 'series');
    return result;
  }

  /**
   * Update the note series count for the summary card
   * Excludes fake notes since they are not real money series
   */
  private updateNoteSeriesCount(): void {
    const allSeries = this.inventoryService.getAllNoteSeries();
    // Exclude fake notes from the count since they're not real money series
    this.noteSeriesCount = allSeries.filter(series => series.id !== NoteSeries.FAKE_NOTES).length;
  }

  /**
   * Check if a series is fake notes
   */
  isFakeNotesSeries(seriesId: string): boolean {
    return seriesId === NoteSeries.FAKE_NOTES;
  }

  private calculateStockLevel(quantity: number, denomination: NoteDenomination): number {
    // Calculate stock level as percentage based on some maximum expected quantity
    const maxQuantities: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 2000,
      [NoteDenomination.R20]: 1500,
      [NoteDenomination.R50]: 1200,
      [NoteDenomination.R100]: 800,
      [NoteDenomination.R200]: 400
    };

    const maxQuantity = maxQuantities[denomination];
    return Math.min(Math.round((quantity / maxQuantity) * 100), 100);
  }

  // Modal event handlers - these methods are no longer needed as we use individual modal services
  // onModalClose and onBackdropClick methods removed as they're not used with the new modal services

  // Tab change handler
  onTabChange(event: any): void {
    console.log('Tab changed to:', event.index);
  }

  // Track by function for denomination cards
  trackByDenomination(_index: number, denomination: any): any {
    return denomination.value;
  }

  // Get status class based on stock level
  getStatusClass(stockLevel: number): string {
    if (stockLevel === 0) return 'status-out-of-stock';
    if (stockLevel >= 80) return 'status-good';
    if (stockLevel >= 50) return 'status-medium';
    return 'status-low';
  }

  // Get status text based on stock level
  getStatusText(stockLevel: number): string {
    if (stockLevel === 0) return 'Out of Stock';
    if (stockLevel >= 80) return 'In Stock';
    if (stockLevel >= 50) return 'Medium';
    return 'Low Stock';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-ZA').format(num);
  }

  getDenominationLabel(denomination: number): string {
    return this.DENOMINATION_LABELS[denomination] || `R${denomination}`;
  }

  formatQuantityDisplay(quantity: number): string {
    const batches = Math.floor(quantity / 100);
    const singles = quantity % 100;

    if (batches === 0) {
      return `${singles} single${singles !== 1 ? 's' : ''}`;
    } else if (singles === 0) {
      return `${batches} batch${batches !== 1 ? 'es' : ''}`;
    } else {
      return `${batches} batch${batches !== 1 ? 'es' : ''} + ${singles} single${singles !== 1 ? 's' : ''}`;
    }
  }



  getStockStatus(item: any): { status: string; class: string } {
    // Out of stock - quantity is exactly 0
    if (item.quantity === 0) {
      return { status: 'Out of Stock', class: 'out-of-stock' };
    }

    // Low stock - quantity is below threshold but greater than 0
    const lowStockThreshold = this.getLowStockThreshold(item.denomination);
    if (item.quantity > 0 && item.quantity < lowStockThreshold) {
      return { status: 'Low Stock', class: 'low-stock' };
    }

    // High stock - quantity is above high threshold
    const highStockThreshold = this.getHighStockThreshold(item.denomination);
    if (item.quantity > highStockThreshold) {
      return { status: 'High Stock', class: 'high-stock' };
    }

    // Normal stock - quantity is within normal range
    return { status: 'In Stock', class: 'in-stock' };
  }

  getStockStatusIcon(item: any): string {
    const status = this.getStockStatus(item);
    switch (status.class) {
      case 'out-of-stock': return 'error';
      case 'low-stock': return 'warning';
      case 'high-stock': return 'trending_up';
      case 'in-stock': return 'check_circle';
      default: return 'check_circle';
    }
  }

  /**
   * Get low stock threshold for a denomination
   */
  private getLowStockThreshold(denomination: NoteDenomination): number {
    const thresholds: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 100,   // 1 batch
      [NoteDenomination.R20]: 100,   // 1 batch
      [NoteDenomination.R50]: 100,   // 1 batch
      [NoteDenomination.R100]: 100,  // 1 batch
      [NoteDenomination.R200]: 100   // 1 batch
    };
    return thresholds[denomination];
  }

  /**
   * Get high stock threshold for a denomination
   */
  private getHighStockThreshold(denomination: NoteDenomination): number {
    const thresholds: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 1000,   // 10 batches
      [NoteDenomination.R20]: 800,    // 8 batches
      [NoteDenomination.R50]: 600,    // 6 batches
      [NoteDenomination.R100]: 500,   // 5 batches
      [NoteDenomination.R200]: 400    // 4 batches
    };
    return thresholds[denomination];
  }

  getStockPercentage(item: any): number {
    const maxStock = this.getHighStockThreshold(item.denomination);
    return Math.min((item.quantity / maxStock) * 100, 100);
  }

  getStockProgressColor(item: any): string {
    const status = this.getStockStatus(item);
    switch (status.class) {
      case 'out-of-stock': return 'warn';
      case 'low-stock': return 'warn';
      case 'high-stock': return 'accent';
      case 'in-stock': return 'primary';
      default: return 'primary';
    }
  }

  getSeriesStyleClass(series: string): string {
    return `series-${series.toLowerCase()}`;
  }

  getDenominationImage(denomination: number): string {
    return `assets/images/Money/R${denomination}.jpg`;
  }

  onImageError(event: any): void {
    event.target.style.display = 'none';
  }

  onAddCash(seriesId?: string, denominationValue?: number): void {
    // If called without parameters (from FAB), open modal with no pre-selection
    if (!seriesId || !denominationValue) {
      const modalData: AddCashDialogData = {
        isDyeStainedMode: this.isDyeStainedMode
      };

      this.addCashModalService.openAddCashModal(modalData)
        .subscribe((result: AddCashResult | undefined) => {
          if (result) {
            // For FAB calls, we don't have specific series/denomination context
            this.handleAddCashResult(result, '', 0);
          }
          // If result is undefined, user cancelled - no action needed
        });
      return;
    }

    // Get current quantity for this denomination
    const currentQuantity = this.getCurrentQuantity(seriesId, denominationValue);

    // Configure modal data
    const modalData: AddCashDialogData = {
      seriesName: seriesId,
      denomination: denominationValue,
      currentQuantity: currentQuantity,
      isDyeStainedMode: this.isDyeStainedMode
    };

    // Open the modal
    this.addCashModalService.openAddCashModal(modalData)
      .subscribe((result: AddCashResult | undefined) => {
        if (result) {
          this.handleAddCashResult(result, seriesId, denominationValue);
        }
        // If result is undefined, user cancelled - no action needed
      });
  }

  private getCurrentQuantity(seriesId: string, denominationValue: number): number {
    const denomination = denominationValue as NoteDenomination;

    // Convert seriesId to NoteSeries enum for predefined series
    const seriesMap: { [key: string]: NoteSeries } = {
      'Mandela Series': NoteSeries.MANDELA,
      'Big 5 Series': NoteSeries.BIG_5,
      'Commemorative Series': NoteSeries.COMMEMORATIVE,
      'V6 Series': NoteSeries.V6,
      'Fake Notes': NoteSeries.FAKE_NOTES,
      // Dye-stained variants
      'Dye Stained Mandela Series': NoteSeries.MANDELA,
      'Dye Stained Big 5 Series': NoteSeries.BIG_5,
      'Dye Stained Commemorative Series': NoteSeries.COMMEMORATIVE,
      'Dye Stained V6 Series': NoteSeries.V6
    };

    const series = seriesMap[seriesId];

    if (series && denomination) {
      // Handle predefined series - check appropriate inventory based on dye-stained mode
      if (this.isDyeStainedMode) {
        return this.inventoryService.getCurrentDyeStainedCashQuantity(series, denomination);
      } else {
        return this.inventoryService.getCurrentCashQuantity(series, denomination);
      }
    } else if (denomination) {
      // Handle custom series - find by seriesId directly in appropriate inventory
      if (this.isDyeStainedMode) {
        const dyeStainedInventory = this.inventoryService.getDyeStainedCashInventory();
        const item = dyeStainedInventory.find(inv =>
          inv.noteSeries === seriesId && inv.denomination === denomination
        );
        return item ? item.quantity : 0;
      } else {
        const cashInventory = this.inventoryService.getCashInventory();
        const item = cashInventory.find(inv =>
          inv.noteSeries === seriesId && inv.denomination === denomination
        );
        return item ? item.quantity : 0;
      }
    }

    return 0;
  }

  private handleAddCashResult(result: AddCashResult, seriesId: string, denominationValue: number): void {
    if (!result.success) {
      this.snackBar.open('Failed to add cash to inventory', 'Close', { duration: 3000 });
      return;
    }

    const quantity = result.added || 0;
    const totalValue = quantity * denominationValue;

    // The add cash modal has already successfully added the cash to the inventory service
    // We just need to show a success message and refresh the data

    // Show success message
    if (denominationValue > 0) {
      this.snackBar.open(
        `Added ${quantity} x R${denominationValue} notes (Total: R${totalValue.toLocaleString()})`,
        'Close',
        {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'top',
          panelClass: ['success-snackbar']
        }
      );
    } else {
      // For FAB calls without specific denomination context
      this.snackBar.open(
        `Successfully added ${quantity} notes to inventory`,
        'Close',
        {
          duration: 5000,
          horizontalPosition: 'center',
          verticalPosition: 'top',
          panelClass: ['success-snackbar']
        }
      );
    }

    // Log the transaction for debugging
    console.log('Cash transaction completed:', {
      result,
      seriesId,
      denominationValue,
      timestamp: new Date().toISOString()
    });

    // No need to manually refresh - the subscription should handle the update automatically
    console.log('Add cash transaction completed, waiting for subscription update...');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private refreshInventoryData(): void {
    console.log('Refreshing inventory data...');
    // Reload inventory data from service
    this.loadInventoryData();
    // Force change detection to update the UI
    this.cdr.detectChanges();
    console.log('Inventory data refreshed and change detection triggered');
  }

  onRemoveCash(seriesId?: string, denominationValue?: number, currentQuantity?: number): void {
    // If called without parameters, open modal with no pre-selection
    if (!seriesId || !denominationValue) {
      const modalData: RemoveCashDialogData = {
        isDyeStainedMode: this.isDyeStainedMode
      };

      this.removeCashModalService.openRemoveCashModal(modalData)
        .subscribe((result: RemoveCashResult | undefined) => {
          if (result) {
            this.handleRemoveCashResult(result, '', 0);
          }
          // If result is undefined, user cancelled - no action needed
        });
      return;
    }

    // Configure modal data
    const modalData: RemoveCashDialogData = {
      seriesName: seriesId,
      denomination: denominationValue,
      currentQuantity: currentQuantity || 0,
      isDyeStainedMode: this.isDyeStainedMode
    };

    this.removeCashModalService.openRemoveCashModal(modalData)
      .subscribe((result: RemoveCashResult | undefined) => {
        if (result) {
          this.handleRemoveCashResult(result, seriesId, denominationValue);
        }
        // If result is undefined, user cancelled - no action needed
      });
  }

  private handleRemoveCashResult(result: RemoveCashResult, seriesId: string, denominationValue: number): void {
    if (!result.success) {
      this.snackBar.open('Failed to remove cash from inventory', 'Close', { duration: 3000 });
      return;
    }

    const quantity = result.removed || 0;
    const totalValue = quantity * denominationValue;

    // The remove cash modal has already successfully removed the cash from the inventory service
    // We just need to show a success message and refresh the data

    // Show success message
    this.snackBar.open(
      `Removed ${quantity} x R${denominationValue} notes (Total: R${totalValue.toLocaleString()})`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Cash removal completed:', {
      result,
      seriesId,
      denominationValue,
      timestamp: new Date().toISOString()
    });

    // No need to manually refresh - the subscription should handle the update automatically
    console.log('Remove cash transaction completed, waiting for subscription update...');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private handleGenericAddCashResult(result: AddCashResult): void {
    if (!result.success) {
      this.snackBar.open('Failed to add cash to inventory', 'Close', { duration: 3000 });
      return;
    }

    const quantity = result.added || 0;

    // Show generic success message
    this.snackBar.open(
      `Successfully added ${quantity} notes to inventory`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Generic cash transaction completed:', {
      result,
      timestamp: new Date().toISOString()
    });

    // No need to manually refresh - the subscription should handle the update automatically
    console.log('Generic add cash transaction completed, waiting for subscription update...');
  }

  // Methods will be redesigned from scratch

  trackByItemId(index: number, item: any): string {
    return item.id || index.toString();
  }

  viewDetails(item: any): void {
    // Navigate to detailed view or open modal
    console.log('View details for item:', item);
    this.snackBar.open(
      `Viewing details for ${this.getDenominationLabel(item.denomination)}`,
      'Close',
      { duration: 2000 }
    );
  }

  /**
   * Event handler for adding coins (called from detailed-inventory component)
   */
  onAddCoin(denomination: number): void {
    this.openAddCoinModal(denomination as CoinDenomination);
  }

  /**
   * Event handler for removing coins (called from detailed-inventory component)
   */
  onRemoveCoin(denomination: number): void {
    this.openRemoveCoinModal(denomination as CoinDenomination);
  }

  /**
   * Event handler for adding coins with series information (called from detailed-inventory component in coins mode)
   */
  onAddCoinWithSeries(denomination: number, seriesId?: string): void {
    if (seriesId) {
      // Extract series from seriesId (format: "coin-mandela" -> "mandela")
      const series = seriesId.replace('coin-', '') as CoinSeries;
      this.openAddCoinModalWithSeries(denomination as CoinDenomination, series);
    } else {
      // Fallback to regular add coin modal
      this.openAddCoinModal(denomination as CoinDenomination);
    }
  }

  /**
   * Event handler for removing coins with series information (called from detailed-inventory component in coins mode)
   */
  onRemoveCoinWithSeries(denomination: number, seriesId?: string): void {
    if (seriesId) {
      // Extract series from seriesId (format: "coin-mandela" -> "mandela")
      const series = seriesId.replace('coin-', '') as CoinSeries;
      this.openRemoveCoinModalWithSeries(denomination as CoinDenomination, series);
    } else {
      // Fallback to regular remove coin modal
      this.openRemoveCoinModal(denomination as CoinDenomination);
    }
  }

  /**
   * Opens the Add Coin modal for a specific denomination
   */
  openAddCoinModal(denomination: CoinDenomination): void {
    const coinData = this.groupedCoinInventory.find(coin => coin.denomination === denomination);
    if (!coinData) {
      this.snackBar.open('Coin data not found', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    const dialogData: AddCoinDialogData = {
      denomination: denomination,
      currentQuantity: coinData.quantity
    };

    this.addCoinModalService.openAddCoinModal(dialogData).subscribe({
      next: (result: AddCoinResult | undefined) => {
        if (result) {
          this.handleCoinModalResult(result);
        }
        // If result is undefined, user cancelled - no action needed
      },
      error: (error: any) => {
        console.error('Error opening add coin modal:', error);
        this.snackBar.open('Failed to open coin modal', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Opens the Add Coin modal with pre-selected series and denomination
   */
  openAddCoinModalWithSeries(denomination: CoinDenomination, series: CoinSeries): void {
    // Find the specific coin inventory item for this series and denomination
    const coinItem = this.coinInventory.find(coin =>
      coin.series === series && coin.denomination === denomination
    );

    const dialogData: AddCoinDialogData = {
      series: series,
      denomination: denomination,
      currentQuantity: coinItem?.quantity || 0
    };

    this.addCoinModalService.openAddCoinModal(dialogData).subscribe({
      next: (result: AddCoinResult | undefined) => {
        if (result) {
          this.handleCoinModalResult(result);
        }
        // If result is undefined, user cancelled - no action needed
      },
      error: (error: any) => {
        console.error('Error opening add coin modal:', error);
        this.snackBar.open('Failed to open coin modal', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Handles the result from the Add Coin modal
   */
  private handleCoinModalResult(result: AddCoinResult): void {
    if (result.success && result.added) {
      this.snackBar.open(
        `Successfully added ${result.added} coins to inventory`,
        'Close',
        { duration: 4000, panelClass: ['success-snackbar'] }
      );

      // In a real application, you would refresh the inventory data here
      // For now, we'll just show the success message
    } else {
      this.snackBar.open('Failed to add coins to inventory', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  /**
   * Opens the Remove Coin modal for a specific denomination
   */
  openRemoveCoinModal(denomination: CoinDenomination): void {
    const coinData = this.groupedCoinInventory.find(coin => coin.denomination === denomination);
    if (!coinData) {
      this.snackBar.open('Coin data not found', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // For aggregated coin data, we don't specify a series (it will be handled across all series)
    const dialogData: RemoveCoinDialogData = {
      seriesName: 'aggregated', // Special value to indicate aggregated removal
      denomination: +denomination,
      currentQuantity: coinData.quantity
    };

    this.removeCoinModalService.openRemoveCoinModal(dialogData).subscribe({
      next: (result: RemoveCoinResult | undefined) => {
        if (result) {
          this.handleRemoveCoinModalResult(result, denomination);
        }
        // If result is undefined, user cancelled - no action needed
      },
      error: (error: any) => {
        console.error('Error opening remove coin modal:', error);
        this.snackBar.open('Failed to open remove coin modal', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Opens the Remove Coin modal with pre-selected series and denomination
   */
  openRemoveCoinModalWithSeries(denomination: CoinDenomination, series: CoinSeries): void {
    // Find the specific coin inventory item for this series and denomination
    const coinItem = this.coinInventory.find(coin =>
      coin.series === series && coin.denomination === denomination
    );

    const dialogData: RemoveCoinDialogData = {
      seriesName: series,
      denomination: +denomination,
      currentQuantity: coinItem?.quantity || 0
    };

    this.removeCoinModalService.openRemoveCoinModal(dialogData).subscribe({
      next: (result: RemoveCoinResult | undefined) => {
        if (result) {
          this.handleRemoveCoinModalResult(result, denomination);
        }
        // If result is undefined, user cancelled - no action needed
      },
      error: (error: any) => {
        console.error('Error opening remove coin modal:', error);
        this.snackBar.open('Failed to open remove coin modal', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Handles the result from the Remove Coin modal
   */
  private handleRemoveCoinModalResult(result: RemoveCoinResult, denomination: CoinDenomination): void {
    if (result.success && result.removed) {
      const coinLabel = this.getCoinLabel(denomination);
      const totalValue = result.removed * +denomination;

      this.snackBar.open(
        `Successfully removed ${result.removed} x ${coinLabel} coins (Total: ${this.formatCoinValue(totalValue)})`,
        'Close',
        {
          duration: 5000,
          panelClass: ['success-snackbar'],
          horizontalPosition: 'center',
          verticalPosition: 'top'
        }
      );

      // Log the transaction for debugging
      console.log('Coin removal completed:', {
        result,
        denomination,
        coinLabel,
        totalValue,
        timestamp: new Date().toISOString()
      });

      // In a real application, you would refresh the inventory data here
      // For now, we'll just show the success message
    } else {
      this.snackBar.open('Failed to remove coins from inventory', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  /**
   * Gets the coin label for display
   */
  getCoinLabel(denomination: CoinDenomination): string {
    return this.coinLabels[denomination];
  }

  /**
   * Formats coin value for display
   */
  formatCoinValue(value: number): string {
    if (value >= 1) {
      return `R${value.toFixed(0)}`;
    } else {
      return `${Math.round(value * 100)}c`;
    }
  }

  /**
   * Gets the total coin inventory value
   */
  getTotalCoinValue(): number {
    return this.groupedCoinInventory.reduce((total, coin) => total + coin.value, 0);
  }

  /**
   * Gets the total number of coins
   */
  getTotalCoins(): number {
    return this.groupedCoinInventory.reduce((total, coin) => total + coin.quantity, 0);
  }

  /**
   * Gets coins with low stock
   */
  getLowStockCoins(): CoinInventory[] {
    return this.groupedCoinInventory.filter(coin => this.getCoinStockLevel(coin) !== 'normal');
  }

  /**
   * Gets the stock level for a coin
   */
  getCoinStockLevel(coin: CoinInventory): 'normal' | 'low' | 'out-of-stock' {
    if (coin.quantity === 0) {
      return 'out-of-stock';
    } else if (coin.quantity < 500) {
      return 'low';
    } else {
      return 'normal';
    }
  }

  /**
   * Get coin stock level as percentage for display consistency
   */
  getCoinStockLevelPercentage(coin: CoinInventory): number {
    // Use a reasonable maximum stock level for coins (e.g., 2000 for percentage calculation)
    const maxStock = 2000;
    return Math.min((coin.quantity / maxStock) * 100, 100);
  }

  /**
   * Open the audit report modal
   */
  onExportAuditReport(): void {
    this.auditReportModalService.openAuditReportModal()
      .subscribe((result) => {
        // Handle modal close if needed
        if (result) {
          console.log('Audit report modal closed with result:', result);
        }
      });
  }

  onManageNoteSeries(): void {
    this.noteSeriesManagementModalService.openNoteSeriesManagementModal()
      .subscribe((result: any) => {
        if (result) {
          console.log('Note series management modal closed with result:', result);

          // If user wants to view details of a specific series, we could navigate or show details
          if (result.action === 'view-details' && result.series) {
            const seriesName = result.seriesName || result.series;
            this.snackBar.open(
              `Viewing details for ${seriesName}`,
              'Close',
              { duration: 3000 }
            );
          }

          // Refresh inventory data to reflect any changes
          this.loadInventoryData();
        } else {
          // Even if no result, refresh data in case changes were made
          this.loadInventoryData();
        }
      });
  }

  // ===== DYE STAINED MODE METHODS =====

  /**
   * Handle dye stained mode toggle change
   * @param isDyeStained - The new toggle state
   */
  onDyeStainedModeChange(isDyeStained: boolean): void {
    // Update the current inventory mode based on the boolean
    this.currentInventoryMode = isDyeStained ? InventoryMode.DYE_STAINED : InventoryMode.NORMAL;

    // Refresh data based on the new mode (series names will be updated automatically in transformToSeriesData)
    this.refreshDataForCurrentMode();
  }

  /**
   * Toggle inventory mode (for FAB button) - cycles through 3 modes
   */
  onToggleInventoryMode(): void {
    switch (this.currentInventoryMode) {
      case InventoryMode.NORMAL:
        this.currentInventoryMode = InventoryMode.DYE_STAINED;
        break;
      case InventoryMode.DYE_STAINED:
        this.currentInventoryMode = InventoryMode.COINS;
        break;
      case InventoryMode.COINS:
        this.currentInventoryMode = InventoryMode.NORMAL;
        break;
    }

    // Update data based on new mode
    this.refreshDataForCurrentMode();
  }

  /**
   * Legacy method for backward compatibility
   */
  onToggleDyeStainedMode(): void {
    this.onToggleInventoryMode();
  }

  /**
   * Get the appropriate icon for the current inventory mode
   */
  getInventoryModeIcon(): string {
    switch (this.currentInventoryMode) {
      case InventoryMode.NORMAL:
        return 'palette';
      case InventoryMode.DYE_STAINED:
        return 'palette_off';
      case InventoryMode.COINS:
        return 'monetization_on';
      default:
        return 'palette';
    }
  }

  /**
   * Get the appropriate tooltip for the current inventory mode
   */
  getInventoryModeTooltip(): string {
    switch (this.currentInventoryMode) {
      case InventoryMode.NORMAL:
        return 'Switch to Dye-Stained Mode';
      case InventoryMode.DYE_STAINED:
        return 'Switch to Coins Mode';
      case InventoryMode.COINS:
        return 'Switch to Normal Mode';
      default:
        return 'Switch Inventory Mode';
    }
  }

  /**
   * Get the appropriate aria label for the current inventory mode
   */
  getInventoryModeAriaLabel(): string {
    switch (this.currentInventoryMode) {
      case InventoryMode.NORMAL:
        return 'Switch to dye-stained inventory mode';
      case InventoryMode.DYE_STAINED:
        return 'Switch to coins inventory mode';
      case InventoryMode.COINS:
        return 'Switch to normal inventory mode';
      default:
        return 'Switch inventory mode';
    }
  }

  /**
   * Get the appropriate title for the current inventory mode
   */
  getInventoryTitle(): string {
    switch (this.currentInventoryMode) {
      case InventoryMode.NORMAL:
        return 'Detailed Inventory';
      case InventoryMode.DYE_STAINED:
        return 'Dye-Stained Inventory';
      case InventoryMode.COINS:
        return 'Coins Inventory';
      default:
        return 'Detailed Inventory';
    }
  }

  /**
   * Update series data to reflect dye stained mode
   * This method transforms the display names of note series (excluding Fake Notes and Coins)
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private updateSeriesDataWithDyeStainedMode(): void {
    this.seriesData = this.seriesData.map(series => {
      // Skip transformation for Fake Notes series and Coins
      if (this.fakeNotesSeriesIds.includes(series.id) || series.name === 'Coins') {
        return series;
      }

      // Transform note series names based on dye stained mode
      const originalName = this.getOriginalSeriesName(series.name);
      const newName = this.isDyeStainedMode
        ? `Dye Stained ${originalName}`
        : originalName;

      return {
        ...series,
        name: newName
      };
    });
  }

  /**
   * Get the original series name by removing "Dye Stained " prefix if present
   * @param currentName - The current series name
   * @returns The original series name without prefix
   */
  private getOriginalSeriesName(currentName: string): string {
    const dyeStainedPrefix = 'Dye Stained ';
    return currentName.startsWith(dyeStainedPrefix)
      ? currentName.substring(dyeStainedPrefix.length)
      : currentName;
  }

  /**
   * Check if a series should be affected by dye stained mode
   * @param seriesId - The series ID to check
   * @returns True if the series should be transformed
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private shouldTransformSeries(seriesId: string): boolean {
    // Don't transform Fake Notes or Coins
    return !this.fakeNotesSeriesIds.includes(seriesId) && seriesId !== 'coins';
  }
}
