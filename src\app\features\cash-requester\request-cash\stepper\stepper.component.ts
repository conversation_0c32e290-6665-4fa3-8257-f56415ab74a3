import { CdkStepper, CdkStepperModule } from '@angular/cdk/stepper';
import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-stepper',
  standalone: true,
  imports: [
    CdkStepperModule, 
    CommonModule,
    MatButtonModule
  ],
  templateUrl: './stepper.component.html',
  styleUrl: './stepper.component.scss',
  providers: [{ provide: CdkStepper, useExisting: StepperComponent }],
})
export class StepperComponent extends CdkStepper {
  @Input() linearModeSelected = true;
  formTitles: string[] = [
    'New Cash Request',
    'Bank Note Selection',
    'Submit Cash Request'
  ]

  onClick(index: number) {
    if (index >= 0 && this.formTitles.length && index < this.steps.length) {
      this.selectedIndex = index;
    }
  }

  getProgressPercent(): number {
    const total = this.steps?.length || 1;
    return ((this.selectedIndex + 1) / total) * 100;
  }
}
