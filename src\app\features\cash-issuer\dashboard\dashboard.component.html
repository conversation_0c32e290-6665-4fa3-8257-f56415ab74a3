<div class="cash-issuer-dashboard-container">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <h1>Cash Issuer Dashboard</h1>
  </div>

  <!-- Main Dashboard Content -->
  <div class="dashboard-content">
    <!-- Unified Welcome and Quick Actions Container -->
    <div class="unified-content-card">
      <!-- Welcome Section -->
      <app-landingboard></app-landingboard>

      <!-- Quick Actions Section -->
      <div class="quick-actions-section">
        <div class="section-header">
          <div class="section-title">
            <mat-icon>flash_on</mat-icon>
            <h2>Quick Actions</h2>
          </div>
        </div>

        <div class="actions-grid">
          <!-- Manage Requests Card -->
          <mat-card class="action-card primary" (click)="onManageRequests()">
            <div class="card-background"></div>
            <mat-card-content>
              <div class="card-icon">
                <mat-icon>assignment</mat-icon>
              </div>
              <div class="card-content">
                <h3>Manage Requests</h3>
                <p>Review, approve, and process cash requests</p>
              </div>
              <div class="card-arrow">
                <mat-icon>arrow_forward</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Inventory Management Card -->
          <mat-card class="action-card secondary" (click)="onInventoryManagement()">
            <div class="card-background"></div>
            <mat-card-content>
              <div class="card-icon">
                <mat-icon>inventory</mat-icon>
              </div>
              <div class="card-content">
                <h3>Inventory Management</h3>
                <p>Monitor and manage cash inventory levels</p>
              </div>
              <div class="card-arrow">
                <mat-icon>arrow_forward</mat-icon>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>

    <!-- Request Stats Section -->
    <app-request-stats></app-request-stats>
  </div>
</div>
