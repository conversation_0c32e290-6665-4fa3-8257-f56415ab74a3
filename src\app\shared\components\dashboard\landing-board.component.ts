import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../features/login/auth.service';
import { User } from '../../models/user.model';


@Component({
  selector: 'app-landingboard',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  templateUrl: './landing-board.component.html',
  styleUrls: ['./landing-board.component.scss'],
})
export class LandingboardComponent {
  userData?: User;
  role: string = '';

  constructor(private userService: UserService, public authService: AuthService) {}

  ngOnInit(): void {

    this.authService.currentUser$.subscribe((user) => {
      if (user) {
        this.userData = user;
        this.role = user.role.toLowerCase();
      }
      console.log('Component initialized, fetching user data');
    });

  }

  onAddNewRequest(): void {
    // Handle add new request logic here
    console.log('Add new request clicked');
  }
}
