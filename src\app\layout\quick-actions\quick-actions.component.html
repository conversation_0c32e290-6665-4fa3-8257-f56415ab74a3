<div class="container">
    <div class="header">
        <span>Quick Actions</span>
    </div>
    <hr color="white"/> <!-- When i try to target it via css, ya gana (will look at it)-->
    <div class="quick-actions">

        <a> <!-- [routerLink]="['/system-logs']", for when I want to make the card clickable-->
            <mat-card appearance="outlined">
                <mat-card-actions>
                    <mat-icon>remove_red_eye</mat-icon>
                </mat-card-actions>
                <mat-card-header>
                    <mat-card-title>View System Logs</mat-card-title>
                </mat-card-header>
            </mat-card>
        </a>
        <br>
        <a>
            <mat-card appearance="outlined">
                <mat-card-actions>
                    <mat-icon>assignment</mat-icon>
                </mat-card-actions>
                <mat-card-header>
                    <mat-card-title>Manage Inventory</mat-card-title>
                </mat-card-header>
            </mat-card>

        </a>
        <br>
        <a>
            <mat-card appearance="outlined">
                <mat-card-actions>
                    <mat-icon> insert_chart</mat-icon>
                </mat-card-actions>
                <mat-card-header>
                    <mat-card-title>Generate Audit Report</mat-card-title>
                </mat-card-header>
            </mat-card>

        </a>




    </div>

</div>