import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AuthService } from '../../../login/auth.service';
import { User } from '../../../../shared/models/user.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-request-cash-step-3',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './request-cash-step-3.component.html',
  styleUrl: './request-cash-step-3.component.scss',
})
export class RequestCashStep3Component implements OnInit, OnD<PERSON>roy {
  user!: User;
  role: string = '';
  additionalDetailsForm!: FormGroup;
  sub!: Subscription;

  constructor(private fb: FormBuilder, private authService: AuthService) {
    this.additionalDetailsForm = this.fb.group({
      issuedTo: ['', Validators.required],
      issuerName: ['', Validators.required],
      requesterComments: [''],
    });
  }
  
  ngOnInit(): void {
    this.sub = this.authService.currentUser$.subscribe((user) => {
      if (user) {
        this.user = user;
        this.role = user.role.toLowerCase();
      }
    })
  }

  ngOnDestroy(): void {
    this.sub.unsubscribe();
  }

}
