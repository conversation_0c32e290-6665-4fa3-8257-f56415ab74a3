import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';

@Component({
  selector: 'app-request-cash-step-3',
  imports: [CommonModule, FormsModule, ReactiveFormsModule, MatSelectModule],
  templateUrl: './request-cash-step-3.component.html',
  styleUrl: './request-cash-step-3.component.scss'
})
export class RequestCashStep3Component {
  fb = inject(FormBuilder)

  additionalDetailsForm = this.fb.group({
    issuedTo: ['', Validators.required],
    issuerName: ['', Validators.required],
    requesterComments: ['']
  })
}
