import { Component } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { RequestService } from '../../../core/services/request.service';

@Component({
  selector: 'app-request-stats',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule],
  templateUrl: './request-stats.component.html',
  styleUrls: ['./request-stats.component.scss']
})
export class RequestStatsComponent {

  stats: { name: string; value: number }[] = [];

  constructor(private requestService: RequestService) {}

  ngOnInit(): void {
    this.loadRequestStats();
  }

  loadRequestStats(): void {
    this.requestService.getAllRequests().subscribe((requests) => {
      const counts: { [key: string]: number } = {};

      for (let request of requests) {
        let status = request.requestStatus;
        counts[status] = (counts[status] || 0) + 1;
      }

      this.stats = [
        { name: 'Issued', value: counts['Issued'] || 0 },
        { name: 'Completed', value: counts['Completed'] || 0 },
        { name: 'Rejected', value: counts['Rejected'] || 0 },
        { name: 'Pending', value: counts['Pending'] || 0 },
        { name: 'Approved', value: counts['Approved'] || 0 },
        {name: 'Total Requests', value: requests.length || 0}
      ];
    });
  }

  getCardClass(statName: string): string {
    switch (statName) {
      case 'Issued': return 'issued-card';
      case 'Completed': return 'completed-card';
      case 'Rejected': return 'rejected-card';
      case 'Pending': return 'pending-card';
      case 'Approved': return 'approved-card';
      case 'Total Requests': return 'total-requests-card';
      default: return '';
    }
  }

  getIconForStat(statName: string): string {
    switch (statName) {
      case 'Issued': return 'send';
      case 'Completed': return 'check_circle';
      case 'Rejected': return 'cancel';
      case 'Pending': return 'schedule';
      case 'Approved': return 'verified';
      case 'Total Requests': return 'assessment';
      default: return 'info';
    }
  }

  getTrendIcon(statName: string): string {
    switch (statName) {
      case 'Issued': return 'trending_up';
      case 'Completed': return 'done';
      case 'Rejected': return 'block';
      case 'Pending': return 'hourglass_empty';
      case 'Approved': return 'thumb_up';
      case 'Total Requests': return 'analytics';
      default: return 'info';
    }
  }

  getTrendLabel(statName: string): string {
    switch (statName) {
      case 'Issued': return 'Active';
      case 'Completed': return 'Done';
      case 'Rejected': return 'Blocked';
      case 'Pending': return 'Waiting';
      case 'Approved': return 'Ready';
      case 'Total Requests': return 'Overview';
      default: return 'Status';
    }
  }

  getTrendClass(statName: string): string {
    switch (statName) {
      case 'Completed': return 'success';
      case 'Rejected': return 'critical';
      case 'Pending': return 'warning';
      case 'Approved': return 'success';
      default: return '';
    }
  }

  getDescription(statName: string): string {
    switch (statName) {
      case 'Issued': return 'Requests sent out';
      case 'Completed': return 'Successfully finished';
      case 'Rejected': return 'Declined requests';
      case 'Pending': return 'Awaiting action';
      case 'Approved': return 'Ready to process';
      case 'Total Requests': return 'All requests combined';
      default: return '';
    }
  }
}
