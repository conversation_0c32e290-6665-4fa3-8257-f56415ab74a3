import { Component } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { CommonModule } from '@angular/common';
import { RequestService } from '../../../core/services/request.service';

@Component({
  selector: 'app-request-stats',
  standalone: true,
  imports: [CommonModule, MatCardModule],
  templateUrl: './request-stats.component.html',
  styleUrls: ['./request-stats.component.scss']
})
export class RequestStatsComponent {

  stats: { name: string; value: number }[] = [];

  constructor(private requestService: RequestService) {}

  ngOnInit(): void {
    this.loadRequestStats();
  }

  loadRequestStats(): void {
    this.requestService.getAllRequests().subscribe((requests) => {
      const counts: { [key: string]: number } = {};

      for (let request of requests) {
        let status = request.requestStatus;
        counts[status] = (counts[status] || 0) + 1;
      }

      this.stats = [
        { name: 'Issued', value: counts['Issued'] || 0 },
        { name: 'Completed', value: counts['Completed'] || 0 },
        { name: 'Rejected', value: counts['Rejected'] || 0 },
        { name: 'Pending', value: counts['Pending'] || 0 },
        { name: 'Approved', value: counts['Approved'] || 0 },
        {name: 'Total Requests', value: requests.length || 0}
      ];
    });
  }
}
