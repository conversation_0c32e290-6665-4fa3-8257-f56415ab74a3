import { Component, Input, OnInit } from '@angular/core';
import { RequestCashService } from './request-cash.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RequestCashStep1Component } from './request-cash-step-1/request-cash-step-1.component';
import { RequestCashStep2Component } from './request-cash-step-2/request-cash-step-2.component';
import { RequestCashStep3Component } from './request-cash-step-3/request-cash-step-3.component';
import { MatStepperModule } from '@angular/material/stepper';
import { CdkStepper, CdkStepperModule } from '@angular/cdk/stepper';
import { StepperComponent } from './stepper/stepper.component';

@Component({
  selector: 'app-request-cash',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule, 
    ReactiveFormsModule,
    RequestCashStep1Component,
    RequestCashStep2Component,
    RequestCashStep3Component,
    MatStepperModule,
    StepperComponent,
    CdkStepperModule
  ],
  templateUrl: './request-cash.component.html',
  styleUrls: ['./request-cash.component.scss']
})
export class RequestCashComponent implements OnInit {
  ngOnInit(): void {
    // Initialize component
  }
}
