<!-- Detailed Inventory Section -->
<section class="detailed-inventory-section">
  <!-- Section Header -->
  <div class="section-header">
    <div class="header-left">
      <div class="header-content">
        <h2 class="section-title">{{ title || 'Detailed Inventory' }}</h2>

        <!-- Normal Mode Indicator -->
        <div class="mode-indicator normal-mode-indicator" *ngIf="!isDyeStainedMode && !isCoinsMode">
          <mat-icon class="indicator-icon">inventory</mat-icon>
          <span class="indicator-text">Normal Mode Active</span>
        </div>

        <!-- Dye-Stained Mode Indicator -->
        <div class="mode-indicator dye-stained-indicator" *ngIf="isDyeStainedMode">
          <mat-icon class="indicator-icon">palette</mat-icon>
          <span class="indicator-text">Dye Stained Mode Active</span>
        </div>

        <!-- Coins Mode Indicator -->
        <div class="mode-indicator coins-mode-indicator" *ngIf="isCoinsMode">
          <mat-icon class="indicator-icon">monetization_on</mat-icon>
          <span class="indicator-text">Coins Mode Active</span>
        </div>
      </div>
    </div>

    <div class="header-center">
      <!-- Empty center space for better layout balance -->
    </div>

    <div class="header-right">
      <button
        mat-raised-button
        class="export-audit-btn"
        (click)="onExportAuditReport()"
        *ngIf="showExportButton"
      >
        <mat-icon>file_download</mat-icon>
        Export Audit Report
      </button>
    </div>
  </div>

  <!-- Series Tabs -->
  <div class="series-tabs-container">
    <mat-tab-group
      class="series-tabs"
      animationDuration="300ms"
      [disablePagination]="true"
      (selectedTabChange)="onTabChange($event)"
    >
      <mat-tab
        *ngFor="let series of seriesData; let i = index"
        [label]="series.name"
      >
        <ng-template mat-tab-label>
          <span class="tab-label">{{ series.name }}</span>
        </ng-template>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Series Summary Totals -->
          <div class="series-totals-summary"
               [class.fake-notes-summary]="isFakeNotesSeries(series.id)"
               [class.normal-mode-summary]="!isDyeStainedMode && !isCoinsMode && !isFakeNotesSeries(series.id)"
               [class.dye-stained-summary]="isDyeStainedMode && !isFakeNotesSeries(series.id)"
               [class.coins-mode-summary]="isCoinsMode && !isFakeNotesSeries(series.id)">
            <div class="total-card total-notes">
              <div class="total-icon">
                <mat-icon>inventory_2</mat-icon>
              </div>
              <div class="total-content">
                <div class="total-value">
                  {{ series.totalBatches }} batches + {{ series.totalSingles }} singles
                </div>
                <div class="total-label">Total Notes</div>
              </div>
            </div>

            <div class="total-card total-value">
              <div class="total-icon">
                <mat-icon>account_balance_wallet</mat-icon>
              </div>
              <div class="total-content">
                <div class="total-value">
                  {{
                    isFakeNotesSeries(series.id)
                      ? formatCurrency(series.totalFakeValue || 0)
                      : formatCurrency(series.totalValue)
                  }}
                </div>
                <div class="total-label">
                  {{
                    isFakeNotesSeries(series.id)
                      ? "Total Fake Notes Value"
                      : "Total Value"
                  }}
                </div>
              </div>
            </div>
          </div>

          <!-- Simple Table Layout -->
          <div class="inventory-table-container">
            <table class="inventory-table">
              <thead>
                <tr>
                  <th>Denomination</th>
                  <th>Quantity</th>
                  <th>Value</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>

                <tr
                  *ngFor="
                    let denomination of series.denominations;
                    trackBy: trackByDenomination;
                    let i = index
                  "
                  [class.low-stock-row]="
                    denomination.stockLevel < 50 && denomination.stockLevel > 0
                  "
                  [class.out-of-stock-row]="denomination.stockLevel === 0"
                  [class.dye-stained-row]="isDyeStainedMode && !isFakeNotesSeries(series.id) && series.id !== 'coins'"
                >
                  <!-- Denomination Column -->
                  <td class="denomination-cell" data-label="Denomination">
                    <div class="denomination-info">
                      <span class="denomination-value">R{{ denomination.value }}</span>
                      <span
                        *ngIf="isDyeStainedMode && !isFakeNotesSeries(series.id) && series.id !== 'coins'"
                        class="dye-indicator"
                        [class]="'dye-' + getDyeColor(denomination.value)"
                      >
                        <mat-icon>colorize</mat-icon>
                        STAINED
                      </span>
                    </div>
                  </td>

                  <!-- Quantity Column -->
                  <td class="quantity-cell" data-label="Quantity">
                    {{ denomination.batches }} batches + {{ denomination.singles }} singles
                  </td>

                  <!-- Value Column -->
                  <td class="value-cell" data-label="Value">
                    {{
                      isFakeNotesSeries(series.id)
                        ? formatCurrency(denomination.fakeValue || 0)
                        : formatCurrency(denomination.totalValue)
                    }}
                  </td>

                  <!-- Status Column -->
                  <td class="status-cell" data-label="Status">
                    <span
                      class="status-badge"
                      [class]="getStatusClass(denomination.stockLevel)"
                    >
                      {{ getStatusText(denomination.stockLevel) }}
                    </span>
                  </td>

                  <!-- Actions Column -->
                  <td class="actions-cell" data-label="Actions">
                    <div class="action-buttons">
                      <button
                        mat-icon-button
                        color="primary"
                        class="add-btn"
                        (click)="isCoinsMode ? onAddCoin(denomination.value, series.id) : onAddCash(series.id, denomination.value)"
                        [matTooltip]="isCoinsMode ? 'Add Coins' : 'Add Cash'"
                      >
                        <mat-icon>add_circle</mat-icon>
                      </button>
                      <button
                        mat-icon-button
                        color="warn"
                        class="remove-btn"
                        (click)="
                          isCoinsMode
                            ? onRemoveCoin(denomination.value, series.id)
                            : onRemoveCash(
                                series.id,
                                denomination.value,
                                denomination.batches * 100 + denomination.singles
                              )
                        "
                        [matTooltip]="isCoinsMode ? 'Remove Coins' : 'Remove Cash'"
                      >
                        <mat-icon>delete</mat-icon>
                      </button>
                      <button
                        mat-icon-button
                        class="edit-btn"
                        matTooltip="Edit"
                      >
                        <mat-icon>edit</mat-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </mat-tab>

      <!-- Coin Inventory Tab -->
      <mat-tab label="Coins" *ngIf="showCoinTab">
        <ng-template mat-tab-label>
          <span class="tab-label">
            Coins
          </span>
        </ng-template>

        <!-- Coin Tab Content -->
        <div class="tab-content">
          <!-- Coin Summary Totals -->
          <div class="series-totals-summary coins-mode-summary">
            <div class="total-card total-notes">
              <div class="total-icon">
                <mat-icon>monetization_on</mat-icon>
              </div>
              <div class="total-content">
                <div class="total-value">
                  {{ formatNumber(getTotalCoins()) }}
                </div>
                <div class="total-label">Total Coins</div>
              </div>
            </div>

            <div class="total-card total-value">
              <div class="total-icon">
                <mat-icon>account_balance_wallet</mat-icon>
              </div>
              <div class="total-content">
                <div class="total-value">
                  {{ formatCurrency(getTotalCoinValue()) }}
                </div>
                <div class="total-label">Total Coin Value</div>
              </div>
            </div>
          </div>

          <!-- Simple Coin Table Layout -->
          <div class="inventory-table-container">
            <table class="inventory-table">
              <thead>
                <tr>
                  <th>Denomination</th>
                  <th>Quantity</th>
                  <th>Value</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>

                <tr
                  *ngFor="
                    let coin of coinInventory;
                    trackBy: trackByItemId
                  "
                  [class.low-stock-row]="getCoinStockLevel(coin) === 'low'"
                  [class.out-of-stock-row]="getCoinStockLevel(coin) === 'out-of-stock'"
                >
                  <!-- Denomination Column -->
                  <td class="denomination-cell" data-label="Denomination">
                    <div class="denomination-info">
                      <span class="denomination-value">{{ getCoinLabel(coin.denomination) }}</span>
                    </div>
                  </td>

                  <!-- Quantity Column -->
                  <td class="quantity-cell" data-label="Quantity">
                    {{ coin.batches }} batches + {{ formatNumber(coin.quantity) }} coins
                  </td>

                  <!-- Value Column -->
                  <td class="value-cell" data-label="Value">
                    {{ formatCurrency(coin.value) }}
                  </td>

                  <!-- Status Column -->
                  <td class="status-cell" data-label="Status">
                    <span
                      class="status-badge"
                      [class]="'status-' + getCoinStockLevel(coin)"
                    >
                      {{
                        getCoinStockLevel(coin) === "normal"
                          ? "IN STOCK"
                          : getCoinStockLevel(coin) === "low"
                          ? "LOW STOCK"
                          : "OUT OF STOCK"
                      }}
                    </span>
                  </td>

                  <!-- Actions Column -->
                  <td class="actions-cell" data-label="Actions">
                    <div class="action-buttons">
                      <button
                        mat-icon-button
                        color="primary"
                        class="add-btn"
                        (click)="onAddCoin(coin.denomination)"
                        matTooltip="Add Coins"
                      >
                        <mat-icon>add_circle</mat-icon>
                      </button>
                      <button
                        mat-icon-button
                        color="warn"
                        class="remove-btn"
                        (click)="onRemoveCoin(coin.denomination)"
                        matTooltip="Remove Coins"
                      >
                        <mat-icon>delete</mat-icon>
                      </button>
                      <button
                        mat-icon-button
                        class="edit-btn"
                        matTooltip="Edit"
                      >
                        <mat-icon>edit</mat-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</section>
