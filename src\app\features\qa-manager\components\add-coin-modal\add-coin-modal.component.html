<div class="add-coin-modal-container">
  <!-- Modern Header with Gradient -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>monetization_on</mat-icon>
      </div>
      <div class="header-text">
        <h2>Add Coin Inventory</h2>
        <p>Enhance your coin reserves with precision</p>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Main Content Area -->
  <div class="modal-content">
    <form #addCoinForm="ngForm" class="add-coin-form">

      <!-- Pre-selected Information Display (when series and denomination are pre-selected) -->
      <div class="form-section pre-selected-info" *ngIf="data?.series && data?.denomination">
        <div class="section-header">
          <mat-icon class="step-icon">info</mat-icon>
          <h3>Selected Item</h3>
        </div>
        <div class="pre-selected-display">
          <div class="selected-item-card">
            <div class="item-icon">
              <mat-icon>{{ getSeriesIcon(selectedSeries!) }}</mat-icon>
            </div>
            <div class="item-info">
              <h4>{{ COIN_SERIES_LABELS[selectedSeries!] }}</h4>
              <p class="denomination-info">{{ COIN_DENOMINATION_LABELS[selectedDenomination!] }}</p>
              <p class="series-description">{{ getSeriesDescription(selectedSeries!) }}</p>
            </div>
            <div class="confirmation-badge">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 1: Series Selection (only show when not pre-selected) -->
      <div class="form-section series-selection" *ngIf="!data?.series">
        <div class="section-header">
          <mat-icon class="step-icon">category</mat-icon>
          <h3>Select Coin Series</h3>
        </div>
        <div class="series-grid">
          <div *ngFor="let series of availableSeries"
               class="series-card"
               [class.selected]="selectedSeries === series"
               (click)="selectedSeries = series; onSeriesChange()">
            <div class="series-icon">
              <mat-icon>{{ getSeriesIcon(series) }}</mat-icon>
            </div>
            <div class="series-info">
              <h4>{{ COIN_SERIES_LABELS[series] }}</h4>
              <p>{{ getSeriesDescription(series) }}</p>
            </div>
            <div class="selection-indicator" *ngIf="selectedSeries === series">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Denomination Selection (only show when not pre-selected) -->
      <div class="form-section denomination-section" *ngIf="selectedSeries && !data?.denomination">
        <div class="section-header">
          <mat-icon class="step-icon">payments</mat-icon>
          <h3>Choose Denomination</h3>
        </div>
        <div class="denomination-grid">
          <div class="denomination-card"
               *ngFor="let denomination of availableDenominations"
               [class.selected]="selectedDenomination === denomination"
               (click)="selectedDenomination = denomination; onDenominationChange()">
            <div class="denomination-icon">
              <mat-icon>{{ +denomination >= 1 ? 'account_balance_wallet' : 'monetization_on' }}</mat-icon>
            </div>
            <div class="denomination-info">
              <h4>{{ COIN_DENOMINATION_LABELS[denomination] }}</h4>
              <p>{{ +denomination >= 1 ? 'High value coin' : 'Standard coin' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Quantity Input -->
      <div class="form-section quantity-section" *ngIf="selectedDenomination">
        <div class="section-header">
          <mat-icon class="step-icon">calculate</mat-icon>
          <h3>Specify Quantity</h3>
        </div>
        <div class="quantity-controls">
          <div class="quantity-card batches-card">
            <div class="card-header">
              <mat-icon>inventory_2</mat-icon>
              <h4>Batches</h4>
              <span class="helper-text">{{ selectedDenomination ? COIN_BATCH_CONFIG[selectedDenomination] : 0 }} coins each</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(-1)" [disabled]="batches <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="batches"
                     name="batches"
                     min="0"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(1)">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="batch-value">Value per batch: {{ selectedDenomination ? formatCurrency(COIN_BATCH_VALUES[selectedDenomination]) : formatCurrency(0) }}</span>
            </div>
          </div>

          <div class="quantity-card singles-card">
            <div class="card-header">
              <mat-icon>looks_one</mat-icon>
              <h4>Singles</h4>
              <span class="helper-text">Individual coins</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(-1)" [disabled]="singles <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="singles"
                     name="singles"
                     min="0"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(1)">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="coin-value">Value per coin: {{ formatCurrency(selectedDenomination) }}</span>
            </div>
          </div>
        </div>

        <!-- Enhanced Summary Display -->
        <div class="summary-display" *ngIf="totalQuantity > 0">
          <div class="summary-card">
            <!-- Header Section -->
            <div class="summary-header">
              <div class="header-content">
                <div class="header-icon">
                  <mat-icon>add</mat-icon>
                </div>
                <div class="header-text">
                  <h3>Adding to Inventory</h3>
                  <p class="series-info">{{ getSelectedSeriesLabel() }} • {{ getSelectedDenominationLabel() }}</p>
                </div>
              </div>
              <div class="status-badge">
                <mat-icon>check_circle</mat-icon>
                <span>Ready</span>
              </div>
            </div>

            <!-- Main Metrics -->
            <div class="metrics-grid">
              <div class="metric-card quantity-card">
                <div class="metric-header">
                  <div class="metric-icon">
                    <mat-icon>monetization_on</mat-icon>
                  </div>
                  <span class="metric-label">Total Coins</span>
                </div>
                <div class="metric-value">{{ totalQuantity }}</div>
                <div class="metric-subtitle">coins</div>
              </div>

              <div class="metric-card value-card">
                <div class="metric-header">
                  <div class="metric-icon">
                    <mat-icon>account_balance_wallet</mat-icon>
                  </div>
                  <span class="metric-label">Total Value</span>
                </div>
                <div class="metric-value">{{ formatCurrency(totalValue) }}</div>
                <div class="metric-subtitle">ZAR</div>
              </div>
            </div>

            <!-- Breakdown Section -->
            <div class="breakdown-section" *ngIf="batches > 0 || singles > 0">
              <div class="breakdown-header">
                <mat-icon>analytics</mat-icon>
                <span>Breakdown</span>
              </div>
              <div class="breakdown-grid">
                <div class="breakdown-item" *ngIf="batches > 0">
                  <div class="breakdown-icon">
                    <mat-icon>inventory_2</mat-icon>
                  </div>
                  <div class="breakdown-content">
                    <div class="breakdown-title">{{ batches }} Batch{{ batches !== 1 ? 'es' : '' }}</div>
                    <div class="breakdown-subtitle">{{ batches * (selectedDenomination ? COIN_BATCH_CONFIG[selectedDenomination] : 0) }} coins</div>
                  </div>
                  <div class="breakdown-value">{{ formatCurrency(selectedDenomination * batches * (selectedDenomination ? COIN_BATCH_CONFIG[selectedDenomination] : 0)) }}</div>
                </div>
                <div class="breakdown-item" *ngIf="singles > 0">
                  <div class="breakdown-icon">
                    <mat-icon>monetization_on</mat-icon>
                  </div>
                  <div class="breakdown-content">
                    <div class="breakdown-title">{{ singles }} Single{{ singles !== 1 ? 's' : '' }}</div>
                    <div class="breakdown-subtitle">{{ singles }} coins</div>
                  </div>
                  <div class="breakdown-value">{{ formatCurrency(selectedDenomination * singles) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Reason (Optional) -->
      <div class="form-section reason-section" *ngIf="selectedDenomination">
        <div class="section-header">
          <mat-icon class="step-icon">description</mat-icon>
          <h3>Reason (Optional)</h3>
        </div>
        <mat-form-field class="reason-field" appearance="outline">
          <mat-label>Reason for addition</mat-label>
          <input matInput
                 [(ngModel)]="reason"
                 name="reason"
                 placeholder="e.g., New coin delivery, Inventory replenishment"
                 maxlength="200">
          <mat-hint>Optional: Provide a reason for this addition</mat-hint>
        </mat-form-field>
      </div>
    </form>
  </div>

  <!-- Action Buttons -->
  <div class="modal-actions">
    <button mat-stroked-button class="cancel-btn" (click)="onCancel()">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button
            class="add-btn"
            [disabled]="!isFormValid()"
            (click)="onAddCoins()">
      <mat-icon>add</mat-icon>
      Add to Inventory
    </button>
  </div>
</div>
