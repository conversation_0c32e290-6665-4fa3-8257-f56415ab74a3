<div [formGroup]="additionalDetailsForm" class="form-group">
  <mat-form-field appearance="outline">
    <mat-label>Issued To</mat-label>
    <input matInput/>
  </mat-form-field>
  <mat-form-field appearance="outline">
    <mat-label>Issuer Name</mat-label>
    <mat-select *ngIf="role == 'cash-requester' || 'qa-manager'">
      <mat-option value="one"><PERSON><PERSON><PERSON><PERSON></mat-option>
      <mat-option value="two"><PERSON><PERSON> Finn</mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field appearance="outline">
    <mat-label>Requester comments</mat-label>
    <textarea matInput></textarea>
  </mat-form-field>
</div>
