import { TestBed } from '@angular/core/testing';
import { InventoryService } from './inventory.service';
import { LocalStorageService } from '../../../shared/services/local-storage.service';
import { NoteSeries, NoteDenomination, CoinSeries, CoinDenomination } from '../../../shared/models/inventory.model';

describe('InventoryService', () => {
  let service: InventoryService;
  let localStorageService: jasmine.SpyObj<LocalStorageService>;

  beforeEach(() => {
    const localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['getItem', 'setItem']);

    TestBed.configureTestingModule({
      providers: [
        InventoryService,
        { provide: LocalStorageService, useValue: localStorageSpy }
      ]
    });
    
    service = TestBed.inject(InventoryService);
    localStorageService = TestBed.inject(LocalStorageService) as jasmine.SpyObj<LocalStorageService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize with default inventory data', () => {
    localStorageService.getItem.and.returnValue(null);
    
    const cashInventory = service.getCashInventory();
    const coinInventory = service.getCoinInventory();
    
    expect(cashInventory.length).toBeGreaterThan(0);
    expect(coinInventory.length).toBeGreaterThan(0);
  });

  it('should add cash to inventory correctly', () => {
    const series = NoteSeries.MANDELA;
    const denomination = NoteDenomination.R100;
    const quantity = 50;
    const reason = 'Test addition';

    const result = service.addCash(series, denomination, quantity, reason);
    
    expect(result).toBe(true);
    
    const currentQuantity = service.getCurrentCashQuantity(series, denomination);
    expect(currentQuantity).toBeGreaterThanOrEqual(quantity);
  });

  it('should remove cash from inventory correctly', () => {
    const series = NoteSeries.MANDELA;
    const denomination = NoteDenomination.R100;
    const addQuantity = 100;
    const removeQuantity = 30;
    const reason = 'Test removal';

    // First add some cash
    service.addCash(series, denomination, addQuantity, 'Test setup');
    const initialQuantity = service.getCurrentCashQuantity(series, denomination);
    
    // Then remove some
    const result = service.removeCash(series, denomination, removeQuantity, reason);
    
    expect(result).toBe(true);
    
    const finalQuantity = service.getCurrentCashQuantity(series, denomination);
    expect(finalQuantity).toBe(initialQuantity - removeQuantity);
  });

  it('should not remove more cash than available', () => {
    const series = NoteSeries.MANDELA;
    const denomination = NoteDenomination.R200;
    const currentQuantity = service.getCurrentCashQuantity(series, denomination);
    const excessiveQuantity = currentQuantity + 100;
    
    const result = service.removeCash(series, denomination, excessiveQuantity, 'Test excessive removal');
    
    expect(result).toBe(false);
  });

  it('should add coins to inventory correctly', () => {
    const series = CoinSeries.MANDELA;
    const denomination = CoinDenomination.R5;
    const quantity = 40; // 2 batches
    const reason = 'Test coin addition';

    const result = service.addCoins(series, denomination, quantity, reason);
    
    expect(result).toBe(true);
    
    const currentQuantity = service.getCurrentCoinQuantity(series, denomination);
    expect(currentQuantity).toBeGreaterThanOrEqual(quantity);
  });

  it('should remove coins from inventory correctly', () => {
    const series = CoinSeries.MANDELA;
    const denomination = CoinDenomination.R5;
    const addQuantity = 60;
    const removeQuantity = 20;
    const reason = 'Test coin removal';

    // First add some coins
    service.addCoins(series, denomination, addQuantity, 'Test setup');
    const initialQuantity = service.getCurrentCoinQuantity(series, denomination);
    
    // Then remove some
    const result = service.removeCoins(series, denomination, removeQuantity, reason);
    
    expect(result).toBe(true);
    
    const finalQuantity = service.getCurrentCoinQuantity(series, denomination);
    expect(finalQuantity).toBe(initialQuantity - removeQuantity);
  });

  it('should calculate inventory summary correctly', () => {
    const summary = service.getInventorySummary();
    
    expect(summary).toBeDefined();
    expect(summary.totalValue).toBeGreaterThanOrEqual(0);
    expect(summary.totalNotes).toBeGreaterThanOrEqual(0);
    expect(Array.isArray(summary.lowStockAlerts)).toBe(true);
  });

  it('should track transactions', () => {
    const series = NoteSeries.MANDELA;
    const denomination = NoteDenomination.R50;
    const quantity = 25;
    
    const initialTransactionCount = service.getTransactions().length;
    
    service.addCash(series, denomination, quantity, 'Test transaction tracking');
    
    const finalTransactionCount = service.getTransactions().length;
    expect(finalTransactionCount).toBe(initialTransactionCount + 1);
    
    const recentTransactions = service.getRecentTransactions(1);
    expect(recentTransactions.length).toBe(1);
    expect(recentTransactions[0].quantityChange).toBe(quantity);
  });

  it('should export and import inventory data', () => {
    // Add some test data
    service.addCash(NoteSeries.MANDELA, NoteDenomination.R100, 50, 'Test export');
    service.addCoins(CoinSeries.MANDELA, CoinDenomination.R2, 30, 'Test export');

    // Export data
    const exportedData = service.exportInventoryData();

    expect(exportedData).toBeDefined();
    expect(exportedData.cashInventory).toBeDefined();
    expect(exportedData.coinInventory).toBeDefined();
    expect(exportedData.transactions).toBeDefined();
    expect(exportedData.exportDate).toBeDefined();

    // Reset inventory
    service.resetInventory();

    // Import data back
    const importResult = service.importInventoryData(exportedData);
    expect(importResult).toBe(true);
  });

  it('should exclude fake notes from stock alerts', () => {
    // Reset inventory to start fresh
    service.resetInventoryToZero();

    // Add some real currency with low stock (should trigger alert)
    service.addCash(NoteSeries.MANDELA, NoteDenomination.R100, 10, 'Low stock test');

    // Add fake notes with zero stock (should NOT trigger alert)
    service.removeCash(NoteSeries.FAKE_NOTES, NoteDenomination.R50, 500, 'Remove all fake notes');

    const summary = service.getInventorySummary();

    // Should have low stock alerts for real currency but not fake notes
    const fakeNotesAlerts = summary.lowStockAlerts.filter(alert => alert.series === NoteSeries.FAKE_NOTES);
    const fakeNotesOutOfStockAlerts = summary.outOfStockAlerts.filter(alert => alert.series === NoteSeries.FAKE_NOTES);

    expect(fakeNotesAlerts.length).toBe(0);
    expect(fakeNotesOutOfStockAlerts.length).toBe(0);

    // Should still have alerts for real currency
    const realCurrencyAlerts = summary.lowStockAlerts.filter(alert => alert.series !== NoteSeries.FAKE_NOTES);
    expect(realCurrencyAlerts.length).toBeGreaterThan(0);
  });
});
