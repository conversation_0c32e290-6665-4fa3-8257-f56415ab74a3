<div class="absa-dashboard-section" aria-label="Request Statistics">
  <div class="dashboard-grid">
    <article
      class="absa-metric-card"
      [ngClass]="getCardClass(stat.name)"
      *ngFor="let stat of stats"
      tabindex="0"
      role="button"
      [attr.aria-label]="stat.name + ': ' + stat.value"
    >
      <div class="card-content">
        <header class="card-header">
          <div class="metric-icon">
            <mat-icon>{{ getIconForStat(stat.name) }}</mat-icon>
          </div>
          <div class="metric-trend" [ngClass]="getTrendClass(stat.name)">
            <mat-icon>{{ getTrendIcon(stat.name) }}</mat-icon>
            <span>{{ getTrendLabel(stat.name) }}</span>
          </div>
        </header>
        <div class="card-body">
          <div class="metric-value">{{ stat.value }}</div>
          <div class="metric-label">{{ stat.name }}</div>
          <div class="metric-description">{{ getDescription(stat.name) }}</div>
        </div>
      </div>
    </article>
  </div>
</div>
