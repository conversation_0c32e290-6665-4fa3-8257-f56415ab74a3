import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { RemoveCashModalComponent, RemoveCashData } from './remove-cash-modal.component';
import { NoteSeries, NoteDenomination } from '../../../../shared/models/inventory.model';

export interface RemoveCashResult {
  success: boolean;
  removed?: number;
}

export interface RemoveCashDialogData {
  seriesName?: string;
  denomination?: number;
  currentQuantity?: number;
  isDyeStainedMode?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class RemoveCashModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Remove Cash modal with optional pre-configuration
   * @param data Optional data to pre-configure the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openRemoveCashModal(data?: RemoveCashDialogData): Observable<RemoveCashResult | undefined> {
    // Convert the dialog data to the modal data format
    const modalData: RemoveCashData = {};

    if (data?.seriesName) {
      // Map series name to enum - handle both display names and enum values
      const seriesMap: { [key: string]: NoteSeries } = {
        'mandela': NoteSeries.MANDELA,
        'mandela series': NoteSeries.MANDELA,
        'big_5': NoteSeries.BIG_5,
        'big5': NoteSeries.BIG_5, // Handle the series ID format from inventory data
        'big 5': NoteSeries.BIG_5,
        'big 5 series': NoteSeries.BIG_5,
        'commemorative': NoteSeries.COMMEMORATIVE,
        'commemorative series': NoteSeries.COMMEMORATIVE,
        'v6': NoteSeries.V6,
        'v6 series': NoteSeries.V6,
        'fake_notes': NoteSeries.FAKE_NOTES,
        'fake notes': NoteSeries.FAKE_NOTES
      };

      const mappedSeries = seriesMap[data.seriesName.toLowerCase()];
      if (mappedSeries) {
        // This is a predefined series
        modalData.series = mappedSeries;
      } else {
        // This is a custom series - pass the ID directly
        modalData.seriesId = data.seriesName;
      }
    }

    if (data?.denomination) {
      modalData.denomination = data.denomination as NoteDenomination;
    }

    if (data?.currentQuantity) {
      modalData.currentQuantity = data.currentQuantity;
    }

    if (data?.isDyeStainedMode !== undefined) {
      modalData.isDyeStainedMode = data.isDyeStainedMode;
    }

    const dialogRef: MatDialogRef<RemoveCashModalComponent, RemoveCashResult> = this.dialog.open(
      RemoveCashModalComponent,
      {
        width: 'calc(100vw - 8rem)', // Full width with 4rem margin on each side
        maxWidth: 'calc(100vw - 8rem)',
        maxHeight: '90vh',
        data: modalData,
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        panelClass: ['add-cash-modal-panel', 'modern-modal-panel'], // Use same panel class as add cash
        backdropClass: ['add-cash-modal-backdrop', 'blur-backdrop'], // Use same backdrop class as add cash
        hasBackdrop: true,
        closeOnNavigation: true,
        enterAnimationDuration: '300ms',
        exitAnimationDuration: '250ms'
      }
    );

    // Handle backdrop click to close
    dialogRef.backdropClick().subscribe(() => {
      dialogRef.close(undefined);
    });

    return dialogRef.afterClosed();
  }

  /**
   * Opens the Remove Cash modal with pre-selected series and denomination
   * @param series The series to pre-select
   * @param denomination The denomination to pre-select
   * @param currentQuantity The current quantity available for removal
   * @returns Observable that emits the result when the modal is closed
   */
  openRemoveCashModalForDenomination(
    series: NoteSeries,
    denomination: NoteDenomination,
    currentQuantity?: number
  ): Observable<RemoveCashResult | undefined> {
    return this.openRemoveCashModal({
      seriesName: series,
      denomination: denomination,
      currentQuantity: currentQuantity
    });
  }

  /**
   * Checks if any Remove Cash modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof RemoveCashModalComponent
    );
  }

  /**
   * Closes all open Remove Cash modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof RemoveCashModalComponent)
      .forEach(dialog => dialog.close({ success: false }));
  }

  /**
   * Gets the count of currently open Remove Cash modals
   * @returns number of open modals
   */
  getOpenModalCount(): number {
    return this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof RemoveCashModalComponent)
      .length;
  }
}
