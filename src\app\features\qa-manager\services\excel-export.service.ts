import { Injectable } from '@angular/core';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

import { AuditReportSummary } from '../components/audit-report-modal/audit-report-modal.component';

@Injectable({
  providedIn: 'root'
})
export class ExcelExportService {

  constructor() { }

  /**
   * Export audit report to Excel with professional tables and advanced styling
   */
  async exportAuditReportToExcel(auditReport: AuditReportSummary): Promise<void> {
    try {
      // Create a new workbook with ExcelJS
      const workbook = new ExcelJS.Workbook();

      // Set workbook properties
      workbook.creator = 'FFA CMS Currency Management System';
      workbook.lastModifiedBy = 'FFA CMS';
      workbook.created = new Date();
      workbook.modified = new Date();
      workbook.lastPrinted = new Date();

      // Only include Overview Sheet
      await this.addOverviewSheet(workbook, auditReport);

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filename = `CMS_Inventory_Audit_Report_${timestamp}.xlsx`;

      // Write and save the file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      saveAs(blob, filename);

      console.log('Professional Excel report exported successfully:', filename);
    } catch (error) {
      console.error('Error exporting Excel report:', error);
      throw new Error('Failed to export Excel report');
    }
  }

  private async addExecutiveSummarySheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('📊 Executive Summary', {
      properties: { tabColor: { argb: 'FF1F4E79' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 35 }, // A - Labels
      { width: 20 }, // B - Values
      { width: 15 }, // C - Status/Percentage
      { width: 15 }, // D - Performance
      { width: 15 }, // E - Additional
      { width: 15 }  // F - Additional
    ];

    // Title Section
    const titleRow = worksheet.addRow(['📊 CMS INVENTORY AUDIT - EXECUTIVE SUMMARY']);
    titleRow.height = 35;
    worksheet.mergeCells('A1:F1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 20, bold: true, color: { argb: 'FFFFFFFF' } },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF1F4E79' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      border: this.getAllBorders()
    };

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:F2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Key Performance Indicators Section
    const kpiHeaderRow = worksheet.addRow(['🎯 KEY PERFORMANCE INDICATORS']);
    worksheet.mergeCells('A4:F4');
    kpiHeaderRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FF1F4E79' } },
      alignment: { horizontal: 'left', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } },
      border: this.getAllBorders()
    };

    worksheet.addRow([]); // Empty row

    // KPI Table (including dye-stained inventory)
    const totalValue = auditReport.totalInventoryValue + auditReport.dyeStainedSummary.totalValue + auditReport.totalCoinValue;
    const totalNotesIncludingDyeStained = auditReport.totalNotes + auditReport.dyeStainedSummary.totalNotes;
    const kpiData = [
      ['💰 Total Portfolio Value', this.formatCurrency(totalValue), '100%', '🎯 TARGET'],
      ['💵 Notes Portfolio Value', this.formatCurrency(auditReport.totalInventoryValue), `${((auditReport.totalInventoryValue / totalValue) * 100).toFixed(1)}%`, '📈 STRONG'],
      ['🎨 Dye-Stained Portfolio Value', this.formatCurrency(auditReport.dyeStainedSummary.totalValue), `${((auditReport.dyeStainedSummary.totalValue / totalValue) * 100).toFixed(1)}%`, '🔒 SECURED'],
      ['🪙 Coins Portfolio Value', this.formatCurrency(auditReport.totalCoinValue), `${((auditReport.totalCoinValue / totalValue) * 100).toFixed(1)}%`, '⚡ STABLE'],
      ['📄 Total Notes Count', this.formatNumber(totalNotesIncludingDyeStained), 'ITEMS', '📊 TRACKED'],
      ['⚪ Total Coins Count', this.formatNumber(auditReport.totalCoins), 'ITEMS', '📊 TRACKED'],
      ['⚠️ Low Stock Alerts', auditReport.lowStockItems.length.toString(), 'ALERTS', auditReport.lowStockItems.length === 0 ? '✅ EXCELLENT' : '⚠️ ATTENTION']
    ];

    // Add KPI table with professional styling
    kpiData.forEach((rowData, index) => {
      const row = worksheet.addRow(rowData);
      row.height = 25;
      row.eachCell((cell, colNumber) => {
        if (colNumber <= 4) {
          cell.style = {
            font: { name: 'Calibri', size: 11, bold: colNumber === 2 },
            alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
            border: this.getAllBorders(),
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: index % 2 === 0 ? 'FFFFFFFF' : 'FFF8F9FA' }
            }
          };

          if (colNumber === 4) { // Status column
            cell.style.font!.bold = true;
            cell.style.font!.color = { argb: this.getStatusColor(rowData[3] as string) };
          }
        }
      });
    });

    // Add spacing after KPI section
    worksheet.addRow([]);
    worksheet.addRow([]);

    // Note Series Performance Section
    const noteHeaderRow = worksheet.addRow(['💵 NOTE SERIES PERFORMANCE BREAKDOWN']);
    worksheet.mergeCells(`A${noteHeaderRow.number}:F${noteHeaderRow.number}`);
    noteHeaderRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FF1F4E79' } },
      alignment: { horizontal: 'left', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } },
      border: this.getAllBorders()
    };

    worksheet.addRow([]); // Empty row

    // Note Series Table Headers
    const noteTableHeaders = ['Series Name', 'Notes Count', 'Batches', 'Singles', 'Portfolio Value', 'Performance'];
    const noteHeadersRow = worksheet.addRow(noteTableHeaders);
    noteHeadersRow.height = 25;
    noteHeadersRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: this.getAllBorders()
      };
    });

    // Note Series Data
    auditReport.noteSeriesSummary.forEach((series, index) => {
      const performance = this.calculatePerformanceIndicator(series.totalValue, totalValue);
      const row = worksheet.addRow([
        `📋 ${series.seriesName}`,
        this.formatNumber(series.totalNotes),
        this.formatNumber(series.totalBatches),
        this.formatNumber(series.totalSingles),
        this.formatCurrency(series.totalValue),
        performance
      ]);

      row.height = 22;
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11, bold: colNumber === 5 },
          alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
          border: this.getAllBorders(),
          fill: {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: index % 2 === 0 ? 'FFFFFFFF' : 'FFF8F9FA' }
          }
        };

        if (colNumber === 6) { // Performance column
          cell.style.font!.bold = true;
          cell.style.font!.color = { argb: this.getStatusColor(performance) };
        }
      });
    });

    // Create Excel Table for Note Series
    const noteTableStartRow = noteHeadersRow.number;
    const noteTableEndRow = noteTableStartRow + auditReport.noteSeriesSummary.length;
    const noteTableRange = `A${noteTableStartRow}:F${noteTableEndRow}`;

    worksheet.addTable({
      name: 'NoteSeriesTable',
      ref: noteTableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium2',
        showRowStripes: true
      },
      columns: noteTableHeaders.map(header => ({ name: header })),
      rows: auditReport.noteSeriesSummary.map(series => [
        `📋 ${series.seriesName}`,
        this.formatNumber(series.totalNotes),
        this.formatNumber(series.totalBatches),
        this.formatNumber(series.totalSingles),
        this.formatCurrency(series.totalValue),
        this.calculatePerformanceIndicator(series.totalValue, totalValue)
      ])
    });
  }

  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(value);
  }

  private formatNumber(value: number): string {
    return new Intl.NumberFormat('en-ZA').format(value);
  }

  // ===== EXCELJS HELPER METHODS =====

  /**
   * Get all borders for ExcelJS cells
   */
  private getAllBorders(): any {
    return {
      top: { style: 'thin', color: { argb: 'FF666666' } },
      left: { style: 'thin', color: { argb: 'FF666666' } },
      bottom: { style: 'thin', color: { argb: 'FF666666' } },
      right: { style: 'thin', color: { argb: 'FF666666' } }
    };
  }

  /**
   * Get status color based on status text
   */
  private getStatusColor(status: string): string {
    if (status.includes('EXCELLENT') || status.includes('✅')) return 'FF0F5132';
    if (status.includes('ATTENTION') || status.includes('⚠️')) return 'FF664D03';
    if (status.includes('CRITICAL') || status.includes('🚨')) return 'FF842029';
    if (status.includes('STRONG') || status.includes('📈')) return 'FF0A58CA';
    if (status.includes('STABLE') || status.includes('⚡')) return 'FF6F42C1';
    return 'FF1F4E79'; // Default blue
  }

  /**
   * Calculate performance indicator for series
   */
  private calculatePerformanceIndicator(seriesValue: number, totalValue: number): string {
    const percentage = (seriesValue / totalValue) * 100;
    if (percentage >= 30) return '🔥 DOMINANT';
    if (percentage >= 20) return '📈 STRONG';
    if (percentage >= 10) return '⚡ ACTIVE';
    if (percentage >= 5) return '📊 MODERATE';
    return '🔍 MINIMAL';
  }

  private async addNoteSeriesDetailsSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('📋 Note Series Details', {
      properties: { tabColor: { argb: 'FF2E7D32' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 25 }, // A - Series Name
      { width: 18 }, // B - Denomination
      { width: 15 }, // C - Quantity
      { width: 12 }, // D - Batches
      { width: 12 }, // E - Singles
      { width: 18 }, // F - Value
      { width: 15 }  // G - Stock Level
    ];

    // Title row
    const titleRow = worksheet.addRow(['📋 NOTE SERIES DETAILED BREAKDOWN']);
    worksheet.mergeCells('A1:G1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF2E7D32' } }
    };
    titleRow.height = 30;

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:G2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Prepare data for all note series
    const noteSeriesData: any[][] = [];
    auditReport.noteSeriesSummary.forEach(series => {
      series.denominations.forEach(denom => {
        noteSeriesData.push([
          series.seriesName,
          denom.denominationLabel,
          denom.quantity,
          denom.batches,
          denom.singles,
          denom.value,
          denom.stockLevel.toUpperCase()
        ]);
      });
    });

    // Add headers
    const headers = ['Series Name', 'Denomination', 'Quantity', 'Batches', 'Singles', 'Value', 'Stock Level'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF2E7D32' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows
    noteSeriesData.forEach(rowData => {
      const row = worksheet.addRow(rowData);
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber >= 3 && colNumber <= 6 ? 'right' : 'left', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
          }
        };

        // Format currency values
        if (colNumber === 6) {
          cell.numFmt = '"R"#,##0.00';
        }
        // Format numbers
        else if (colNumber >= 3 && colNumber <= 5) {
          cell.numFmt = '#,##0';
        }

        // Color code stock levels
        if (colNumber === 7) {
          const stockLevel = cell.value as string;
          if (stockLevel === 'LOW') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFD68910' } };
          } else if (stockLevel === 'OUT-OF-STOCK') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFDEAEA' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFE74C3C' } };
          } else {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE8F5E8' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FF27AE60' } };
          }
        }
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + noteSeriesData.length;
    const tableRange = `A${tableStartRow}:G${tableEndRow}`;

    worksheet.addTable({
      name: 'NoteSeriesDetailsTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium2',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: noteSeriesData
    });
  }

  private async addCoinPortfolioSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('🪙 Coin Portfolio', {
      properties: { tabColor: { argb: 'FFFF8C00' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 18 }, // A - Denomination
      { width: 15 }, // B - Total Quantity
      { width: 12 }, // C - Total Batches
      { width: 18 }, // D - Total Value
      { width: 25 }, // E - Series Breakdown
      { width: 15 }  // F - Performance
    ];

    // Title row
    const titleRow = worksheet.addRow(['🪙 COIN PORTFOLIO ANALYSIS']);
    worksheet.mergeCells('A1:F1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFF8C00' } }
    };
    titleRow.height = 30;

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:F2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Prepare coin data
    const coinData: any[][] = [];
    const totalCoinValue = auditReport.coinSummary.reduce((sum, coin) => sum + coin.totalValue, 0);

    auditReport.coinSummary.forEach(coin => {
      const seriesBreakdown = coin.seriesBreakdown.map(series =>
        `${series.seriesName}: ${series.quantity}`
      ).join(', ');

      const performance = this.calculatePerformanceIndicator(coin.totalValue, totalCoinValue);

      coinData.push([
        coin.denominationLabel,
        coin.totalQuantity,
        coin.totalBatches,
        coin.totalValue,
        seriesBreakdown,
        performance
      ]);
    });

    // Add headers
    const headers = ['Denomination', 'Total Quantity', 'Total Batches', 'Total Value', 'Series Breakdown', 'Performance'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFF8C00' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows
    coinData.forEach(rowData => {
      const row = worksheet.addRow(rowData);
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber >= 2 && colNumber <= 4 ? 'right' : 'left', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
          }
        };

        // Format currency values
        if (colNumber === 4) {
          cell.numFmt = '"R"#,##0.00';
        }
        // Format numbers
        else if (colNumber >= 2 && colNumber <= 3) {
          cell.numFmt = '#,##0';
        }
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + coinData.length;
    const tableRange = `A${tableStartRow}:F${tableEndRow}`;

    worksheet.addTable({
      name: 'CoinPortfolioTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium6',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: coinData
    });
  }

  private async addLowStockAlertsSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('⚠️ Low Stock Alerts', {
      properties: { tabColor: { argb: 'FFDC3545' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 12 }, // A - Type
      { width: 25 }, // B - Series
      { width: 18 }, // C - Denomination
      { width: 15 }, // D - Current Quantity
      { width: 15 }, // E - Stock Level
      { width: 18 }, // F - Value
      { width: 20 }  // G - Action Required
    ];

    // Title row
    const titleRow = worksheet.addRow(['⚠️ LOW STOCK ALERTS & CRITICAL ITEMS']);
    worksheet.mergeCells('A1:G1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDC3545' } }
    };
    titleRow.height = 30;

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:G2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Check if there are low stock items
    if (auditReport.lowStockItems.length === 0) {
      const noAlertsRow = worksheet.addRow(['✅ No low stock alerts - All inventory levels are healthy']);
      worksheet.mergeCells('A4:G4');
      noAlertsRow.getCell(1).style = {
        font: { name: 'Calibri', size: 14, bold: true, color: { argb: 'FF27AE60' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE8F5E8' } }
      };
      noAlertsRow.height = 40;
      return;
    }

    // Prepare low stock data
    const lowStockData: any[][] = [];
    auditReport.lowStockItems.forEach(item => {
      const actionRequired = item.stockLevel === 'out-of-stock' ? 'URGENT RESTOCK' : 'MONITOR & REORDER';
      lowStockData.push([
        item.type.toUpperCase(),
        item.series,
        item.denomination,
        item.currentQuantity,
        item.stockLevel.toUpperCase(),
        item.value,
        actionRequired
      ]);
    });

    // Add headers
    const headers = ['Type', 'Series', 'Denomination', 'Current Quantity', 'Stock Level', 'Value', 'Action Required'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDC3545' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows with conditional formatting
    lowStockData.forEach(rowData => {
      const row = worksheet.addRow(rowData);
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber === 4 || colNumber === 6 ? 'right' : 'left', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
          }
        };

        // Format currency values
        if (colNumber === 6) {
          cell.numFmt = '"R"#,##0.00';
        }
        // Format numbers
        else if (colNumber === 4) {
          cell.numFmt = '#,##0';
        }

        // Color code based on urgency
        if (colNumber === 5) {
          const stockLevel = cell.value as string;
          if (stockLevel === 'OUT-OF-STOCK') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFDEAEA' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFE74C3C' }, bold: true };
          } else if (stockLevel === 'LOW') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFD68910' }, bold: true };
          }
        }

        if (colNumber === 7) {
          const action = cell.value as string;
          if (action === 'URGENT RESTOCK') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFDEAEA' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFE74C3C' }, bold: true };
          } else {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFD68910' }, bold: true };
          }
        }
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + lowStockData.length;
    const tableRange = `A${tableStartRow}:G${tableEndRow}`;

    worksheet.addTable({
      name: 'LowStockAlertsTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium3',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: lowStockData
    });
  }

  private async addDenominationBreakdownSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    // Placeholder implementation - add basic sheet
    const worksheet = workbook.addWorksheet('💰 Denomination Analysis');
    worksheet.addRow(['Denomination Analysis - Implementation Pending']);
  }

  private async addDyeStainedInventorySheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('🎨 Dye-Stained Inventory', {
      properties: { tabColor: { argb: 'FF8B5CF6' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 25 }, // A - Series Name
      { width: 18 }, // B - Denomination
      { width: 15 }, // C - Quantity
      { width: 12 }, // D - Batches
      { width: 12 }, // E - Singles
      { width: 18 }, // F - Value
      { width: 15 }, // G - Stock Level
      { width: 20 }  // H - Performance
    ];

    // Title
    const titleRow = worksheet.addRow(['🎨 DYE-STAINED INVENTORY ANALYSIS']);
    titleRow.height = 35;
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 18, bold: true, color: { argb: 'FF8B5CF6' } },
      alignment: { horizontal: 'left', vertical: 'middle' }
    };
    worksheet.mergeCells('A1:H1');

    // Subtitle with totals
    const subtitleRow = worksheet.addRow([
      `Total Dye-Stained Value: ${this.formatCurrency(auditReport.dyeStainedSummary.totalValue)} | Total Notes: ${this.formatNumber(auditReport.dyeStainedSummary.totalNotes)}`
    ]);
    subtitleRow.height = 25;
    subtitleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF6B7280' } },
      alignment: { horizontal: 'left', vertical: 'middle' }
    };
    worksheet.mergeCells('A2:H2');

    // Empty row
    worksheet.addRow([]);

    // Prepare data for all dye-stained series
    const dyeStainedData: any[][] = [];
    auditReport.dyeStainedSummary.seriesSummary.forEach(series => {
      series.denominations.forEach(denom => {
        const performance = this.calculatePerformanceIndicator(denom.value, auditReport.dyeStainedSummary.totalValue);

        dyeStainedData.push([
          series.seriesName,
          denom.denominationLabel,
          denom.quantity,
          denom.batches,
          denom.singles,
          denom.value,
          denom.stockLevel.toUpperCase(),
          performance
        ]);
      });
    });

    // Add headers
    const headers = ['Series Name', 'Denomination', 'Quantity', 'Batches', 'Singles', 'Value', 'Stock Level', 'Performance'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF8B5CF6' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows
    dyeStainedData.forEach(rowData => {
      const dataRow = worksheet.addRow(rowData);
      dataRow.eachCell((cell, colNumber) => {
        // Format currency for value column (F)
        if (colNumber === 6) {
          cell.numFmt = '"R"#,##0.00';
        }

        // Style based on stock level
        const stockLevel = rowData[6];
        let fillColor = 'FFF8FAFC'; // Default light gray
        if (stockLevel === 'OUT-OF-STOCK') {
          fillColor = 'FFFEF2F2'; // Light red
        } else if (stockLevel === 'LOW') {
          fillColor = 'FFFEF3C7'; // Light yellow
        }

        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber <= 2 ? 'left' : 'center', vertical: 'middle' },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: fillColor } },
          border: {
            top: { style: 'thin', color: { argb: 'FFE2E8F0' } },
            left: { style: 'thin', color: { argb: 'FFE2E8F0' } },
            bottom: { style: 'thin', color: { argb: 'FFE2E8F0' } },
            right: { style: 'thin', color: { argb: 'FFE2E8F0' } }
          }
        };
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + dyeStainedData.length;
    const tableRange = `A${tableStartRow}:H${tableEndRow}`;

    worksheet.addTable({
      name: 'DyeStainedInventoryTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium15',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: dyeStainedData
    });
  }

  private async addDetailedInventorySheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    // Placeholder implementation - add basic sheet
    const worksheet = workbook.addWorksheet('📋 Complete Inventory');
    worksheet.addRow(['Complete Inventory - Implementation Pending']);
  }

  private calculateMarketShare(percentage: number): string {
    if (percentage >= 40) return '🏆 DOMINANT';
    if (percentage >= 25) return '💪 MAJOR';
    if (percentage >= 15) return '📈 SIGNIFICANT';
    if (percentage >= 5) return '📊 MODERATE';
    return '🔍 MINOR';
  }

  /**
   * Add comprehensive Overview sheet as the first tab
   */
  private async addOverviewSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('📊 Overview', {
      properties: { tabColor: { argb: 'FF2E7D32' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 25 }, // A - Series/Description
      { width: 18 }, // B - Total Value
      { width: 15 }, // C - Total Batches
      { width: 15 }, // D - Total Singles
      { width: 18 }, // E - Total Quantity
      { width: 20 }  // F - Additional info
    ];

    let currentRow = 1;

    // Main title
    const titleRow = worksheet.addRow(['📊 COMPREHENSIVE INVENTORY OVERVIEW']);
    worksheet.mergeCells('A1:F1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 18, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF2E7D32' } }
    };
    titleRow.height = 45;
    currentRow++;

    // Subtitle with generation date
    const subtitleRow = worksheet.addRow([`Generated on ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:F2');
    subtitleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center', vertical: 'middle' }
    };
    subtitleRow.height = 25;
    currentRow++;

    // Empty row
    worksheet.addRow([]);
    currentRow++;

    // Section 1: Cash Inventory Summary
    currentRow = await this.addCashInventorySection(worksheet, auditReport, currentRow);

    // Section 2: Coin Inventory Summary
    currentRow = await this.addCoinInventorySection(worksheet, auditReport, currentRow);

    // Section 3: Dye-Stained Inventory Summary
    currentRow = await this.addDyeStainedInventorySection(worksheet, auditReport, currentRow);

    // Section 4: Fake Notes Summary (moved after dye-stained)
    currentRow = await this.addFakeNotesSection(worksheet, auditReport, currentRow);

    // Section 5: Grand Total Summary
    await this.addGrandTotalSection(worksheet, auditReport, currentRow);
  }

  /**
   * Add Cash Inventory Summary section
   */
  private async addCashInventorySection(worksheet: ExcelJS.Worksheet, auditReport: AuditReportSummary, startRow: number): Promise<number> {
    let currentRow = startRow;

    // Section header
    const headerRow = worksheet.addRow(['💵 CASH INVENTORY SUMMARY']);
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    headerRow.getCell(1).style = {
      font: { name: 'Calibri', size: 14, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF1976D2' } }
    };
    headerRow.height = 35;
    currentRow++;

    // Table headers
    const tableHeaders = ['Series Name', 'Total Value', 'Total Batches', 'Total Singles', 'Total Quantity', 'Status'];
    const headersRow = worksheet.addRow(tableHeaders);
    headersRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF424242' } },
        border: {
          top: { style: 'thin', color: { argb: 'FF000000' } },
          left: { style: 'thin', color: { argb: 'FF000000' } },
          bottom: { style: 'thin', color: { argb: 'FF000000' } },
          right: { style: 'thin', color: { argb: 'FF000000' } }
        }
      };
    });
    currentRow++;

    // Calculate cash totals
    let cashSubtotal = 0;
    let totalCashBatches = 0;
    let totalCashSingles = 0;
    let totalCashQuantity = 0;

    // Add note series data (excluding fake notes)
    auditReport.noteSeriesSummary.forEach(series => {
      if (series.seriesName !== 'Fake Notes') {
        const batches = series.totalBatches;
        const singles = series.totalNotes % 100;

        const dataRow = worksheet.addRow([
          series.seriesName,
          this.formatCurrency(series.totalValue),
          batches.toString(),
          singles.toString(),
          series.totalNotes.toString(),
          series.totalNotes > 0 ? '✅ ACTIVE' : '⚠️ EMPTY'
        ]);

        dataRow.eachCell((cell, colNumber) => {
          cell.style = {
            font: { name: 'Calibri', size: 10 },
            alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
            border: {
              top: { style: 'thin', color: { argb: 'FFCCCCCC' } },
              left: { style: 'thin', color: { argb: 'FFCCCCCC' } },
              bottom: { style: 'thin', color: { argb: 'FFCCCCCC' } },
              right: { style: 'thin', color: { argb: 'FFCCCCCC' } }
            }
          };
        });

        cashSubtotal += series.totalValue;
        totalCashBatches += batches;
        totalCashSingles += singles;
        totalCashQuantity += series.totalNotes;
        currentRow++;
      }
    });

    // Cash subtotal row
    const subtotalRow = worksheet.addRow([
      'CASH SUBTOTAL',
      this.formatCurrency(cashSubtotal),
      totalCashBatches.toString(),
      totalCashSingles.toString(),
      totalCashQuantity.toString(),
      '📊 SUMMARY'
    ]);
    subtotalRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF1976D2' } },
        border: {
          top: { style: 'thick', color: { argb: 'FF000000' } },
          left: { style: 'thin', color: { argb: 'FF000000' } },
          bottom: { style: 'thick', color: { argb: 'FF000000' } },
          right: { style: 'thin', color: { argb: 'FF000000' } }
        }
      };
    });
    currentRow++;

    // Empty row
    worksheet.addRow([]);
    currentRow++;

    return currentRow;
  }

  /**
   * Add Coin Inventory Summary section
   */
  private async addCoinInventorySection(worksheet: ExcelJS.Worksheet, auditReport: AuditReportSummary, startRow: number): Promise<number> {
    let currentRow = startRow;

    // Section header
    const headerRow = worksheet.addRow(['🪙 COIN INVENTORY SUMMARY']);
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    headerRow.getCell(1).style = {
      font: { name: 'Calibri', size: 14, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFF8F00' } }
    };
    headerRow.height = 35;
    currentRow++;

    // Table headers
    const tableHeaders = ['Denomination', 'Total Value', 'Total Batches', 'Total Singles', 'Total Quantity', 'Status'];
    const headersRow = worksheet.addRow(tableHeaders);
    headersRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF424242' } },
        border: {
          top: { style: 'thin', color: { argb: 'FF000000' } },
          left: { style: 'thin', color: { argb: 'FF000000' } },
          bottom: { style: 'thin', color: { argb: 'FF000000' } },
          right: { style: 'thin', color: { argb: 'FF000000' } }
        }
      };
    });
    currentRow++;

    // Calculate coin totals
    let coinSubtotal = 0;
    let totalCoinBatches = 0;
    let totalCoinSingles = 0;
    let totalCoinQuantity = 0;

    // Add coin data
    auditReport.coinSummary.forEach(coin => {
      const batches = coin.totalBatches;
      const singles = coin.totalQuantity % 100;

      const dataRow = worksheet.addRow([
        coin.denominationLabel,
        this.formatCurrency(coin.totalValue),
        batches.toString(),
        singles.toString(),
        coin.totalQuantity.toString(),
        coin.totalQuantity > 0 ? '✅ ACTIVE' : '⚠️ EMPTY'
      ]);

      dataRow.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 10 },
          alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFCCCCCC' } },
            left: { style: 'thin', color: { argb: 'FFCCCCCC' } },
            bottom: { style: 'thin', color: { argb: 'FFCCCCCC' } },
            right: { style: 'thin', color: { argb: 'FFCCCCCC' } }
          }
        };
      });

      coinSubtotal += coin.totalValue;
      totalCoinBatches += batches;
      totalCoinSingles += singles;
      totalCoinQuantity += coin.totalQuantity;
      currentRow++;
    });

    // Coin subtotal row
    const subtotalRow = worksheet.addRow([
      'COIN SUBTOTAL',
      this.formatCurrency(coinSubtotal),
      totalCoinBatches.toString(),
      totalCoinSingles.toString(),
      totalCoinQuantity.toString(),
      '📊 SUMMARY'
    ]);
    subtotalRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFF8F00' } },
        border: {
          top: { style: 'thick', color: { argb: 'FF000000' } },
          left: { style: 'thin', color: { argb: 'FF000000' } },
          bottom: { style: 'thick', color: { argb: 'FF000000' } },
          right: { style: 'thin', color: { argb: 'FF000000' } }
        }
      };
    });
    currentRow++;

    // Empty row
    worksheet.addRow([]);
    currentRow++;

    return currentRow;
  }

  /**
   * Add Fake Notes Summary section
   */
  private async addFakeNotesSection(worksheet: ExcelJS.Worksheet, auditReport: AuditReportSummary, startRow: number): Promise<number> {
    let currentRow = startRow;

    // Section header with warning styling
    const headerRow = worksheet.addRow(['⚠️ FAKE NOTES SUMMARY (TRAINING MATERIALS)']);
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    headerRow.getCell(1).style = {
      font: { name: 'Calibri', size: 14, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDC3545' } }
    };
    headerRow.height = 35;
    currentRow++;

    // Disclaimer row
    const disclaimerRow = worksheet.addRow(['⚠️ FAKE NOTES - NOT INCLUDED IN FINANCIAL TOTALS']);
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    disclaimerRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFDC3545' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFEAA7' } },
      border: {
        top: { style: 'thick', color: { argb: 'FFDC3545' } },
        left: { style: 'thick', color: { argb: 'FFDC3545' } },
        bottom: { style: 'thick', color: { argb: 'FFDC3545' } },
        right: { style: 'thick', color: { argb: 'FFDC3545' } }
      }
    };
    disclaimerRow.height = 30;
    currentRow++;

    // Table headers
    const tableHeaders = ['Denomination', 'Face Value', 'Total Batches', 'Total Singles', 'Total Quantity', 'Status'];
    const headersRow = worksheet.addRow(tableHeaders);
    headersRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDC3545' } },
        border: {
          top: { style: 'thin', color: { argb: 'FF000000' } },
          left: { style: 'thin', color: { argb: 'FF000000' } },
          bottom: { style: 'thin', color: { argb: 'FF000000' } },
          right: { style: 'thin', color: { argb: 'FF000000' } }
        }
      };
    });
    currentRow++;

    // Find fake notes data
    const fakeNotesSeries = auditReport.noteSeriesSummary.find(series => series.seriesName === 'Fake Notes');
    let fakeNotesTotalValue = 0;
    let fakeNotesTotalBatches = 0;
    let fakeNotesTotalSingles = 0;
    let fakeNotesTotalQuantity = 0;

    if (fakeNotesSeries && fakeNotesSeries.denominations) {
      fakeNotesSeries.denominations.forEach(denom => {
        const batches = denom.batches;
        const singles = denom.singles;
        const faceValue = denom.value; // This should be the face value for fake notes

        const dataRow = worksheet.addRow([
          denom.denominationLabel,
          this.formatCurrency(faceValue),
          batches.toString(),
          singles.toString(),
          denom.quantity.toString(),
          denom.quantity > 0 ? '📚 TRAINING' : '⚠️ EMPTY'
        ]);

        dataRow.eachCell((cell, colNumber) => {
          cell.style = {
            font: { name: 'Calibri', size: 10 },
            alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFEAA7' } },
            border: {
              top: { style: 'thin', color: { argb: 'FFDC3545' } },
              left: { style: 'thin', color: { argb: 'FFDC3545' } },
              bottom: { style: 'thin', color: { argb: 'FFDC3545' } },
              right: { style: 'thin', color: { argb: 'FFDC3545' } }
            }
          };
        });

        fakeNotesTotalValue += faceValue;
        fakeNotesTotalBatches += batches;
        fakeNotesTotalSingles += singles;
        fakeNotesTotalQuantity += denom.quantity;
        currentRow++;
      });
    }

    // Fake notes subtotal row
    const subtotalRow = worksheet.addRow([
      'FAKE NOTES TOTAL (REFERENCE ONLY)',
      this.formatCurrency(fakeNotesTotalValue),
      fakeNotesTotalBatches.toString(),
      fakeNotesTotalSingles.toString(),
      fakeNotesTotalQuantity.toString(),
      '📚 NOT COUNTED'
    ]);
    subtotalRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDC3545' } },
        border: {
          top: { style: 'thick', color: { argb: 'FF000000' } },
          left: { style: 'thick', color: { argb: 'FF000000' } },
          bottom: { style: 'thick', color: { argb: 'FF000000' } },
          right: { style: 'thick', color: { argb: 'FF000000' } }
        }
      };
    });
    currentRow++;

    // Empty row
    worksheet.addRow([]);
    currentRow++;

    return currentRow;
  }

  private async addDyeStainedInventorySection(worksheet: ExcelJS.Worksheet, auditReport: AuditReportSummary, currentRow: number): Promise<number> {
    // Section header
    const sectionHeaderRow = worksheet.addRow(['🎨 DYE-STAINED INVENTORY SUMMARY']);
    sectionHeaderRow.height = 35;
    sectionHeaderRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FF8B5CF6' } },
      alignment: { horizontal: 'left', vertical: 'middle' }
    };
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    currentRow++;

    // Disclaimer
    const disclaimerRow = worksheet.addRow(['Dye-stained money represents real currency marked with security dye and contributes to total portfolio value.']);
    disclaimerRow.getCell(1).style = {
      font: { name: 'Calibri', size: 10, italic: true, color: { argb: 'FF6B7280' } },
      alignment: { horizontal: 'left', vertical: 'middle' }
    };
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    disclaimerRow.height = 30;
    currentRow++;

    // Table headers
    const tableHeaders = ['Series Name', 'Total Value', 'Total Batches', 'Total Singles', 'Total Quantity', 'Status'];
    const headersRow = worksheet.addRow(tableHeaders);
    headersRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF8B5CF6' } },
        border: {
          top: { style: 'thin', color: { argb: 'FF000000' } },
          left: { style: 'thin', color: { argb: 'FF000000' } },
          bottom: { style: 'thin', color: { argb: 'FF000000' } },
          right: { style: 'thin', color: { argb: 'FF000000' } }
        }
      };
    });
    currentRow++;

    // Calculate dye-stained totals
    let dyeStainedSubtotal = 0;
    let totalDyeStainedBatches = 0;
    let totalDyeStainedSingles = 0;
    let totalDyeStainedQuantity = 0;

    // Add dye-stained series data
    auditReport.dyeStainedSummary.seriesSummary.forEach(series => {
      const dataRow = worksheet.addRow([
        series.seriesName,
        this.formatCurrency(series.totalValue),
        series.totalBatches.toString(),
        series.totalSingles.toString(),
        series.totalNotes.toString(),
        series.totalNotes > 0 ? '🎨 DYE-STAINED' : '⚠️ EMPTY'
      ]);

      // Apply styling
      dataRow.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3E8FF' } },
          border: {
            top: { style: 'thin', color: { argb: 'FFE2E8F0' } },
            left: { style: 'thin', color: { argb: 'FFE2E8F0' } },
            bottom: { style: 'thin', color: { argb: 'FFE2E8F0' } },
            right: { style: 'thin', color: { argb: 'FFE2E8F0' } }
          }
        };
      });

      // Accumulate totals
      dyeStainedSubtotal += series.totalValue;
      totalDyeStainedBatches += series.totalBatches;
      totalDyeStainedSingles += series.totalSingles;
      totalDyeStainedQuantity += series.totalNotes;
      currentRow++;
    });

    // Add subtotal row
    const subtotalRow = worksheet.addRow([
      'DYE-STAINED SUBTOTAL:',
      this.formatCurrency(dyeStainedSubtotal),
      totalDyeStainedBatches.toString(),
      totalDyeStainedSingles.toString(),
      totalDyeStainedQuantity.toString(),
      '💰 REAL VALUE'
    ]);

    subtotalRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true },
        alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDDD6FE' } },
        border: {
          top: { style: 'medium', color: { argb: 'FF8B5CF6' } },
          left: { style: 'thin', color: { argb: 'FF8B5CF6' } },
          bottom: { style: 'medium', color: { argb: 'FF8B5CF6' } },
          right: { style: 'thin', color: { argb: 'FF8B5CF6' } }
        }
      };
    });
    currentRow++;

    // Empty row
    worksheet.addRow([]);
    currentRow++;

    return currentRow;
  }

  /**
   * Add Grand Total Summary section
   */
  private async addGrandTotalSection(worksheet: ExcelJS.Worksheet, auditReport: AuditReportSummary, startRow: number): Promise<void> {
    let currentRow = startRow;

    // Section header
    const headerRow = worksheet.addRow(['🏆 GRAND TOTAL SUMMARY']);
    worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
    headerRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF2E7D32' } }
    };
    headerRow.height = 40;
    currentRow++;

    // Empty row
    worksheet.addRow([]);
    currentRow++;

    // Cash inventory total
    const cashRow = worksheet.addRow([
      'Cash Inventory:',
      this.formatCurrency(auditReport.totalInventoryValue),
      '', '', '', '💵 REAL CURRENCY'
    ]);
    cashRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: colNumber <= 2 },
        alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE8F5E8' } }
      };
    });
    currentRow++;

    // Dye-stained inventory total
    const dyeStainedRow = worksheet.addRow([
      'Dye-Stained Inventory:',
      this.formatCurrency(auditReport.dyeStainedSummary.totalValue),
      '', '', '', '🎨 REAL CURRENCY'
    ]);
    dyeStainedRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: colNumber <= 2 },
        alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3E8FF' } }
      };
    });
    currentRow++;

    // Coin inventory total
    const coinRow = worksheet.addRow([
      'Coin Inventory:',
      this.formatCurrency(auditReport.totalCoinValue),
      '', '', '', '🪙 REAL CURRENCY'
    ]);
    coinRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: colNumber <= 2 },
        alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0F8FF' } }
      };
    });
    currentRow++;

    // Separator line
    const separatorRow = worksheet.addRow([
      '─────────────────────────────────────',
      '─────────────────────',
      '', '', '', ''
    ]);
    separatorRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true },
        alignment: { horizontal: 'center', vertical: 'middle' }
      };
    });
    currentRow++;

    // Grand total (including dye-stained inventory)
    const grandTotal = auditReport.totalInventoryValue + auditReport.dyeStainedSummary.totalValue + auditReport.totalCoinValue;
    const grandTotalRow = worksheet.addRow([
      'Grand Total:',
      this.formatCurrency(grandTotal),
      '', '', '', '🏆 TOTAL VALUE'
    ]);
    grandTotalRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 14, bold: true, color: { argb: 'FFFFFFFF' } },
        alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF2E7D32' } },
        border: {
          top: { style: 'thick', color: { argb: 'FF000000' } },
          left: { style: 'thick', color: { argb: 'FF000000' } },
          bottom: { style: 'thick', color: { argb: 'FF000000' } },
          right: { style: 'thick', color: { argb: 'FF000000' } }
        }
      };
    });
    grandTotalRow.height = 35;
    currentRow++;

    // Empty row
    worksheet.addRow([]);
    currentRow++;

    // Fake notes disclaimer
    const fakeNotesSeries = auditReport.noteSeriesSummary.find(series => series.seriesName === 'Fake Notes');
    const fakeNotesValue = fakeNotesSeries ? fakeNotesSeries.totalValue : 0;

    const disclaimerRow = worksheet.addRow([
      `Fake Notes (Reference Only): ${this.formatCurrency(fakeNotesValue)} - NOT INCLUDED`,
      '', '', '', '', '⚠️ EXCLUDED'
    ]);
    worksheet.mergeCells(`A${currentRow}:E${currentRow}`);
    disclaimerRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 11, bold: true, color: { argb: 'FFDC3545' } },
        alignment: { horizontal: colNumber === 1 ? 'center' : 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFEAA7' } },
        border: {
          top: { style: 'thin', color: { argb: 'FFDC3545' } },
          left: { style: 'thin', color: { argb: 'FFDC3545' } },
          bottom: { style: 'thin', color: { argb: 'FFDC3545' } },
          right: { style: 'thin', color: { argb: 'FFDC3545' } }
        }
      };
    });
  }
}
