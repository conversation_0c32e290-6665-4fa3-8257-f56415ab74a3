import { Component, Input, Output, EventEmitter, <PERSON><PERSON>nit, On<PERSON><PERSON><PERSON>, Renderer2, ElementRef, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

import { NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS } from '../../../../shared/models/inventory.model';
import { InventoryService } from '../../services/inventory.service';

export interface AddCashData {
  series?: NoteSeries;
  seriesId?: string; // For custom series
  denomination?: NoteDenomination;
  isDyeStainedMode?: boolean;
}

@Component({
  selector: 'app-add-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatSnackBarModule
  ],
  templateUrl: './add-cash-modal.component.html',
  styleUrls: ['./add-cash-modal.component.scss']
})
export class AddCashModalComponent implements OnInit, OnDestroy {
  @Output() close = new EventEmitter<any>();
  selectedSeries: NoteSeries | null = null;
  selectedSeriesId: string | null = null; // For custom series
  selectedDenomination: NoteDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';
  isDyeStainedMode: boolean = false;

  // Computed property for total quantity
  get totalQuantity(): number {
    return (this.batches * 100) + this.singles;
  }

  availableSeries = Object.values(NoteSeries).map(series => ({
    value: series,
    label: NOTE_SERIES_LABELS[series]
  }));

  availableDenominations = Object.values(NoteDenomination)
    .filter(d => typeof d === 'number')
    .map(denom => ({
      value: denom as NoteDenomination,
      label: DENOMINATION_LABELS[denom as NoteDenomination]
    }));

  constructor(
    private snackBar: MatSnackBar,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private dialogRef: MatDialogRef<AddCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddCashData,
    private inventoryService: InventoryService
  ) {}

  ngOnInit(): void {
    // Pre-populate if data provided
    if (this.data?.series) {
      this.selectedSeries = this.data.series;
    }
    if (this.data?.seriesId) {
      this.selectedSeriesId = this.data.seriesId;
    }
    if (this.data?.denomination) {
      this.selectedDenomination = this.data.denomination;
    }
    if (this.data?.isDyeStainedMode !== undefined) {
      this.isDyeStainedMode = this.data.isDyeStainedMode;
    }

    // Backdrop blur is now handled entirely by CSS for smoother transitions
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }



  isFormValid(): boolean {
    return !!((this.selectedSeries || this.selectedSeriesId) &&
              this.selectedDenomination &&
              this.totalQuantity > 0);
  }

  selectSeries(series: NoteSeries): void {
    this.selectedSeries = series;
    // Reset denomination when series changes (unless pre-selected)
    if (!this.data?.denomination) {
      this.selectedDenomination = null;
    }
  }

  selectDenomination(denomination: NoteDenomination): void {
    this.selectedDenomination = denomination;
  }

  adjustBatches(delta: number): void {
    this.batches = Math.max(0, this.batches + delta);
    this.onQuantityChange();
  }

  adjustSingles(delta: number): void {
    this.singles = Math.max(0, Math.min(99, this.singles + delta));
    this.onQuantityChange();
  }

  onQuantityChange(): void {
    // Ensure singles don't exceed 99
    if (this.singles > 99) {
      this.singles = 99;
    }
    if (this.singles < 0) {
      this.singles = 0;
    }
    if (this.batches < 0) {
      this.batches = 0;
    }
  }

  getSeriesIcon(series: NoteSeries): string {
    const icons = {
      [NoteSeries.MANDELA]: 'account_balance',
      [NoteSeries.BIG_5]: 'nature',
      [NoteSeries.COMMEMORATIVE]: 'star',
      [NoteSeries.V6]: 'new_releases',
      [NoteSeries.FAKE_NOTES]: 'school'
    };
    return icons[series] || 'category';
  }

  getSeriesDescription(series: NoteSeries): string {
    const descriptions = {
      [NoteSeries.MANDELA]: 'Standard circulation notes',
      [NoteSeries.BIG_5]: 'Wildlife themed series',
      [NoteSeries.COMMEMORATIVE]: 'Special edition notes',
      [NoteSeries.V6]: 'Latest series design',
      [NoteSeries.FAKE_NOTES]: 'Training and educational notes'
    };
    return descriptions[series] || 'Note series';
  }

  getDenominationDescription(denomination: NoteDenomination): string {
    const descriptions = {
      [NoteDenomination.R10]: 'Ten Rand note',
      [NoteDenomination.R20]: 'Twenty Rand note',
      [NoteDenomination.R50]: 'Fifty Rand note',
      [NoteDenomination.R100]: 'One Hundred Rand note',
      [NoteDenomination.R200]: 'Two Hundred Rand note'
    };
    return descriptions[denomination] || 'Bank note';
  }

  getSelectedSeriesLabel(): string {
    return this.selectedSeries ? NOTE_SERIES_LABELS[this.selectedSeries] : '';
  }

  getSelectedDenominationLabel(): string {
    return this.selectedDenomination ? DENOMINATION_LABELS[this.selectedDenomination] : '';
  }

  onAddCash(): void {
    if (!this.isFormValid()) {
      this.snackBar.open(
        'Please fill in all required fields before adding cash to inventory.',
        'Close',
        {
          duration: 5000,
          panelClass: ['error-snackbar']
        }
      );
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory addition"
      const reasonText = this.reason.trim() || 'Manual inventory addition';
      let success = false;

      // Determine which inventory method to use based on dye-stained mode and series type
      if (this.isDyeStainedMode) {
        // Use dye-stained inventory methods
        if (this.selectedSeriesId) {
          // Custom series dye-stained
          success = this.inventoryService.addDyeStainedCashToCustomSeries(
            this.selectedSeriesId,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        } else if (this.selectedSeries) {
          // Predefined series dye-stained
          success = this.inventoryService.addDyeStainedCash(
            this.selectedSeries,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        }
      } else {
        // Use regular inventory methods
        if (this.selectedSeriesId) {
          // Custom series regular
          success = this.inventoryService.addCashToCustomSeries(
            this.selectedSeriesId,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        } else if (this.selectedSeries) {
          // Predefined series regular
          success = this.inventoryService.addCash(
            this.selectedSeries,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        }
      }

      if (success) {
        const quantityDescription = this.batches > 0 && this.singles > 0
          ? `${this.batches} batches + ${this.singles} singles`
          : this.batches > 0
            ? `${this.batches} batches`
            : `${this.singles} singles`;

        const inventoryType = this.isDyeStainedMode ? 'dye-stained inventory' : 'inventory';
        const message = `Successfully added ${quantityDescription} (${this.totalQuantity} notes) x ${DENOMINATION_LABELS[this.selectedDenomination!]} to ${inventoryType}.`;

        this.snackBar.open(message, 'Close', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });

        this.dialogRef.close({ success: true, added: this.totalQuantity });
      } else {
        this.snackBar.open(
          'Failed to add cash to inventory. Please try again or contact support if the problem persists.',
          'Close',
          {
            duration: 5000,
            panelClass: ['error-snackbar']
          }
        );
      }
    } catch (error) {
      console.error('Error adding cash:', error);
      this.snackBar.open(
        'An unexpected error occurred while adding cash to inventory. Please try again.',
        'Close',
        {
          duration: 5000,
          panelClass: ['error-snackbar']
        }
      );
    }
  }

  onCancel(): void {
    this.dialogRef.close(undefined);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  /**
   * Get dye color based on denomination value
   */
  getDyeColor(denominationValue: number): string {
    const dyeColors = ['red', 'blue', 'green', 'purple', 'orange', 'teal', 'pink', 'amber'];

    // Create a consistent color mapping based on denomination value
    const colorIndex = denominationValue % dyeColors.length;
    return dyeColors[colorIndex];
  }

  /**
   * Check if the selected series should show dye stained effects
   */
  shouldShowDyeStainedEffects(): boolean {
    return this.isDyeStainedMode &&
           this.selectedSeries !== NoteSeries.FAKE_NOTES &&
           this.selectedDenomination !== null;
  }
}
