
// ===== CASH REQUESTS CARD =====
.cash-requests-card {
  margin: 2rem;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #e5e7eb;

  ::ng-deep .mat-mdc-card-header {
    padding: 2rem 2rem 0 2rem;
  }

  ::ng-deep .mat-mdc-card-content {
    padding: 1rem 2rem 2rem 2rem;
  }
}

.heading {
  font-size: 2rem;
  margin: 0;
  font-weight: 600;
  color: #1f2937;
  font-family: 'Roboto', sans-serif;
}

.subtitle {
  font-size: 1rem;
  margin: 0.5rem 0 0 0;
  color: #6b7280;
  font-family: 'Roboto', sans-serif;
}

// ===== TABS STYLING =====
.requests-tabs {
  ::ng-deep .mat-mdc-tab-group {
    --mat-tab-header-inactive-label-text-color: #6b7280;
    --mat-tab-header-active-label-text-color: #dc2626;
    --mat-tab-header-inactive-focus-label-text-color: #6b7280;
    --mat-tab-header-active-focus-label-text-color: #dc2626;
  }

  ::ng-deep .mat-mdc-tab-header {
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  ::ng-deep .mdc-tab-indicator__content--underline {
    background-color: #dc2626;
    border-color: #dc2626;
  }
}

// ===== MATERIAL TABLE STYLING =====
.mat-elevation-z8 {
  margin: 2rem;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);

  // Header styling
  ::ng-deep .mat-mdc-header-row {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }

  ::ng-deep .mat-mdc-header-cell {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  // Row styling
  ::ng-deep .mat-mdc-row {
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f9fafb;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  ::ng-deep .mat-mdc-cell {
    padding: 1rem;
    font-size: 0.875rem;
    color: #374151;
    border-bottom: none;
    vertical-align: middle;
  }

  // Action column specific styling
  ::ng-deep .mat-mdc-cell:last-child {
    text-align: center;
    width: 80px;
  }

  ::ng-deep .mat-mdc-header-cell:last-child {
    text-align: center;
    width: 80px;
  }
}








// ===== ACTION BUTTON STYLING =====
::ng-deep .mat-mdc-icon-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f3f4f6;
  color: #6b7280;
  transition: all 0.2s ease;

  &:hover {
    background-color: #dc2626;
    color: white;
    transform: scale(1.05);
  }

  .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

// ===== STATUS CHIP STYLING =====
::ng-deep .mat-mdc-chip {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 16px;
  padding: 0.25rem 0.75rem;

  &.status-pending {
    background-color: #fef3c7;
    color: #92400e;
  }

  &.status-approved {
    background-color: #d1fae5;
    color: #065f46;
  }

  &.status-issued {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.status-rejected {
    background-color: #fee2e2;
    color: #991b1b;
  }

  &.status-completed {
    background-color: #f0fdf4;
    color: #166534;
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .cash-requests-card {
    margin: 1rem;

    ::ng-deep .mat-mdc-card-header {
      padding: 1.5rem 1.5rem 0 1.5rem;
    }

    ::ng-deep .mat-mdc-card-content {
      padding: 1rem 1.5rem 1.5rem 1.5rem;
    }
  }

  .heading {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 0.875rem;
  }

  .mat-elevation-z8 {
    margin: 1rem;
    overflow-x: auto;

    ::ng-deep .mat-mdc-header-cell,
    ::ng-deep .mat-mdc-cell {
      padding: 0.75rem 0.5rem;
      font-size: 0.75rem;
      white-space: nowrap;
    }

    ::ng-deep .mat-mdc-header-cell:first-child,
    ::ng-deep .mat-mdc-cell:first-child {
      padding-left: 1rem;
    }

    ::ng-deep .mat-mdc-header-cell:last-child,
    ::ng-deep .mat-mdc-cell:last-child {
      padding-right: 1rem;
    }
  }

  ::ng-deep .mat-mdc-icon-button {
    width: 36px;
    height: 36px;

    .mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .cash-requests-card {
    margin: 0.5rem;
  }

  .mat-elevation-z8 {
    margin: 0.5rem;
  }

  .heading {
    font-size: 1.25rem;
  }
}
