
.cash-requests-card {
  margin: 10px;
  background-color: white;
  box-shadow: none;
}
.heading {
  font-size: 32px;
  margin: 8px;
  font-weight: 600;
  color: #4f5455;
    font: xx-large;
  font-family: roboto;
}

.subtitle {
  font-size: 24px;
  margin:10px;
  color: #808080;
  font-family: roboto;
}

 .mat-tab-label {
  color: red !important; 
}

.requests-tabs .mat-ink-bar {
  background-color: red !important; 
}
.table-container {
  width: 100%;
  background: white;
  color: red;
}








// // Header styling to match the image
// .header {
//   margin-bottom: 20px;

//   .title {
//     font-size: 24px;
//     font-weight: 600;
//     color: #333;
//     margin: 0 0 8px 0;
//   }

//   .subtitle {
//     font-size: 14px;
//     color: #666;
//     margin: 0;
//   }
// }

// // Tabs container
// .tabs-container {
//   border: 1px solid #e0e0e0;
//   border-radius: 4px;
//   overflow: hidden;
// }

// // Clean tab styling
// .requests-tabs {
//   :host ::ng-deep .mat-mdc-tab-group {
//     --mat-tab-header-inactive-label-text-color: #666;
//     --mat-tab-header-active-label-text-color: #d32f2f;
//     --mat-tab-header-inactive-focus-label-text-color: #666;
//     --mat-tab-header-active-focus-label-text-color: #d32f2f;
//   }

//   :host ::ng-deep .mat-mdc-tab-header {
//     border-bottom: 1px solid #e0e0e0;
//     background: #fafafa;
//   }

//   :host ::ng-deep .mat-mdc-tab {
//     color: #666;

//     &.mdc-tab--active {
//       color: #d32f2f;
//     }
//   }

//   :host ::ng-deep .mat-mdc-tab-header-pagination {
//     display: none;
//   }

//   :host ::ng-deep .mdc-tab-indicator__content--underline {
//     background-color: #d32f2f;
//     border-color: #d32f2f;
//   }
// }

// // Tab content
// .tab-content {
//   padding: 0;
//   background: white;
// }

// // Table container
// .table-container {
//   width: 100%;
//   background: white;
// }

// // Empty state
// .no-data {
//   padding: 40px;
//   text-align: center;
//   color: #666;

//   p {
//     margin: 0;
//     font-size: 14px;
//   }
// }

// // Simple table styling to match the image exactly
// .simple-table {
//   width: 100%;

//   table {
//     width: 100%;
//     border-collapse: collapse;
//     font-size: 14px;

//     thead {
//       tr {
//         border-bottom: 1px solid #e0e0e0;

//         th {
//           text-align: left;
//           padding: 12px 16px;
//           font-weight: 600;
//           color: #333;
//           background: white;
//           border: none;
//           font-size: 14px;
//         }
//       }
//     }

//     tbody {
//       tr {
//         border-bottom: 1px solid #f0f0f0;

//         &:hover {
//           background: #f8f9fa;
//         }

//         &:last-child {
//           border-bottom: none;
//         }

//         td {
//           padding: 12px 16px;
//           color: #333;
//           border: none;
//           vertical-align: middle;

//           // Action button styling
//           button {
//             color: #666;

//             &:hover {
//               color: #d32f2f;
//             }

//             mat-icon {
//               font-size: 18px;
//               width: 18px;
//               height: 18px;
//             }
//           }
//         }
//       }
//     }
//   }
// }

// // Responsive design
// @media (max-width: 768px) {
//   .cash-requests-container {
//     padding: 10px;
//   }

//   .simple-table {
//     overflow-x: auto;

//     table {
//       min-width: 600px;

//       th, td {
//         padding: 8px 12px;
//         font-size: 12px;
//       }
//     }
//   }
// }
