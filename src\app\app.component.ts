import { Component, OnInit } from '@angular/core';
import { PlatformService } from './shared/utils/platform.service';
import { RouterOutlet } from '@angular/router';
import { NavbarComponent } from "./layout/navbar/navbar.component";
import { AuthService } from './features/login/auth.service';
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavbarComponent, CommonModule],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  constructor(
    private platformService: PlatformService,
    public authService: AuthService
  ) {}

  ngOnInit() {
    // Example of platform-specific code
    if (this.platformService.isBrowser()) {
      console.log('Running in browser');
    }
  }

  title = 'ffa-cms';
}
