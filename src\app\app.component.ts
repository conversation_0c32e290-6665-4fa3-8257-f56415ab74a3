import { Component, OnInit } from '@angular/core';
import { PlatformService } from './shared/utils/platform.service';
import { RouterOutlet } from '@angular/router';
import { NavbarComponent } from "./layout/navbar/navbar.component";
import { AuthService } from './features/login/auth.service';
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-root',
  standalone: true,
<<<<<<< HEAD
  imports: [RouterOutlet, NavbarComponent, CommonModule],
=======
  imports: [RouterOutlet],
>>>>>>> 7c5ea40e0b8ee552b48216656b563f1733b04cf0
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  constructor(
    private platformService: PlatformService,
    public authService: AuthService
  ) {}

  ngOnInit() {
    // Example of platform-specific code
    if (this.platformService.isBrowser()) {
      console.log('Running in browser');
    }
  }

  title = 'ffa-cms';
}
