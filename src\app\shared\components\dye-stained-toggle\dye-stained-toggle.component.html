<div class="dye-stained-toggle-container">
  <div class="custom-toggle-wrapper"
       [class.disabled]="disabled"
       [matTooltip]="tooltipText"
       matTooltipPosition="above">

    <!-- Paint Brush Icon -->
    <div class="paint-icon-container">
      <mat-icon class="paint-brush-icon">brush</mat-icon>
      <div class="paint-drip" [class.active]="internalChecked"></div>
    </div>

    <!-- Custom Toggle Switch -->
    <div class="custom-toggle-switch"
         [class.checked]="internalChecked"
         [class.disabled]="disabled"
         (click)="onToggleClick()"
         [attr.aria-label]="getAriaLabel()"
         role="switch"
         [attr.aria-checked]="internalChecked"
         tabindex="0"
         (keydown.enter)="onToggleClick()"
         (keydown.space)="onToggleClick()">

      <!-- Toggle Track -->
      <div class="toggle-track">
        <div class="track-fill" [class.active]="internalChecked"></div>
        <div class="track-labels">
          <span class="track-label off-label">OFF</span>
          <span class="track-label on-label">ON</span>
        </div>
      </div>

      <!-- Toggle Handle -->
      <div class="toggle-handle" [class.checked]="internalChecked">
        <div class="handle-inner">
          <mat-icon class="handle-icon">{{ internalChecked ? 'colorize' : 'block' }}</mat-icon>
        </div>
        <div class="handle-glow" [class.active]="internalChecked"></div>
      </div>
    </div>

    <!-- Label -->
    <div class="toggle-label-container">
      <span class="toggle-label-text">{{ label }}</span>
      <div class="label-underline" [class.active]="internalChecked"></div>
    </div>

    <!-- Status Badge -->
    <div class="status-badge" [class.active]="internalChecked">
      <div class="badge-dot"></div>
      <span class="badge-text">{{ internalChecked ? 'ACTIVE' : 'INACTIVE' }}</span>
    </div>

  </div>
</div>
