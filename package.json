{"name": "ffa-cms", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:ffa-cms": "node dist/ffa-cms/server/server.mjs", "dev:ssr": "ng run ${this.config.appName}:serve-ssr", "serve:ssr": "node dist/${this.config.appName}/server/main.js", "build:ssr": "ng build && ng run ${this.config.appName}:server", "prerender": "ng run ${this.config.appName}:prerender"}, "private": true, "dependencies": {"@angular/animations": "~19.0.0", "@angular/build": "^19.0.7", "@angular/cdk": "~19.2.19", "@angular/common": "~19.0.0", "@angular/compiler": "~19.0.0", "@angular/core": "~19.0.0", "@angular/forms": "~19.0.0", "@angular/material": "^19.2.19", "@angular/platform-browser": "~19.0.0", "@angular/platform-browser-dynamic": "~19.0.0", "@angular/platform-server": "~19.0.0", "@angular/router": "~19.0.0", "@angular/ssr": "~19.0.0", "exceljs": "^4.4.0", "express": "^4.18.2", "file-saver": "^2.0.5", "json-server": "^1.0.0-beta.3", "rxjs": "~7.8.0", "tslib": "^2.6.2", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "~19.0.0", "@angular/cli": "~19.0.0", "@angular/compiler-cli": "~19.0.0", "@types/express": "^4.17.17", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.5", "@types/node": "^18.16.3", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.2"}}