import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import '@angular/material';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import {MatInputModule} from '@angular/material/input';
import {MatButtonModule} from '@angular/material/button';
import {MatSelectModule} from '@angular/material/select';

@Component({
  selector: 'app-request-cash-step-1',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule, 
    ReactiveFormsModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule
  ],
  templateUrl: './request-cash-step-1.component.html',
  styleUrl: './request-cash-step-1.component.scss'
})
export class RequestCashStep1Component {
  fb = inject(FormBuilder)

  requestDetailsForm = this.fb.group({
    requesterName: ['', Validators.required],
    departmentName: ['', Validators.required]
  })
}
