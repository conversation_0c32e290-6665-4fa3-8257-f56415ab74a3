import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import '@angular/material';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { AuthService } from '../../../login/auth.service';
import { User } from '../../../../shared/models/user.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-request-cash-step-1',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
  ],
  templateUrl: './request-cash-step-1.component.html',
  styleUrl: './request-cash-step-1.component.scss',
})
export class RequestCashStep1Component implements OnInit, OnDestroy {
  user!: User;
  requestDetailsForm!: FormGroup;
  isDisabled: boolean = true;
  sub!: Subscription;

  constructor(private fb: FormBuilder, private authService: AuthService) {
    this.requestDetailsForm = this.fb.group({
      requesterName: new FormControl({
        value: '',
        disabled: true,
      }),
      departmentName: new FormControl({
        value: '',
        disabled: true,
      })
    });
  }

  ngOnInit(): void {
    this.sub = this.authService.currentUser$.subscribe((user) => {
      if (user) {
        this.user = user
      }
    })
  }

  ngOnDestroy(): void {
    this.sub.unsubscribe();
  }
}
