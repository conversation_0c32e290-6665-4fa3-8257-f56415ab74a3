import { Component, Inject, OnInit, Renderer2, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

import { NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS } from '../../../../shared/models/inventory.model';
import { InventoryService } from '../../services/inventory.service';

export interface RemoveCashData {
  series?: NoteSeries;
  seriesId?: string; // For custom series
  denomination?: NoteDenomination;
  currentQuantity?: number;
  isDyeStainedMode?: boolean;
}

@Component({
  selector: 'app-remove-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule
  ],
  templateUrl: './remove-cash-modal.component.html',
  styleUrls: ['./remove-cash-modal.component.scss']
})
export class RemoveCashModalComponent implements OnInit, AfterViewInit, OnDestroy {
  // Series and denomination options
  availableSeries = Object.values(NoteSeries);
  availableDenominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

  // Form state
  selectedSeries: NoteSeries | null = null;
  selectedSeriesId: string | null = null; // For custom series
  selectedDenomination: NoteDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';

  // Current inventory data
  currentQuantity: number = 0;
  currentBatches: number = 0;
  currentSingles: number = 0;
  isDyeStainedMode: boolean = false;

  // Calculated values
  totalQuantity: number = 0;
  remainingQuantity: number = 0;
  remainingBatches: number = 0;
  remainingSingles: number = 0;
  totalValue: number = 0;

  // Labels for display
  NOTE_SERIES_LABELS = NOTE_SERIES_LABELS;
  DENOMINATION_LABELS = DENOMINATION_LABELS;

  constructor(
    public dialogRef: MatDialogRef<RemoveCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RemoveCashData,
    private snackBar: MatSnackBar,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private inventoryService: InventoryService
  ) {
    // Pre-populate from dialog data
    if (data?.series) {
      this.selectedSeries = data.series;
    }
    if (data?.seriesId) {
      this.selectedSeriesId = data.seriesId;
    }
    if (data?.denomination) {
      this.selectedDenomination = data.denomination;
    }
    if (data?.isDyeStainedMode !== undefined) {
      this.isDyeStainedMode = data.isDyeStainedMode;
    }

    // For dye-stained mode, always load fresh data instead of using passed currentQuantity
    // This ensures we get the most up-to-date inventory after any cleanup operations
    if (this.isDyeStainedMode && this.selectedSeries && this.selectedDenomination) {
      this.loadCurrentInventory();
    } else if (data?.currentQuantity !== undefined) {
      this.currentQuantity = data.currentQuantity;
      this.updateCurrentInventoryBreakdown();
    }
  }

  ngOnInit(): void {
    // If we have pre-selected series and denomination, load current inventory
    if (this.selectedSeries && this.selectedDenomination) {
      this.loadCurrentInventory();
    }
  }

  ngAfterViewInit(): void {
    // Backdrop blur is now handled entirely by CSS for smoother transitions
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  onSeriesChange(): void {
    this.selectedDenomination = null;
    this.resetQuantities();
    this.loadCurrentInventory();
  }

  onDenominationChange(): void {
    this.resetQuantities();
    this.loadCurrentInventory();
  }

  /**
   * Refresh current inventory data from the service
   */
  refreshCurrentInventory(): void {
    if (this.selectedSeries && this.selectedDenomination) {
      // Always get fresh data from the service
      if (this.isDyeStainedMode) {
        this.currentQuantity = this.inventoryService.getCurrentDyeStainedCashQuantity(this.selectedSeries, this.selectedDenomination);
      } else {
        this.currentQuantity = this.inventoryService.getCurrentCashQuantity(this.selectedSeries, this.selectedDenomination);
      }
      this.updateCurrentInventoryBreakdown();
      this.calculateValues();

      // Reset user input quantities if they exceed the new current quantity
      const maxBatches = Math.floor(this.currentQuantity / 100);
      if (this.batches > maxBatches) {
        this.batches = maxBatches;
      }

      const maxSingles = this.currentQuantity - (this.batches * 100);
      if (this.singles > maxSingles) {
        this.singles = maxSingles;
      }

      this.calculateValues();
    }
  }

  private loadCurrentInventory(): void {
    if (this.selectedSeries && this.selectedDenomination) {
      // Use appropriate inventory method based on dye-stained mode
      if (this.isDyeStainedMode) {
        this.currentQuantity = this.inventoryService.getCurrentDyeStainedCashQuantity(this.selectedSeries, this.selectedDenomination);
      } else {
        this.currentQuantity = this.inventoryService.getCurrentCashQuantity(this.selectedSeries, this.selectedDenomination);
      }
      this.updateCurrentInventoryBreakdown();
      this.calculateValues();
    }
  }

  private updateCurrentInventoryBreakdown(): void {
    this.currentBatches = Math.floor(this.currentQuantity / 100);
    this.currentSingles = this.currentQuantity % 100;
  }

  private resetQuantities(): void {
    this.batches = 0;
    this.singles = 0;
    this.currentQuantity = 0;
    this.currentBatches = 0;
    this.currentSingles = 0;
    this.calculateValues();
  }

  adjustBatches(delta: number): void {
    const newBatches = Math.max(0, this.batches + delta);
    const maxBatches = Math.floor(this.currentQuantity / 100);
    this.batches = Math.min(newBatches, maxBatches);
    this.onQuantityChange();
  }

  adjustSingles(delta: number): void {
    const newSingles = Math.max(0, this.singles + delta);
    const maxSingles = this.currentQuantity - (this.batches * 100);
    this.singles = Math.min(newSingles, maxSingles);
    this.onQuantityChange();
  }

  onQuantityChange(): void {
    // Ensure we don't exceed available inventory
    const maxBatches = Math.floor(this.currentQuantity / 100);
    this.batches = Math.min(this.batches, maxBatches);

    const maxSingles = this.currentQuantity - (this.batches * 100);
    this.singles = Math.min(this.singles, maxSingles);

    this.calculateValues();
  }

  private calculateValues(): void {
    this.totalQuantity = (this.batches * 100) + this.singles;
    this.remainingQuantity = this.currentQuantity - this.totalQuantity;
    this.remainingBatches = Math.floor(this.remainingQuantity / 100);
    this.remainingSingles = this.remainingQuantity % 100;

    if (this.selectedDenomination) {
      this.totalValue = this.totalQuantity * this.selectedDenomination;
    }
  }

  isFormValid(): boolean {
    return !!(
      this.selectedSeries &&
      this.selectedDenomination &&
      this.totalQuantity > 0 &&
      this.totalQuantity <= this.currentQuantity
    );
  }

  getSelectedSeriesLabel(): string {
    return this.selectedSeries ? NOTE_SERIES_LABELS[this.selectedSeries] : '';
  }

  getSelectedDenominationLabel(): string {
    return this.selectedDenomination ? DENOMINATION_LABELS[this.selectedDenomination] : '';
  }

  onRemoveCash(): void {
    if (!this.isFormValid()) {
      this.snackBar.open(
        'Please complete all required fields before removing cash from inventory.',
        'Close',
        {
          duration: 5000,
          panelClass: ['error-snackbar']
        }
      );
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory removal"
      const reasonText = this.reason.trim() || 'Manual inventory removal';
      let success = false;

      // Determine which inventory method to use based on dye-stained mode and series type
      if (this.isDyeStainedMode) {
        // Use dye-stained inventory methods
        if (this.selectedSeriesId) {
          // Custom series dye-stained
          success = this.inventoryService.removeDyeStainedCashFromCustomSeries(
            this.selectedSeriesId,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        } else if (this.selectedSeries) {
          // Predefined series dye-stained
          success = this.inventoryService.removeDyeStainedCash(
            this.selectedSeries,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        }
      } else {
        // Use regular inventory methods
        if (this.selectedSeriesId) {
          // Custom series regular
          success = this.inventoryService.removeCashFromCustomSeries(
            this.selectedSeriesId,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        } else if (this.selectedSeries) {
          // Predefined series regular
          success = this.inventoryService.removeCash(
            this.selectedSeries,
            this.selectedDenomination!,
            this.totalQuantity,
            reasonText
          );
        }
      }

      if (success) {
        const quantityDescription = this.batches > 0 && this.singles > 0
          ? `${this.batches} batches + ${this.singles} singles`
          : this.batches > 0
            ? `${this.batches} batches`
            : `${this.singles} singles`;

        const inventoryType = this.isDyeStainedMode ? 'dye-stained inventory' : 'inventory';
        const message = `Successfully removed ${quantityDescription} (${this.totalQuantity} notes) x ${DENOMINATION_LABELS[this.selectedDenomination!]} from ${inventoryType}.`;

        this.snackBar.open(message, 'Close', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });

        this.dialogRef.close({ success: true, removed: this.totalQuantity });
      } else {
        this.snackBar.open(
          'Failed to remove cash from inventory. This may be due to insufficient quantity or a system error. Please check the available quantity and try again.',
          'Close',
          {
            duration: 5000,
            panelClass: ['error-snackbar']
          }
        );
      }
    } catch (error) {
      console.error('Error removing cash:', error);
      this.snackBar.open(
        'An unexpected error occurred while removing cash from inventory. Please try again.',
        'Close',
        {
          duration: 5000,
          panelClass: ['error-snackbar']
        }
      );
    }
  }

  onCancel(): void {
    this.dialogRef.close(undefined);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  /**
   * Get dye color based on denomination value
   */
  getDyeColor(denominationValue: number): string {
    const dyeColors = ['red', 'blue', 'green', 'purple', 'orange', 'teal', 'pink', 'amber'];

    // Create a consistent color mapping based on denomination value
    const colorIndex = denominationValue % dyeColors.length;
    return dyeColors[colorIndex];
  }

  /**
   * Check if the selected series should show dye stained effects
   */
  shouldShowDyeStainedEffects(): boolean {
    return this.isDyeStainedMode &&
           this.selectedSeries !== NoteSeries.FAKE_NOTES &&
           this.selectedDenomination !== null;
  }



  getSeriesIcon(series: NoteSeries): string {
    const icons = {
      [NoteSeries.MANDELA]: 'account_balance',
      [NoteSeries.BIG_5]: 'nature',
      [NoteSeries.COMMEMORATIVE]: 'star',
      [NoteSeries.V6]: 'new_releases',
      [NoteSeries.FAKE_NOTES]: 'school'
    };
    return icons[series] || 'category';
  }

  getSeriesDescription(series: NoteSeries): string {
    const descriptions = {
      [NoteSeries.MANDELA]: 'Standard circulation notes',
      [NoteSeries.BIG_5]: 'Wildlife themed series',
      [NoteSeries.COMMEMORATIVE]: 'Special edition notes',
      [NoteSeries.V6]: 'Latest series design',
      [NoteSeries.FAKE_NOTES]: 'Training and educational notes'
    };
    return descriptions[series] || 'Note series';
  }
}
