mat-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--backgound-white);
    margin: 0;
    padding: 0;
    border: 1px solid var(--backgound-white);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 64px;
    min-height: 64px;
    max-height: 64px;
    width: 100%;
    box-sizing: border-box;

    // Prevent any layout shifts
    contain: layout style;
    will-change: auto;

    // Ensure consistent rendering
    backface-visibility: hidden;
    transform: translateZ(0);
}

.left {
    height: 64px;
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: var(--absa-red);
    padding: 0;
    padding-right: 5%;
    min-height: 64px;
    max-height: 64px;
}

.left #image {
    height: 56px;
    width: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.left #image img {
    height: 100%;
    width: 100%;
    object-fit: contain;
    display: block;
}

.left #header {
    font-weight: 600;
    color: white;
    margin-left: 8px;
    font-size: 20px;
    display: flex;
    align-items: center;
    height: 64px;
    padding-left: 12px;
    white-space: nowrap;
    flex-shrink: 0;
}


.center {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.right {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
    height: 64px;
    padding-right: 16px;
}

.right .profile {
    display: flex;
    align-items: center;
    padding-right: 10px;
    height: 100%;
}

.right .username {
    margin-left: 8px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

.right .menu-items {
    padding-right: 10px;
    height: 100%;
    display: flex;
    align-items: center;
}

// Logout button styling
.mat-mdc-menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 48px;

    mat-icon {
        color: #f44336;
        font-size: 20px;
    }

    span {
        color: #333;
        font-weight: 500;
    }

    &:hover {
        background-color: #f5f5f5;

        mat-icon {
            color: #d32f2f;
        }
    }
}
