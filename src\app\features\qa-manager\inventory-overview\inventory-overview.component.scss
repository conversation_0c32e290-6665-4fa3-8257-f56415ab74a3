// ===== ABSA INVENTORY OVERVIEW COMPONENT STYLES =====

// ===== RESPONSIVE BREAKPOINTS =====
$mobile-small: 320px;
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$desktop-large: 1200px;
$desktop-xl: 1440px;

// ===== MODAL OVERLAY STYLES =====
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(15, 23, 42, 0.4);
  backdrop-filter: blur(8px) saturate(1.1) brightness(0.9);
  -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: backdropFadeIn 0.3s ease-out;
  cursor: pointer;

  &:hover {
    backdrop-filter: blur(12px) saturate(1.2) brightness(0.85);
    -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(0.85);
    background: rgba(15, 23, 42, 0.5);
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9);
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9);
  }
}

// ===== RESPONSIVE MIXINS =====
@mixin mobile-small {
  @media (max-width: #{$mobile-small - 1px}) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: #{$tablet - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $desktop) {
    @content;
  }
}

@mixin desktop-large {
  @media (min-width: $desktop-large) {
    @content;
  }
}

.absa-inventory-container {
  background: var(--absa-white);
  position: relative;
  margin-top: 2rem; // Add space from navbar

  @include tablet {
    margin-top: 1rem;
  }

  @include mobile {
    margin-top: 0.5rem;
    // Scale down everything on mobile for better fit
    transform: scale(0.9);
    transform-origin: top left;
    width: 111.11%; // Compensate for scale
  }

  @include mobile-small {
    transform: scale(0.8);
    width: 125%; // Compensate for scale
  }
}

// ===== HERO HEADER SECTION =====
.absa-hero-header {
  position: relative;
  z-index: 1;
  background: var(--absa-gradient-primary);
  color: var(--absa-white);
  margin: 0 2rem 2rem 2rem; // Match content margins exactly
  padding: 2rem;
  border-radius: 12px; // Add rounded corners
  box-shadow: var(--absa-shadow-lg);

  @include tablet {
    margin: 0 1rem 1.5rem 1rem;
    padding: 1.5rem;
    border-radius: 8px;
  }

  @include mobile {
    margin: 0 0.5rem 1rem 0.5rem;
    padding: 1rem;
    border-radius: 6px;
  }

  @include mobile-small {
    margin: 0 0.25rem 0.75rem 0.25rem;
    padding: 0.75rem;
  }

  .hero-content {
    display: flex;
    justify-content: center; // Center the content
    align-items: center;
    gap: 2rem;

    @include tablet {
      flex-direction: column;
      text-align: center;
      gap: 1.5rem;
    }

    @include mobile {
      gap: 1rem;
    }

    @include mobile-small {
      gap: 0.75rem;
    }
  }

  .hero-text {
    text-align: center; // Center the text content

    .hero-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      display: flex;
      align-items: center;
      justify-content: center; // Center the title content
      gap: 1rem;
      letter-spacing: -0.02em;

      @include tablet {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 0.375rem;
      }

      @include mobile {
        font-size: 1.75rem;
        gap: 0.375rem;
        margin-bottom: 0.25rem;
      }

      @include mobile-small {
        font-size: 1.5rem;
        gap: 0.25rem;
        margin-bottom: 0.125rem;
      }

      .hero-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        color: var(--absa-gold);

        @include tablet {
          font-size: 2.5rem;
          width: 2.5rem;
          height: 2.5rem;
        }

        @include mobile {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }

        @include mobile-small {
          font-size: 1.75rem;
          width: 1.75rem;
          height: 1.75rem;
        }
      }
    }

    .hero-subtitle {
      font-size: 1.125rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 400;

      @include tablet {
        font-size: 1rem;
      }

      @include mobile {
        font-size: 0.875rem;
      }

      @include mobile-small {
        font-size: 0.75rem;
      }
    }
  }



  .hero-actions {
    .absa-fab-primary {
      background: var(--absa-gold);
      color: var(--absa-red);
      font-weight: 600;
      box-shadow: var(--absa-shadow-lg);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: var(--absa-gold-light);
        transform: translateY(-2px);
        box-shadow: var(--absa-shadow-xl);
      }

      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// ===== SIMPLE HEADER SECTION =====
.inventory-header {
  margin: 0 2rem 2rem 2rem;
  padding: 1.5rem 0 1rem 0;
  border-bottom: 1px solid #e2e8f0;

  @include tablet {
    margin: 0 1.5rem 1.5rem 1.5rem;
    padding: 1.25rem 0 0.75rem 0;
  }

  @include mobile {
    margin: 0 1rem 1rem 1rem;
    padding: 1rem 0 0.5rem 0;
  }

  @include mobile-small {
    margin: 0 0.5rem 0.75rem 0.5rem;
    padding: 0.75rem 0 0.5rem 0;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    text-align: center;

    @include tablet {
      font-size: 1.75rem;
    }

    @include mobile {
      font-size: 1.5rem;
    }

    @include mobile-small {
      font-size: 1.375rem;
    }
  }
}

// ===== MAIN CONTENT AREA =====
.absa-main-content {
  position: relative;
  z-index: 1;
  margin: 0 2rem 2rem 2rem; // Match header margins
  padding: 0 0 2rem;

  @include tablet {
    margin: 0 1rem 1.5rem 1rem;
    padding: 0 0 1.5rem;
  }

  @include mobile {
    margin: 0 0.5rem 1rem 0.5rem;
    padding: 0 0 1rem;
  }

  @include mobile-small {
    margin: 0 0.25rem 0.75rem 0.25rem;
    padding: 0 0 0.75rem;
  }

  @media (max-width: 768px) {
    margin: 0 1rem 2rem 1rem;
    padding: 0 0 2rem;
  }
}

// ===== DASHBOARD SECTION =====
.absa-dashboard-section {
  margin-bottom: 4rem;

  @include tablet {
    margin-bottom: 3rem;
  }

  @include mobile {
    margin-bottom: 2rem;
  }

  @include mobile-small {
    margin-bottom: 1.5rem;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;

    @include desktop-large {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
    }

    @include desktop {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.75rem;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    @include mobile {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    @include mobile-small {
      gap: 0.75rem;
    }
  }
}

// ===== METRIC CARDS =====
.absa-metric-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  position: relative;
  border: 1px solid #e2e8f0;
  border-left: 4px solid #10b981; // Default green highlight
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  @include tablet {
    padding: 1.25rem;
  }

  @include mobile {
    padding: 1rem;
  }

  @include mobile-small {
    padding: 0.75rem;
  }

  &:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  &:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .card-content {
    position: relative;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;

    @include tablet {
      margin-bottom: 1.25rem;
    }

    @include mobile {
      margin-bottom: 1rem;
    }

    @include mobile-small {
      margin-bottom: 0.75rem;
    }

    .metric-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(16, 185, 129, 0.1); // Default light green background
      border: none;

      @include tablet {
        width: 44px;
        height: 44px;
      }

      @include mobile {
        width: 40px;
        height: 40px;
      }

      @include mobile-small {
        width: 36px;
        height: 36px;
      }

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        color: #10b981; // Default green color

        @include tablet {
          font-size: 1.375rem;
          width: 1.375rem;
          height: 1.375rem;
        }

        @include mobile {
          font-size: 1.25rem;
          width: 1.25rem;
          height: 1.25rem;
        }

        @include mobile-small {
          font-size: 1.125rem;
          width: 1.125rem;
          height: 1.125rem;
        }
      }
    }

    .metric-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.375rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      @include tablet {
        padding: 0.25rem 0.5rem;
        font-size: 0.625rem;
        border-radius: 16px;
        gap: 0.125rem;
      }

      @include mobile {
        padding: 0.125rem 0.375rem;
        font-size: 0.5rem;
        border-radius: 12px;
      }

      @include mobile-small {
        padding: 0.125rem 0.25rem;
        font-size: 0.375rem;
        border-radius: 8px;
      }

      &.positive {
        background: rgba(52, 168, 83, 0.1);
        color: var(--absa-success);
      }

      &.neutral {
        background: var(--absa-gray-100);
        color: var(--absa-gray-600);
      }

      &.warning {
        background: rgba(251, 188, 4, 0.1);
        color: var(--absa-warning);
      }

      &.critical {
        background: rgba(220, 38, 127, 0.1);
        color: var(--absa-error);
        border: 1px solid rgba(220, 38, 127, 0.2);
      }

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;

        @include tablet {
          font-size: 0.75rem;
          width: 0.75rem;
          height: 0.75rem;
        }

        @include mobile {
          font-size: 0.625rem;
          width: 0.625rem;
          height: 0.625rem;
        }

        @include mobile-small {
          font-size: 0.5rem;
          width: 0.5rem;
          height: 0.5rem;
        }
      }
    }
  }

  .card-body {
    .metric-value {
      font-size: 2.25rem;
      font-weight: 700;
      color: var(--absa-gray-900);
      line-height: 1;
      margin-bottom: 0.5rem;
      letter-spacing: -0.02em;

      @include tablet {
        font-size: 1.875rem;
        margin-bottom: 0.375rem;
      }

      @include mobile {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
      }

      @include mobile-small {
        font-size: 1.25rem;
        margin-bottom: 0.125rem;
      }
    }

    .metric-label {
      font-size: 1rem;
      font-weight: 600;
      color: var(--absa-gray-700);
      margin-bottom: 0.25rem;

      @include tablet {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
      }

      @include mobile {
        font-size: 0.75rem;
        margin-bottom: 0.125rem;
      }

      @include mobile-small {
        font-size: 0.625rem;
        margin-bottom: 0.0625rem;
      }
    }

    .metric-description {
      font-size: 0.875rem;
      color: var(--absa-gray-500);
      font-weight: 400;

      @include tablet {
        font-size: 0.75rem;
      }

      @include mobile {
        font-size: 0.625rem;
      }

      @include mobile-small {
        font-size: 0.5rem;
      }
    }
  }

  // Progress bars removed for clean design
}

// ===== CARD-SPECIFIC STYLING =====
.absa-metric-card {
  // Total Inventory Card - Green with breakdown
  &.value-card.total-inventory {
    border-left-color: #10b981; // Green
    .metric-icon {
      background: rgba(16, 185, 129, 0.1) !important; // Light green background

      mat-icon {
        color: #10b981 !important; // Green icon
      }
    }

    // Inventory breakdown styling
    .inventory-breakdown {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #e2e8f0;

      .breakdown-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;

        &:last-child {
          margin-bottom: 0;
        }

        .breakdown-label {
          color: var(--absa-gray-600);
          font-weight: 500;
        }

        .breakdown-value {
          color: var(--absa-gray-900);
          font-weight: 600;
        }

        @include tablet {
          font-size: 0.75rem;
          margin-bottom: 0.375rem;
        }

        @include mobile {
          font-size: 0.625rem;
          margin-bottom: 0.25rem;
        }

        @include mobile-small {
          font-size: 0.5rem;
          margin-bottom: 0.125rem;
        }
      }

      @include tablet {
        margin-top: 0.75rem;
        padding-top: 0.75rem;
      }

      @include mobile {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
      }

      @include mobile-small {
        margin-top: 0.375rem;
        padding-top: 0.375rem;
      }
    }
  }



  // Low Stock Alerts Card - Orange/Yellow
  &.alerts-card {
    border-left-color: #f59e0b; // Orange
    .metric-icon {
      background: rgba(245, 158, 11, 0.1) !important; // Light orange background

      mat-icon {
        color: #f59e0b !important; // Orange icon
      }
    }
  }

  // Out of Stock Card - Red
  &.out-of-stock-card {
    border-left-color: #ef4444; // Red
    .metric-icon {
      background: rgba(239, 68, 68, 0.1) !important; // Light red background

      mat-icon {
        color: #ef4444 !important; // Red icon
      }
    }
  }

  // Note Series Card - Purple
  &.series-card {
    border-left-color: #8b5cf6; // Purple
    .metric-icon {
      background: rgba(139, 92, 246, 0.1) !important; // Light purple background

      mat-icon {
        color: #8b5cf6 !important; // Purple icon
      }
    }
  }
}

// ===== INVENTORY SECTION =====
.absa-inventory-section {
  .inventory-container {
    background: var(--absa-white);
    border-radius: 24px;
    box-shadow: var(--absa-shadow-lg);
    overflow: hidden;
    border: 1px solid var(--absa-gray-200);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--absa-gradient-primary);
      z-index: 1;
    }
  }

  .inventory-content {
    padding: 2rem;

    @media (max-width: 768px) {
      padding: 1rem;
    }
  }
}

// Content area ready for redesign

// ===== SERIES DASHBOARD =====
.series-dashboard {
  margin-top: 2rem;

  .series-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .series-stat-card {
    background: var(--absa-white);
    border: 1px solid var(--absa-gray-200);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    box-shadow: var(--absa-shadow-sm);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--absa-shadow-md);
      border-color: var(--absa-red-200);
    }

    .stat-icon-wrapper {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        color: var(--absa-white);
      }
    }

    &.notes-stat .stat-icon-wrapper {
      background: var(--absa-gradient-primary);
    }

    &.value-stat .stat-icon-wrapper {
      background: var(--absa-gradient-gold);
    }

    .stat-content {
      flex: 1;

      .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--absa-gray-900);
        line-height: 1;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--absa-gray-600);
        font-weight: 500;
        margin-bottom: 0.5rem;
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        color: var(--absa-success);
        font-weight: 600;

        mat-icon {
          font-size: 0.875rem;
          width: 0.875rem;
          height: 0.875rem;
        }
      }
    }
  }
}

// ===== INVENTORY GRID =====
.absa-inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 1400px) {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
  }
}

// ===== INVENTORY CARDS =====
.inventory-card {
  background: var(--absa-white);
  border-radius: 20px;
  box-shadow: var(--absa-shadow-md);
  border: 1px solid var(--absa-gray-200);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--absa-shadow-xl);
    border-color: var(--absa-red-200);
  }

  // Series-specific left border
  &.series-mandela {
    border-left: 4px solid var(--absa-success);
  }

  &.series-big_5 {
    border-left: 4px solid var(--absa-warning);
  }

  &.series-commemorative {
    border-left: 4px solid #9C27B0;
  }

  &.series-v6 {
    border-left: 4px solid var(--absa-info);
  }

  .card-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;

    .denomination-section {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;

      .denomination-visual {
        position: relative;

        .note-preview {
          width: 60px;
          height: 40px;
          border-radius: 8px;
          overflow: hidden;
          position: relative;
          background: var(--absa-gray-100);
          border: 2px solid var(--absa-gray-300);

          .note-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .note-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--absa-red-100);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            .note-icon {
              color: var(--absa-red);
              font-size: 1.25rem;
            }
          }

          &:hover .note-overlay {
            opacity: 1;
          }
        }

        .denomination-badge {
          position: absolute;
          bottom: -8px;
          right: -8px;
          background: var(--absa-gradient-primary);
          color: var(--absa-white);
          font-size: 0.75rem;
          font-weight: 600;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          box-shadow: var(--absa-shadow-sm);
        }
      }

      .denomination-info {
        .denomination-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--absa-gray-900);
          margin: 0 0 0.25rem 0;
        }

        .denomination-series {
          font-size: 0.875rem;
          color: var(--absa-gray-600);
          margin: 0 0 0.5rem 0;
        }

        .denomination-value {
          font-size: 1rem;
          font-weight: 600;
          color: var(--absa-red);
        }
      }
    }

    .status-section {
      .status-chip {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.375rem 0.75rem;
        border-radius: 16px;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.status-in-stock {
          background: rgba(52, 168, 83, 0.1);
          color: var(--absa-success);
        }

        &.status-out-of-stock {
          background: rgba(239, 68, 68, 0.15);
          color: var(--absa-error);
          border: 1px solid rgba(239, 68, 68, 0.3);
          font-weight: 700;
          animation: pulse 2s infinite;
        }

        &.status-watch {
          background: rgba(251, 188, 4, 0.1);
          color: var(--absa-warning);
        }

        &.status-low {
          background: rgba(234, 67, 53, 0.1);
          color: var(--absa-error);
        }

        mat-icon {
          font-size: 0.875rem;
          width: 0.875rem;
          height: 0.875rem;
          margin-right: 0.25rem;
        }
      }
    }
  }

  .card-body {
    padding: 0 1.5rem 1rem;

    .metrics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .metric-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--absa-gray-50);
        border-radius: 12px;
        border: 1px solid var(--absa-gray-200);
        transition: all 0.3s ease;

        &:hover {
          background: var(--absa-red-50);
          border-color: var(--absa-red-200);
        }

        .metric-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          mat-icon {
            font-size: 1.25rem;
            width: 1.25rem;
            height: 1.25rem;
            color: var(--absa-white);
          }
        }

        &.quantity-metric .metric-icon {
          background: var(--absa-gradient-primary);
        }

        &.value-metric .metric-icon {
          background: var(--absa-gradient-gold);
        }

        .metric-content {
          .metric-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--absa-gray-900);
            line-height: 1;
            margin-bottom: 0.25rem;
          }

          .metric-label {
            font-size: 0.75rem;
            color: var(--absa-gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }

    .stock-level-section {
      .stock-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .stock-label {
          font-size: 0.875rem;
          color: var(--absa-gray-600);
          font-weight: 500;
        }

        .stock-percentage {
          font-size: 0.875rem;
          color: var(--absa-red);
          font-weight: 600;
        }
      }

      .stock-progress-container {
        .absa-progress-bar {
          height: 8px;
          border-radius: 4px;
          background: var(--absa-gray-200);

          ::ng-deep {
            .mat-mdc-progress-bar-buffer {
              background: var(--absa-gray-200);
            }

            .mat-mdc-progress-bar-fill::after {
              background: var(--absa-gradient-primary);
            }

            &.mat-warn .mat-mdc-progress-bar-fill::after {
              background: var(--absa-gradient-gold);
            }
          }
        }
      }
    }
  }

  .card-actions {
    padding: 1rem 1.5rem 1.5rem;
    display: flex;
    gap: 0.75rem;
    border-top: 1px solid var(--absa-gray-200);

    .absa-action-btn {
      flex: 1;
      font-weight: 600;
      border-radius: 12px;
      padding: 0.75rem 1rem;
      transition: all 0.3s ease;

      &.primary {
        background: var(--absa-gradient-primary);
        color: var(--absa-white);
        border: none;
        box-shadow: var(--absa-shadow-sm);

        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--absa-shadow-md);
        }
      }

      &.secondary {
        background: transparent;
        color: var(--absa-red);
        border: 1px solid var(--absa-red);

        &:hover {
          background: var(--absa-red-50);
        }
      }

      mat-icon {
        margin-right: 0.5rem;
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// ===== NO DATA STATE =====
.absa-no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .no-data-illustration {
    margin-bottom: 2rem;
    position: relative;

    .illustration-circle {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: var(--absa-gradient-subtle);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      position: relative;
      animation: float 3s ease-in-out infinite;

      .illustration-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        color: var(--absa-gray-400);
      }
    }

    .illustration-dots {
      display: flex;
      justify-content: center;
      gap: 0.5rem;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--absa-red);
        animation: pulse 2s infinite;

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }

  .no-data-content {
    max-width: 400px;

    .no-data-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--absa-gray-900);
      margin: 0 0 1rem 0;
    }

    .no-data-description {
      font-size: 1rem;
      color: var(--absa-gray-600);
      line-height: 1.5;
      margin: 0 0 2rem 0;
    }

    .absa-action-btn.large {
      padding: 1rem 2rem;
      font-size: 1rem;
      border-radius: 16px;

      mat-icon {
        margin-right: 0.75rem;
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
}

// ===== INVENTORY MODE TOGGLE FAB =====
.inventory-mode-fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;

  .inventory-mode-fab {
    width: 64px;
    height: 64px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.15),
      0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);

    &:hover {
      transform: scale(1.1) translateY(-2px);
      box-shadow:
        0 12px 35px rgba(0, 0, 0, 0.2),
        0 6px 15px rgba(0, 0, 0, 0.15);
      border-color: rgba(255, 255, 255, 0.2);
    }

    &:active {
      transform: scale(0.95);
    }

    // Normal mode (gray)
    &.normal-mode {
      background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
      color: var(--absa-white);
    }

    // Dye-stained mode (purple with glow)
    &.dye-stained-mode {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      color: var(--absa-white);
      border-color: rgba(212, 175, 55, 0.3);
      box-shadow:
        0 8px 25px rgba(139, 92, 246, 0.3),
        0 4px 10px rgba(139, 92, 246, 0.2),
        0 0 20px rgba(139, 92, 246, 0.1);

      &:hover {
        background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
        box-shadow:
          0 12px 35px rgba(139, 92, 246, 0.4),
          0 6px 15px rgba(139, 92, 246, 0.3),
          0 0 25px rgba(139, 92, 246, 0.2);
        border-color: var(--absa-gold);
      }

      // Pulsing animation for dye-stained state
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-radius: 50%;
        z-index: -1;
        opacity: 0.3;
        animation: dyeStainedPulse 2s ease-in-out infinite;
      }
    }

    // Coins mode (gold/bronze)
    &.coins-mode {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: var(--absa-white);
      border-color: rgba(16, 185, 129, 0.3);
      box-shadow:
        0 8px 25px rgba(245, 158, 11, 0.3),
        0 4px 10px rgba(217, 119, 6, 0.2),
        0 0 20px rgba(245, 158, 11, 0.1);

      &:hover {
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        box-shadow:
          0 12px 35px rgba(245, 158, 11, 0.4),
          0 6px 15px rgba(217, 119, 6, 0.3),
          0 0 25px rgba(245, 158, 11, 0.2);
        border-color: #10b981;
      }

      // Pulsing animation for coins state
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border-radius: 50%;
        z-index: -1;
        opacity: 0.3;
        animation: coinsPulse 2s ease-in-out infinite;
      }
    }

    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      transition: all 0.3s ease;
    }

    // Icon rotation animation on state change
    &:hover mat-icon {
      animation: iconSpin 0.5s ease-in-out;
    }
  }

  @media (max-width: 768px) {
    bottom: 1rem;
    right: 1rem;

    .inventory-mode-fab {
      width: 56px;
      height: 56px;

      mat-icon {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
}

// ===== ANIMATIONS =====
@keyframes dyeStainedPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.1);
  }
}

@keyframes coinsPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.1);
  }
}

@keyframes iconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// ===== ANIMATIONS =====

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

@keyframes pulse-critical {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 23, 68, 0.7);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(255, 23, 68, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// ===== RESPONSIVE DESIGN ENHANCEMENTS =====
@media (max-width: 1200px) {

  .absa-main-content {
    margin: 0 1.5rem 2rem 1.5rem;
    padding: 0 0 2rem;
  }

  .absa-dashboard-section .dashboard-grid {
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {

  .absa-main-content {
    margin: 0 1rem 1.5rem 1rem;
    padding: 0 0 1.5rem;
  }

  .series-dashboard .series-stats {
    gap: 1rem;
  }

  .inventory-card {
    .card-header {
      padding: 1rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .denomination-section {
        width: 100%;
      }

      .status-section {
        align-self: flex-end;
      }
    }

    .card-body {
      padding: 0 1rem 1rem;

      .metrics-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }
    }

    .card-actions {
      padding: 1rem;
      flex-direction: column;

      .absa-action-btn {
        width: 100%;
      }
    }
  }
}

@media (max-width: 480px) {

  .absa-dashboard-section .dashboard-grid {
    gap: 1rem;
  }

  .absa-metric-card {
    padding: 1rem;

    .card-header {
      margin-bottom: 1rem;

      .metric-icon {
        width: 50px;
        height: 50px;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }

    .card-body .metric-value {
      font-size: 1.75rem;
    }
  }

  .inventory-card .card-header .denomination-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {

  .absa-metric-card,
  .inventory-card {
    border-width: 2px;
    border-color: var(--absa-gray-900);
  }

  .absa-action-btn.primary {
    background: var(--absa-red) !important;
    border: 2px solid var(--absa-gray-900);
  }
}

// ===== PRINT STYLES =====
@media print {
  .inventory-header,
  .absa-fab-container,
  .card-actions {
    display: none !important;
  }

  .absa-inventory-container {
    background: white !important;
  }

  .absa-metric-card,
  .inventory-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}

// Status indicators for coins
.status-normal,
.status-in-stock {
  color: var(--absa-success);

  mat-icon {
    color: var(--absa-success);
  }
}

.status-low {
  color: var(--absa-warning);

  mat-icon {
    color: var(--absa-warning);
  }
}

.status-out-of-stock {
  color: var(--absa-red);

  mat-icon {
    color: var(--absa-red);
  }
}
